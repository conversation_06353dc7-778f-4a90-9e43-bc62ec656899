<x-layouts.app title="{{ $article->title }}">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center gap-4 mb-4">
                <flux:button href="{{ route('support.index') }}" variant="ghost" wire:navigate>
                    <x-flux::icon icon="arrow-left" class="w-4 h-4 mr-2" />
                    Voltar para Central de Ajuda
                </flux:button>
            </div>
            
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center gap-3 mb-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {{ ucfirst($article->category) }}
                        </span>
                        @if($article->is_featured)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                <x-flux::icon icon="star" class="w-4 h-4 mr-1" />
                                Destaque
                            </span>
                        @endif
                    </div>
                    
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">{{ $article->title }}</h1>
                    
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <span class="flex items-center">
                            <x-flux::icon icon="eye" class="w-4 h-4 mr-1" />
                            {{ $article->view_count }} visualizações
                        </span>
                        <span class="flex items-center">
                            <x-flux::icon icon="calendar" class="w-4 h-4 mr-1" />
                            {{ $article->created_at->format('d/m/Y') }}
                        </span>
                        @if($article->updated_at->gt($article->created_at))
                            <span class="flex items-center">
                                <x-flux::icon icon="pencil" class="w-4 h-4 mr-1" />
                                Atualizado em {{ $article->updated_at->format('d/m/Y') }}
                            </span>
                        @endif
                    </div>
                </div>
                
                @if($article->helpful_count > 0 || $article->not_helpful_count > 0)
                    <div class="text-right">
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Avaliação dos usuários</div>
                        <div class="flex items-center gap-2">
                            <div class="flex items-center text-green-600 dark:text-green-400">
                                <x-flux::icon icon="hand-thumb-up" class="w-4 h-4 mr-1" />
                                {{ $article->helpful_count }}
                            </div>
                            <div class="flex items-center text-red-600 dark:text-red-400">
                                <x-flux::icon icon="hand-thumb-down" class="w-4 h-4 mr-1" />
                                {{ $article->not_helpful_count }}
                            </div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                ({{ $article->helpful_percentage }}% útil)
                            </span>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Content -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-8">
            <div class="p-8">
                <div class="prose prose-lg max-w-none dark:prose-invert text-gray-100">
                    {!! nl2br(e($article->content)) !!}
                </div>
                
                @if($article->tags && count($article->tags) > 0)
                    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Tags:</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($article->tags as $tag)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                    {{ $tag }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Feedback Section -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Este artigo foi útil?</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Sua avaliação nos ajuda a melhorar nosso conteúdo e criar tutoriais mais úteis.
                </p>
                
                <div class="flex items-center gap-4">
                    <livewire:support.article-feedback :article="$article" />
                </div>
                
                @if($article->helpful_count > 0 || $article->not_helpful_count > 0)
                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                            <span>{{ $article->helpful_count + $article->not_helpful_count }} pessoas avaliaram este artigo</span>
                            <span>{{ $article->helpful_percentage }}% consideraram útil</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Related Articles -->
        @php
            $relatedArticles = \App\Models\HelpArticle::published()
                ->where('category', $article->category)
                ->where('id', '!=', $article->id)
                ->limit(3)
                ->get();
        @endphp
        
        @if($relatedArticles->count() > 0)
            <div class="mt-8">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Artigos Relacionados</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($relatedArticles as $related)
                        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700">
                            <div class="p-6">
                                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">
                                    <a href="{{ route('support.articles.show', $related) }}" 
                                       class="hover:text-blue-600 dark:hover:text-blue-400"
                                       wire:navigate>
                                        {{ $related->title }}
                                    </a>
                                </h4>
                                <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">
                                    {{ Str::limit(strip_tags($related->content), 100) }}
                                </p>
                                <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                                    <span>{{ $related->view_count }} visualizações</span>
                                    @if($related->helpful_count > 0 || $related->not_helpful_count > 0)
                                        <span>{{ $related->helpful_percentage }}% útil</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Help Section -->
        <div class="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-start gap-4">
                <div class="flex-shrink-0">
                    <x-flux::icon icon="question-mark-circle" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">Ainda precisa de ajuda?</h3>
                    <p class="text-blue-800 dark:text-blue-200 mb-4">
                        Se este artigo não resolveu sua dúvida, nossa equipe de suporte está pronta para ajudar!
                    </p>
                    <div class="flex gap-3">
                        <a
                            href="{{ route('support.index', ['activeTab' => 'faq']) }}"
                            wire:navigate
                            class="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-700 hover:text-blue-800 hover:bg-blue-100 dark:text-blue-300 dark:hover:text-blue-200 dark:hover:bg-blue-800/30 rounded-md transition-colors">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Ver FAQ
                        </a>
                        <a
                            href="{{ route('support.index', ['activeTab' => 'tickets']) }}"
                            wire:navigate
                            class="inline-flex items-center px-3 py-1 text-sm font-medium bg-blue-600 text-white hover:bg-blue-700 rounded-md transition-colors">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Abrir Ticket
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
