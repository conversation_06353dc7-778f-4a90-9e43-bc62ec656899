@push('scripts')
<script src="{{ asset('js/mention-hashtag-handler.js') }}" defer></script>
@endpush

<div class="p-6 border border-neutral-200 dark:border-neutral-700 shadow-md rounded-lg">
    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            {{ session('error') }}
        </div>
    @endif

    <!-- Mensagens de erro específicas -->
    @if ($errors->has('general'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            {{ $errors->first('general') }}
        </div>
    @endif

    <form wire:submit.prevent="store" enctype="multipart/form-data">
        <div x-data="mentionHashtagHandler()" class="relative">
            <textarea
                x-ref="textarea"
                x-on:input="handleInput($event)"
                x-on:keydown="handleKeydown($event)"
                class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-zinc-700 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 p-3"
                rows="3"
                placeholder="O que você está pensando?"
                wire:model="content"
            ></textarea>
            @error('content') <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> @enderror

            <!-- Sugestões de menções -->
            <div
                x-show="showMentions"
                x-transition
                :style="mentionStyle"
                class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto bg-zinc-800"
            >
                <template x-for="user in mentionSuggestions" :key="user.id">
                    <div
                        @click="selectMention(user.username)"
                        class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300 flex items-center"
                    >
                        <img :src="user.avatar" :alt="user.username" class="w-6 h-6 rounded-full mr-2">
                        <span x-text="user.name + ' (@' + user.username + ')'"></span>
                    </div>
                </template>
            </div>

            <!-- Sugestões de hashtags -->
            <div
                x-show="showHashtags"
                x-transition
                :style="hashtagStyle"
                class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto"
            >
                <template x-for="hashtag in hashtagSuggestions" :key="hashtag.id">
                    <div
                        @click="selectHashtag(hashtag.name)"
                        class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300"
                    >
                        <span x-text="'#' + hashtag.name"></span>
                    </div>
                </template>
            </div>
        </div>

        @if ($image)
            <div class="mt-2">
                <img src="{{ $image->temporaryUrl() }}"
                     class="max-w-xs h-auto rounded-lg shadow-sm"
                     alt="Preview">
            </div>
        @endif
        @error('image') <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> @enderror
        @if ($video)
            <div class="mt-2">
                <video controls class="max-w-xs h-auto rounded-lg shadow-sm">
                    <source src="{{ $video->temporaryUrl() }}" type="video/mp4">
                    Seu navegador não suporta o elemento de vídeo.
                </video>
            </div>
        @endif
        @error('video') <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> @enderror

        <div class="flex justify-between mt-3">
            <div class="flex space-x-4">
                <label for="image" class="cursor-pointer flex items-center text-gray-500">
                    <x-flux::icon icon="photo" variant="{{ $image ? 'solid' : 'outline' }}" class="w-5 h-5 mr-1" />
                    <input wire:model="image" id="image" type="file" accept="image/*" class="hidden">
                </label>
                @if ($image)
                    <span class="text-sm text-gray-500">
                        @if(is_object($image) && method_exists($image, 'getClientOriginalName'))
                            {{ $image->getClientOriginalName() }}
                        @else
                            Imagem selecionada
                        @endif
                    </span>
                @endif

                <label for="video" class="cursor-pointer flex items-center text-gray-500">
                    <x-flux::icon icon="video-camera" variant="{{ $video ? 'solid' : 'outline' }}" class="w-5 h-5 mr-1" />
                    <input wire:model="video" id="video" type="file" accept="video/*" class="hidden">
                </label>
                @if ($video)
                    <span class="text-sm text-gray-500">
                        @if(is_object($video) && method_exists($video, 'getClientOriginalName'))
                            {{ $video->getClientOriginalName() }}
                        @else
                            Vídeo selecionado
                        @endif
                    </span>
                @endif
            </div>
            <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg disabled:opacity-50"
                wire:loading.attr="disabled"
                wire:loading.class="opacity-50">
                <span wire:loading.remove>Postar</span>
                <span wire:loading>Enviando...</span>
            </button>
        </div>

        <div wire:loading wire:target="image,video" class="mt-2">
            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                <div class="bg-purple-600  h-2.5 rounded-full" style="width: 100%"></div>
            </div>
            <div class="text-sm text-gray-500 mt-1">Carregando arquivo...</div>
        </div>
    </form>
</div>
