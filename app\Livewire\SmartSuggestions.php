<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\Group;
use App\Models\Hobby;
use App\Models\City;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SmartSuggestions extends Component
{
    public $suggestions = [];
    public $suggestionType = 'all'; // all, location, interests, groups, activity
    public $showModal = false;

    public function mount()
    {
        $this->loadSuggestions();
    }

    public function loadSuggestions()
    {
        if (!Auth::check()) {
            return;
        }

        $currentUser = Auth::user();
        $suggestions = collect();

        // Sugestões baseadas em localização
        if ($this->suggestionType === 'all' || $this->suggestionType === 'location') {
            $locationSuggestions = $this->getLocationBasedSuggestions($currentUser);
            $suggestions = $suggestions->merge($locationSuggestions);
        }

        // Sugestões baseadas em interesses
        if ($this->suggestionType === 'all' || $this->suggestionType === 'interests') {
            $interestSuggestions = $this->getInterestBasedSuggestions($currentUser);
            $suggestions = $suggestions->merge($interestSuggestions);
        }

        // Sugestões baseadas em grupos
        if ($this->suggestionType === 'all' || $this->suggestionType === 'groups') {
            $groupSuggestions = $this->getGroupBasedSuggestions($currentUser);
            $suggestions = $suggestions->merge($groupSuggestions);
        }

        // Sugestões baseadas em atividade
        if ($this->suggestionType === 'all' || $this->suggestionType === 'activity') {
            $activitySuggestions = $this->getActivityBasedSuggestions($currentUser);
            $suggestions = $suggestions->merge($activitySuggestions);
        }

        // Remover duplicatas e usuários já seguidos
        $followingIds = $currentUser->following()->pluck('following_id');
        $this->suggestions = $suggestions
            ->unique('id')
            ->whereNotIn('id', $followingIds)
            ->where('id', '!=', $currentUser->id)
            ->take(12)
            ->values();
    }

    protected function getLocationBasedSuggestions($currentUser)
    {
        if (!$currentUser->city_id) {
            return collect();
        }

        return User::where('city_id', $currentUser->city_id)
            ->where('id', '!=', $currentUser->id)
            ->with(['userPhotos', 'city'])
            ->inRandomOrder()
            ->limit(4)
            ->get()
            ->map(function ($user) {
                $user->suggestion_reason = 'Mora em ' . ($user->city->name ?? 'sua cidade');
                $user->suggestion_type = 'location';
                return $user;
            });
    }

    protected function getInterestBasedSuggestions($currentUser)
    {
        $userHobbies = $currentUser->hobbies()->pluck('hobby_id');
        
        if ($userHobbies->isEmpty()) {
            return collect();
        }

        return User::whereHas('hobbies', function ($query) use ($userHobbies) {
                $query->whereIn('hobby_id', $userHobbies);
            })
            ->where('id', '!=', $currentUser->id)
            ->with(['userPhotos', 'hobbies'])
            ->withCount(['hobbies as common_hobbies_count' => function ($query) use ($userHobbies) {
                $query->whereIn('hobby_id', $userHobbies);
            }])
            ->orderBy('common_hobbies_count', 'desc')
            ->limit(4)
            ->get()
            ->map(function ($user) {
                $count = $user->common_hobbies_count;
                $user->suggestion_reason = $count . ' interesse' . ($count > 1 ? 's' : '') . ' em comum';
                $user->suggestion_type = 'interests';
                return $user;
            });
    }

    protected function getGroupBasedSuggestions($currentUser)
    {
        $userGroups = $currentUser->groups()->where('is_approved', true)->pluck('group_id');
        
        if ($userGroups->isEmpty()) {
            return collect();
        }

        return User::whereHas('groups', function ($query) use ($userGroups) {
                $query->whereIn('group_id', $userGroups)
                      ->where('is_approved', true);
            })
            ->where('id', '!=', $currentUser->id)
            ->with(['userPhotos', 'groups'])
            ->withCount(['groups as common_groups_count' => function ($query) use ($userGroups) {
                $query->whereIn('group_id', $userGroups)
                      ->where('is_approved', true);
            }])
            ->orderBy('common_groups_count', 'desc')
            ->limit(4)
            ->get()
            ->map(function ($user) {
                $count = $user->common_groups_count;
                $user->suggestion_reason = $count . ' grupo' . ($count > 1 ? 's' : '') . ' em comum';
                $user->suggestion_type = 'groups';
                return $user;
            });
    }

    protected function getActivityBasedSuggestions($currentUser)
    {
        // Usuários ativos recentemente com atividade similar
        return User::where('last_seen', '>=', now()->subDays(7))
            ->where('id', '!=', $currentUser->id)
            ->whereHas('posts', function ($query) {
                $query->where('created_at', '>=', now()->subDays(30));
            })
            ->with(['userPhotos'])
            ->withCount(['posts as recent_posts_count' => function ($query) {
                $query->where('created_at', '>=', now()->subDays(30));
            }])
            ->orderBy('last_seen', 'desc')
            ->limit(4)
            ->get()
            ->map(function ($user) {
                $user->suggestion_reason = 'Usuário ativo recentemente';
                $user->suggestion_type = 'activity';
                return $user;
            });
    }

    public function setSuggestionType($type)
    {
        $this->suggestionType = $type;
        $this->loadSuggestions();
    }

    public function followUser($userId)
    {
        $currentUser = Auth::user();
        $userToFollow = User::find($userId);
        
        if ($userToFollow && !$currentUser->isFollowing($userToFollow)) {
            $currentUser->following()->attach($userId);
            
            // Remover da lista de sugestões
            $this->suggestions = $this->suggestions->where('id', '!=', $userId);
            
            // Dispatch evento para atualizar outros componentes
            $this->dispatch('user-followed', userId: $userId);
            
            // Mostrar toast de sucesso
            $this->dispatch('toast', 
                message: 'Agora você está seguindo ' . $userToFollow->name,
                type: 'success'
            )->to('toast-notification');
        }
    }

    public function getAvatar($user)
    {
        if ($user->userPhotos && $user->userPhotos->first()) {
            return Storage::url($user->userPhotos->first()->photo_path);
        }
        return asset('images/users/avatar.svg');
    }

    public function getSuggestionIcon($type)
    {
        return match($type) {
            'location' => 'map-pin',
            'interests' => 'heart',
            'groups' => 'user-group',
            'activity' => 'bolt',
            default => 'sparkles'
        };
    }

    public function getSuggestionColor($type)
    {
        return match($type) {
            'location' => 'text-blue-500',
            'interests' => 'text-red-500',
            'groups' => 'text-green-500',
            'activity' => 'text-yellow-500',
            default => 'text-purple-500'
        };
    }

    public function render()
    {
        return view('livewire.smart-suggestions');
    }
}
