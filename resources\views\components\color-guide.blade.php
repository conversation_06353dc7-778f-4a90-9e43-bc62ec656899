<div class="p-6 bg-white dark:bg-zinc-800 rounded-lg shadow">
    <h2 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white"><PERSON><PERSON><PERSON> de Cores e Bordas</h2>

    <div class="space-y-8">
        <!-- Cores de Texto Semânticas -->
        <div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Cores de Texto Semânticas</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 bg-gray-100 dark:bg-zinc-700 rounded">
                    <p class="text-title mb-2">.text-title</p>
                    <p class="text-subtitle mb-2">.text-subtitle</p>
                    <p class="text-body mb-2">.text-body</p>
                    <p class="text-body-light mb-2">.text-body-light</p>
                    <p class="text-body-lighter mb-2">.text-body-lighter</p>
                    <p class="text-label mb-2">.text-label</p>
                    <p class="text-muted">.text-muted</p>
                </div>
                <div class="p-4 bg-gray-100 dark:bg-zinc-700 rounded">
                    <p class="text-link mb-2">.text-link (passe o mouse para ver o efeito neon)</p>
                    <p class="text-link-subtle mb-2">.text-link-subtle (passe o mouse para ver o efeito)</p>
                    <p class="text-success mb-2">.text-success</p>
                    <p class="text-warning mb-2">.text-warning</p>
                    <p class="text-danger mb-2">.text-danger</p>
                    <p class="text-info mb-2">.text-info</p>
                    <p class="text-disabled">.text-disabled (cursor desabilitado)</p>
                </div>
            </div>
        </div>
        <!-- Bordas e Contornos -->
        <div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Bordas e Contornos</h3>

            <!-- Cores de Bordas -->
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700 dark:text-gray-300">Cores de Bordas</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Bordas Zinc -->
                    <div class="space-y-2">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Zinc</h5>
                        <div class="p-3 border border-zinc-200 dark:border-zinc-700 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-zinc-200 / .dark:border-zinc-700</code>
                        </div>
                        <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-zinc-300 / .dark:border-zinc-600</code>
                        </div>
                        <div class="p-3 border border-zinc-600 dark:border-zinc-400 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-zinc-600 / .dark:border-zinc-400</code>
                        </div>
                        <div class="p-3 border border-zinc-700 dark:border-zinc-300 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-zinc-700 / .dark:border-zinc-300</code>
                        </div>
                    </div>

                    <!-- Bordas Gray -->
                    <div class="space-y-2">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Gray</h5>
                        <div class="p-3 border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-gray-200 / .dark:border-gray-700</code>
                        </div>
                        <div class="p-3 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-gray-300 / .dark:border-gray-600</code>
                        </div>
                        <div class="p-3 border border-gray-600 dark:border-gray-400 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-gray-600 / .dark:border-gray-400</code>
                        </div>
                        <div class="p-3 border border-gray-700 dark:border-gray-300 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-gray-700 / .dark:border-gray-300</code>
                        </div>
                    </div>

                    <!-- Bordas Coloridas -->
                    <div class="space-y-2">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Coloridas</h5>
                        <div class="p-3 border border-red-500 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-red-500</code>
                        </div>
                        <div class="p-3 border border-green-500 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-green-500</code>
                        </div>
                        <div class="p-3 border border-blue-500 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-blue-500</code>
                        </div>
                        <div class="p-3 border border-purple-500 rounded bg-white dark:bg-zinc-800">
                            <code class="text-xs">.border-purple-500</code>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Border Radius -->
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700 dark:text-gray-300">Border Radius</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded-none bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded-none</code>
                    </div>
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded-sm bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded-sm</code>
                    </div>
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded</code>
                    </div>
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded-md</code>
                    </div>
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded-lg bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded-lg</code>
                    </div>
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded-xl bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded-xl</code>
                    </div>
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded-2xl bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded-2xl</code>
                    </div>
                    <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded-full bg-white dark:bg-zinc-800 text-center">
                        <code class="text-xs">.rounded-full</code>
                    </div>
                </div>
            </div>

            <!-- Ring e Outline -->
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700 dark:text-gray-300">Ring e Outline</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Ring (Focus States)</h5>
                        <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800 ring-2 ring-blue-500 ring-offset-2">
                            <code class="text-xs">.ring-2 .ring-blue-500 .ring-offset-2</code>
                        </div>
                        <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800 ring-2 ring-red-500 ring-offset-2">
                            <code class="text-xs">.ring-2 .ring-red-500 .ring-offset-2</code>
                        </div>
                        <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800 ring-2 ring-green-500 ring-offset-2">
                            <code class="text-xs">.ring-2 .ring-green-500 .ring-offset-2</code>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Outline</h5>
                        <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800 outline outline-2 outline-blue-500 outline-offset-2">
                            <code class="text-xs">.outline .outline-2 .outline-blue-500</code>
                        </div>
                        <div class="p-3 border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800 outline-none">
                            <code class="text-xs">.outline-none</code>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Divide -->
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700 dark:text-gray-300">Divide (Separadores)</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Divide Y</h5>
                        <div class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-zinc-800 rounded border border-zinc-300 dark:border-zinc-600">
                            <div class="p-3"><code class="text-xs">.divide-y</code></div>
                            <div class="p-3"><code class="text-xs">.divide-gray-200</code></div>
                            <div class="p-3"><code class="text-xs">dark:divide-gray-700</code></div>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Divide X</h5>
                        <div class="flex divide-x divide-gray-200 dark:divide-gray-700 bg-white dark:bg-zinc-800 rounded border border-zinc-300 dark:border-zinc-600">
                            <div class="p-3 flex-1 text-center"><code class="text-xs">.divide-x</code></div>
                            <div class="p-3 flex-1 text-center"><code class="text-xs">gray-200</code></div>
                            <div class="p-3 flex-1 text-center"><code class="text-xs">dark:gray-700</code></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bordas Especiais do Projeto -->
            <div>
                <h4 class="text-lg font-medium mb-3 text-gray-700 dark:text-gray-300">Bordas Especiais do Projeto</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Night Board</h5>
                        <div class="p-3 rounded bg-white dark:bg-zinc-800" style="border: 2px solid #E60073;">
                            <code class="text-xs">border: 2px solid #E60073 (Rosa Neon)</code>
                        </div>
                        <div class="p-3 rounded bg-white dark:bg-zinc-800" style="border: 2px solid #00FFF7;">
                            <code class="text-xs">border: 2px solid #00FFF7 (Ciano Neon)</code>
                        </div>
                        <div class="p-3 rounded bg-white dark:bg-zinc-800" style="border: 2px solid #FFE600;">
                            <code class="text-xs">border: 2px solid #FFE600 (Amarelo Neon)</code>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400">Tooltips</h5>
                        <div class="p-3 border border-gray-600 rounded-lg bg-black text-white">
                            <code class="text-xs">.border-gray-600 (Tooltip Style)</code>
                        </div>
                        <div class="space-y-2">
                            <h6 class="text-xs font-medium text-gray-600 dark:text-gray-400">Toast Notifications</h6>
                            <div class="p-2 border border-green-700 rounded bg-zinc-800 text-white">
                                <code class="text-xs">.border-green-700 (Success)</code>
                            </div>
                            <div class="p-2 border border-red-700 rounded bg-zinc-800 text-white">
                                <code class="text-xs">.border-red-700 (Error)</code>
                            </div>
                            <div class="p-2 border border-purple-700 rounded bg-zinc-800 text-white">
                                <code class="text-xs">.border-purple-700 (Message)</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cores para Preços -->
        <div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Cores para Preços</h3>
            <div class="p-4 bg-gray-100 dark:bg-zinc-700 rounded">
                <p class="text-price mb-2">.text-price</p>
                <p class="text-price-discount mb-2">.text-price-discount</p>
                <p class="text-price-old">.text-price-old</p>
            </div>
        </div>

        <!-- Efeitos Neon para Texto -->
        <div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Efeitos Neon para Texto</h3>
            <div class="p-4 bg-gray-100 dark:bg-zinc-700 rounded">
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">Passe o mouse sobre os textos abaixo para ver o efeito neon:</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                    <a href="#" class="neon-text neon-text-red p-2 text-center">.neon-text-red</a>
                    <a href="#" class="neon-text neon-text-green p-2 text-center">.neon-text-green</a>
                    <a href="#" class="neon-text neon-text-blue p-2 text-center">.neon-text-blue</a>
                    <a href="#" class="neon-text neon-text-purple p-2 text-center">.neon-text-purple</a>
                    <a href="#" class="neon-text neon-text-pink p-2 text-center">.neon-text-pink</a>
                </div>
            </div>
        </div>

        <!-- Efeitos Neon para Divs -->
        <div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Efeitos Neon para Divs</h3>
            <div class="p-4 bg-gray-100 dark:bg-zinc-700 rounded">
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">Passe o mouse sobre as caixas abaixo para ver o efeito neon:</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                    <div class="neon-box neon-box-red p-3 bg-white dark:bg-zinc-800 rounded text-center">.neon-box-red</div>
                    <div class="neon-box neon-box-green p-3 bg-white dark:bg-zinc-800 rounded text-center">.neon-box-green</div>
                    <div class="neon-box neon-box-blue p-3 bg-white dark:bg-zinc-800 rounded text-center">.neon-box-blue</div>
                    <div class="neon-box neon-box-purple p-3 bg-white dark:bg-zinc-800 rounded text-center">.neon-box-purple</div>
                    <div class="neon-box neon-box-pink p-3 bg-white dark:bg-zinc-800 rounded text-center">.neon-box-pink</div>
                </div>
            </div>
        </div>

        <!-- Cores de Texto para Fundos Zinc -->
        <div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Cores de Texto para Fundos Zinc</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <div class="p-3 bg-zinc-50 rounded"><span class="text-auto">.bg-zinc-50 .text-auto</span></div>
                    <div class="p-3 bg-zinc-100 rounded"><span class="text-auto">.bg-zinc-100 .text-auto</span></div>
                    <div class="p-3 bg-zinc-200 rounded"><span class="text-auto">.bg-zinc-200 .text-auto</span></div>
                    <div class="p-3 bg-zinc-300 rounded"><span class="text-auto">.bg-zinc-300 .text-auto</span></div>
                    <div class="p-3 bg-zinc-400 rounded"><span class="text-auto">.bg-zinc-400 .text-auto</span></div>
                </div>
                <div class="space-y-2">
                    <div class="p-3 bg-zinc-500 rounded"><span class="text-auto">.bg-zinc-500 .text-auto</span></div>
                    <div class="p-3 bg-zinc-600 rounded"><span class="text-auto">.bg-zinc-600 .text-auto</span></div>
                    <div class="p-3 bg-zinc-700 rounded"><span class="text-auto">.bg-zinc-700 .text-auto</span></div>
                    <div class="p-3 bg-zinc-800 rounded"><span class="text-auto">.bg-zinc-800 .text-auto</span></div>
                    <div class="p-3 bg-zinc-900 rounded"><span class="text-auto">.bg-zinc-900 .text-auto</span></div>
                </div>
            </div>
        </div>

        <!-- Exemplos de Uso -->
        <div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Exemplos de Uso</h3>
            <div class="p-4 bg-gray-100 dark:bg-zinc-700 rounded">
                <h4 class="text-title text-xl font-bold mb-2">Título da Seção</h4>
                <h5 class="text-subtitle text-lg font-semibold mb-2">Subtítulo da Seção</h5>
                <p class="text-body mb-4">Este é um texto de corpo principal, usado para a maioria dos conteúdos da página. Ele tem boa legibilidade e contraste adequado tanto no tema claro quanto no escuro.</p>
                <p class="text-body-light mb-4">Este é um texto de corpo mais leve, usado para informações secundárias ou menos importantes.</p>
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-label">Etiqueta:</span>
                    <span class="text-body">Valor da etiqueta</span>
                </div>
                <p class="text-muted mb-4">Este é um texto muted, usado para informações auxiliares ou menos relevantes.</p>
                <div class="mb-4">
                    <a href="#" class="text-link">Este é um link principal (passe o mouse para ver o efeito neon)</a>
                </div>
                <div class="mb-4">
                    <a href="#" class="text-link-subtle">Este é um link sutil (passe o mouse para ver o efeito)</a>
                </div>
                <div class="space-y-2 mb-4">
                    <p class="text-success">Mensagem de sucesso</p>
                    <p class="text-warning">Mensagem de aviso</p>
                    <p class="text-danger">Mensagem de erro</p>
                    <p class="text-info">Mensagem informativa</p>
                    <p class="text-disabled">Texto desabilitado (cursor não permitido)</p>
                </div>
                <div>
                    <p class="text-price mb-1">R$ 99,90</p>
                    <p class="text-price-discount mb-1">R$ 79,90</p>
                    <p class="text-price-old">R$ 129,90</p>
                </div>

                <!-- Exemplo com Div Neon -->
                <div class="mt-6 neon-box neon-box-red p-4 bg-white dark:bg-zinc-800 rounded">
                    <h5 class="text-subtitle mb-2">Exemplo de Card com Efeito Neon</h5>
                    <p class="text-body mb-2">Este card tem um efeito neon vermelho quando você passa o mouse sobre ele.</p>
                    <a href="#" class="neon-text neon-text-red">Saiba mais</a>
                </div>
            </div>
        </div>
    </div>
</div>
