<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminPasswordResetCopy extends Notification
{
    use Queueable;

    protected $data;

    /**
     * Create a new notification instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('[ADMIN COPY] Solicitação de Reset de Senha - ' . config('app.name'))
            ->view('emails.admin-password-reset-copy', [
                'user' => $this->data['user'],
                'resetUrl' => $this->data['resetUrl'],
                'adminRecipient' => $notifiable,
            ]);
    }
}
