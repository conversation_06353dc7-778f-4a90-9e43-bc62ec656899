<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CharmReceived extends Notification
{
    use Queueable;

    protected $payment;
    protected $sender;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
        $this->sender = $payment->sender;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $charmTypes = [
            'flower' => 'Flor',
            'kiss' => 'Beijo',
            'drink' => 'Drink',
            'ticket' => 'Ingresso'
        ];

        $charmName = $charmTypes[$this->payment->charm_type] ?? $this->payment->charm_type;

        return (new MailMessage)
            ->subject("Você recebeu um {$charmName} de {$this->sender->name}")
            ->view('vendor.mail.html.charm-received', [
                'user' => $notifiable,
                'sender' => $this->sender,
                'payment' => $this->payment,
                'charmName' => $charmName,
            ]);
    }
} 