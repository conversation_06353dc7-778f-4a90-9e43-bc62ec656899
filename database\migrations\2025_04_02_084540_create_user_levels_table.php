<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        // Renomear a tabela existente se ela já existir e tiver sido criada
        if (Schema::hasTable('user_levels')) {
            Schema::rename('user_levels', 'levels');
        } else {
            // C<PERSON>o contrário, criar a nova tabela com o nome correto
            Schema::create('levels', function (Blueprint $table) {
                $table->id();
                $table->integer('level')->unique(); // Nível numérico
                $table->integer('min_points')->default(0); // Pontos mínimos para este nível
                $table->json('permissions')->nullable(); // Permissões associadas a este nível
                $table->timestamps();
            });
        }

        // Se a tabela original user_levels existia e tinha a coluna user_id, removê-la após renomear
        if (Schema::hasColumn('levels', 'user_id')) {
            Schema::table('levels', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
                $table->dropColumn('user_id');
            });
        }
    }
    
    public function down(): void {
        Schema::dropIfExists('levels');
        // Opcional: renomear de volta para user_levels em um rollback completo, se necessário
        // Schema::rename('levels', 'user_levels');
    }
};
