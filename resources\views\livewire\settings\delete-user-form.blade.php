<?php

use App\Livewire\Actions\Logout;
use Illuminate\Support\Facades\Auth;
use Livewire\Volt\Component;

new class extends Component {
    public string $password = '';
    public bool $showDeleteModal = false;

    /**
     * Show the delete confirmation modal.
     */
    public function confirmDelete(): void
    {
        $this->showDeleteModal = true;
    }

    /**
     * Cancel the delete operation.
     */
    public function cancelDelete(): void
    {
        $this->showDeleteModal = false;
        $this->password = '';
    }

    /**
     * Delete the currently authenticated user.
     */
    public function deleteUser(Logout $logout): void
    {
        $this->validate([
            'password' => ['required', 'string', 'current_password'],
        ]);

        tap(Auth::user(), $logout(...))->delete();

        $this->redirect('/', navigate: true);
    }
}; ?>

<section class="mt-10 space-y-6">
    <div class="relative mb-5">
        <flux:heading>{{ __('Deletar conta') }}</flux:heading>
        <flux:subheading>{{ __('Deletar sua conta e todos os seus dados') }}</flux:subheading>
    </div>

    <flux:button variant="danger" wire:click="confirmDelete">
        {{ __('Deletar conta') }}
    </flux:button>

    <flux:modal wire:model="showDeleteModal" focusable class="max-w-lg">
        <form wire:submit="deleteUser" class="space-y-6">
            <div>
                <flux:heading>{{ __('Tem certeza que deseja deletar sua conta?') }}</flux:heading>

                <flux:subheading>
                    {{ __('Uma vez deletada sua conta e seus dados não poderão mais ser recuperados.') }}
                </flux:subheading>
            </div>

            <flux:input wire:model="password" :label="__('Password')" type="password" />

            <div class="flex justify-end space-x-2">
                <flux:button variant="filled" wire:click="cancelDelete">
                    {{ __('Cancelar') }}
                </flux:button>

                <flux:button variant="danger" type="submit">
                    {{ __('Deletar Conta') }}
                </flux:button>
            </div>
        </form>
    </flux:modal>
</section>
