<?php

namespace Database\Factories;

use App\Models\Event;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Event::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->sentence(3);
        $date = $this->faker->dateTimeBetween('+1 week', '+3 months');
        
        return [
            'name' => $name,
            'slug' => Str::slug($name) . '-' . $this->faker->unique()->randomNumber(4),
            'description' => $this->faker->paragraphs(3, true),
            'date' => $date->format('Y-m-d'),
            'start_time' => $date->format('Y-m-d H:i:s'),
            'end_time' => $date->modify('+3 hours')->format('Y-m-d H:i:s'),
            'image' => null,
            'cover_image' => null,
            'price' => $this->faker->randomFloat(2, 0, 200),
            'capacity' => $this->faker->numberBetween(10, 500),
            'location' => $this->faker->address,
            'address' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'zip_code' => $this->faker->postcode,
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'is_active' => true,
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the event is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => 0,
        ]);
    }

    /**
     * Indicate that the event is paid.
     */
    public function paid(float $price = null): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $price ?? $this->faker->randomFloat(2, 10, 200),
        ]);
    }

    /**
     * Indicate that the event is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the event is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the event is in the past.
     */
    public function past(): static
    {
        $date = $this->faker->dateTimeBetween('-3 months', '-1 week');
        
        return $this->state(fn (array $attributes) => [
            'date' => $date->format('Y-m-d'),
            'start_time' => $date->format('Y-m-d H:i:s'),
            'end_time' => $date->modify('+3 hours')->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Indicate that the event has limited capacity.
     */
    public function limitedCapacity(int $capacity = 10): static
    {
        return $this->state(fn (array $attributes) => [
            'capacity' => $capacity,
        ]);
    }
}
