<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NightBoardPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'board',
        'is_public',
        'background_image',
        'background_position',
    ];

    protected $casts = [
        'board' => 'array',
        'is_public' => 'boolean',
    ];

    /**
     * Relacionamento com o usuário criador do plano
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relacionamento com os patrocínios
     */
    public function supports(): HasMany
    {
        return $this->hasMany(NightBoardSupport::class, 'plan_id');
    }

    /**
     * Calcula o total de patrocínios recebidos
     */
    public function getTotalSupportAmountAttribute(): float
    {
        return $this->supports()->sum('amount');
    }

    /**
     * Verifica se uma posição específica tem patrocínio
     */
    public function hasSupportAtPosition(string $position): bool
    {
        return $this->supports()->where('position', $position)->exists();
    }

    /**
     * Obtém o patrocínio de uma posição específica
     */
    public function getSupportAtPosition(string $position): ?NightBoardSupport
    {
        return $this->supports()->where('position', $position)->first();
    }

    /**
     * Scope para planos públicos
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope para planos mais patrocinados
     */
    public function scopeMostSupported($query)
    {
        return $query->withSum('supports', 'amount')
                    ->orderByDesc('supports_sum_amount');
    }
}
