<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('live_streams', function (Blueprint $table) {
            $table->string('video_path')->nullable()->after('settings');
            $table->integer('duration')->nullable()->after('video_path'); // duração em segundos
            $table->boolean('has_replay')->default(false)->after('duration');
        });
    }

    public function down()
    {
        Schema::table('live_streams', function (Blueprint $table) {
            $table->dropColumn(['video_path', 'duration', 'has_replay']);
        });
    }
};
