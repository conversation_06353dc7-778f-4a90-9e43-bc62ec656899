# Funcionalidade "Esqueceu a Senha"

## 📋 Visão Geral

A funcionalidade "Esqueceu a Senha" foi implementada com sucesso no projeto, se<PERSON><PERSON> as melhores práticas do Laravel 12, Livewire 3 e Flux UI. O sistema permite que usuários redefinam suas senhas de forma segura através de links enviados por email.

## ✨ Características Implementadas

### 🔐 Segurança
- **Tokens seguros**: Links de redefinição expiram em 60 minutos
- **Throttling**: Proteção contra spam (60 segundos entre solicitações)
- **Validação robusta**: Verificação de email, senha e confirmação
- **Criptografia**: Senhas são hasheadas com bcrypt

### 📧 Sistema de Email
- **Templates personalizados**: Emails com design profissional
- **Cópia para administradores**: Notificação automática para admins
- **SMTP configurado**: Integração com smtp.kinghost.net
- **Fallback gracioso**: Mensagens de sucesso mesmo se email falhar

### 🎨 Interface do Usuário
- **Design responsivo**: Compatível com dispositivos móveis
- **Feedback visual**: Indicadores de carregamento e status
- **Validação em tempo real**: Mensagens de erro claras
- **Acessibilidade**: Componentes Flux UI otimizados

## 🚀 Como Usar

### Para Usuários

1. **Acessar página de login**: `/login`
2. **Clicar em "Esqueceu a senha?"**
3. **Inserir email**: Digite o email cadastrado
4. **Verificar email**: Clique no link recebido
5. **Definir nova senha**: Digite e confirme a nova senha

### Para Desenvolvedores

#### Rotas Disponíveis
```php
GET  /forgot-password     -> Formulário de solicitação
POST /forgot-password     -> Envio do link
GET  /reset-password/{token} -> Formulário de redefinição
POST /reset-password/{token} -> Processamento da nova senha
```

#### Testando a Funcionalidade
```bash
# Criar usuário de teste
php artisan db:seed --class=TestUserSeeder

# Testar envio de email (substitua pelo email real)
php artisan test:password-reset <EMAIL>

# Executar testes automatizados
php artisan test tests/Feature/Auth/PasswordResetUITest.php
```

## 📁 Arquivos Criados/Modificados

### Notificações
- `app/Notifications/CustomResetPassword.php` - Notificação principal
- `app/Notifications/AdminPasswordResetCopy.php` - Cópia para admins

### Templates de Email
- `resources/views/emails/reset-password.blade.php` - Email do usuário
- `resources/views/emails/admin-password-reset-copy.blade.php` - Email dos admins

### Componentes Livewire
- `resources/views/livewire/auth/forgot-password.blade.php` - Melhorado
- `resources/views/livewire/auth/reset-password.blade.php` - Melhorado

### Traduções
- `lang/pt_BR/passwords.php` - Mensagens em português

### Testes
- `tests/Feature/Auth/PasswordResetUITest.php` - Testes de interface
- `tests/Feature/Auth/CustomPasswordResetTest.php` - Testes de notificação

### Comandos e Seeders
- `app/Console/Commands/TestPasswordReset.php` - Comando de teste
- `database/seeders/TestUserSeeder.php` - Usuários de teste

### Modelo
- `app/Models/User.php` - Método `sendPasswordResetNotification()` adicionado

## 🔧 Configuração

### Variáveis de Ambiente
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.kinghost.net
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="GodTei45!cva"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
MAIL_ENCRYPTION=tls
```

### Configuração de Autenticação
```php
// config/auth.php
'passwords' => [
    'users' => [
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60, // minutos
        'throttle' => 60, // segundos
    ],
],
```

## 🎯 Fluxo Completo

1. **Usuário solicita reset** → Formulário `/forgot-password`
2. **Sistema valida email** → Verifica se existe no banco
3. **Token é gerado** → Armazenado na tabela `password_reset_tokens`
4. **Email é enviado** → Para usuário e cópia para admins
5. **Usuário clica no link** → Acessa `/reset-password/{token}`
6. **Nova senha é definida** → Validação e atualização
7. **Usuário é redirecionado** → Para página de login com sucesso

## 🛡️ Segurança

### Medidas Implementadas
- **Expiração de tokens**: 60 minutos
- **Rate limiting**: 1 solicitação por minuto por IP
- **Validação de entrada**: Email, senha e confirmação
- **Hash seguro**: bcrypt para senhas
- **Logs de auditoria**: Cópias para administradores

### Boas Práticas
- Tokens são únicos e criptograficamente seguros
- Emails não revelam se a conta existe (proteção contra enumeração)
- Links expiram automaticamente
- Senhas antigas são invalidadas após reset

## 🚨 Troubleshooting

### Problemas Comuns

**Email não chega**
- Verificar configurações SMTP
- Checar logs do Laravel (`storage/logs/laravel.log`)
- Confirmar que o domínio aceita emails

**Erro de token inválido**
- Token pode ter expirado (60 minutos)
- Verificar se a tabela `password_reset_tokens` existe
- Confirmar que o email está correto

**Problemas de validação**
- Senha deve ter pelo menos 8 caracteres
- Confirmação deve ser idêntica à senha
- Email deve ser válido e existir no sistema

## 📞 Suporte

Para problemas ou dúvidas:
- Verificar logs em `storage/logs/laravel.log`
- Executar `php test_password_reset.php` para diagnóstico
- Contatar administrador do sistema

---

**Status**: ✅ Implementado e Testado  
**Versão**: 1.0  
**Data**: 29/05/2025
