<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('hashtags')) {
            Schema::create('hashtags', function (Blueprint $table) {
                $table->id();
                $table->string('name')->unique(); // Stores the hashtag text, e.g., "laravel"
                $table->string('slug')->unique()->nullable(); // Stores the URL-friendly slug
                $table->unsignedInteger('posts_count')->default(0); // Counter for posts using this hashtag
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hashtags');
    }
};
