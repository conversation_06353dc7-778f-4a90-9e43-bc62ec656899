<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Album extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'cover_image',
        'privacy',
        'is_default',
        'sort_order',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Relacionamento com o usuário
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relacionamento com as mídias do álbum
     */
    public function medias(): HasMany
    {
        return $this->hasMany(AlbumMedia::class)->orderBy('sort_order');
    }

    /**
     * Apenas fotos do álbum
     */
    public function photos(): HasMany
    {
        return $this->hasMany(AlbumMedia::class)->where('type', 'photo')->orderBy('sort_order');
    }

    /**
     * Apenas vídeos do álbum
     */
    public function videos(): HasMany
    {
        return $this->hasMany(AlbumMedia::class)->where('type', 'video')->orderBy('sort_order');
    }

    /**
     * Contagem de mídias
     */
    public function getMediaCountAttribute()
    {
        return $this->medias()->count();
    }

    /**
     * Contagem de fotos
     */
    public function getPhotosCountAttribute()
    {
        return $this->photos()->count();
    }

    /**
     * Contagem de vídeos
     */
    public function getVideosCountAttribute()
    {
        return $this->videos()->count();
    }

    /**
     * Primeira mídia para usar como capa
     */
    public function getFirstMediaAttribute()
    {
        return $this->medias()->first();
    }

    /**
     * URL da capa do álbum
     */
    public function getCoverUrlAttribute()
    {
        if ($this->cover_image) {
            return \Storage::url($this->cover_image);
        }

        $firstMedia = $this->first_media;
        if ($firstMedia && $firstMedia->type === 'photo') {
            return \Storage::url($firstMedia->file_path);
        }

        return asset('images/default-album-cover.jpg');
    }

    /**
     * Verifica se o álbum é público
     */
    public function isPublic(): bool
    {
        return $this->privacy === 'public';
    }

    /**
     * Verifica se o álbum é privado
     */
    public function isPrivate(): bool
    {
        return $this->privacy === 'private';
    }

    /**
     * Verifica se o álbum é apenas para amigos
     */
    public function isFriendsOnly(): bool
    {
        return $this->privacy === 'friends';
    }

    /**
     * Scope para álbuns públicos
     */
    public function scopePublic($query)
    {
        return $query->where('privacy', 'public');
    }

    /**
     * Scope para álbuns do usuário
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope para álbuns padrão
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Boot method para criar álbum padrão
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($album) {
            // Adicionar pontos por criar álbum
            \App\Models\UserPoint::addPoints(
                $album->user_id,
                'album_created',
                10,
                "Criou o álbum: {$album->name}",
                $album->id,
                Album::class
            );
        });
    }
}
