<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\VipSubscription;
use App\Services\VipSubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;
use Carbon\Carbon;

class VipSubscriptionController extends Controller
{
    protected $vipSubscriptionService;

    public function __construct(VipSubscriptionService $vipSubscriptionService)
    {
        $this->vipSubscriptionService = $vipSubscriptionService;
    }

    /**
     * Create a checkout session for VIP subscription
     */
    public function createCheckoutSession(Request $request)
    {
        $validated = $request->validate([
            'plan' => 'required|string|in:30,60,90,180,360',
            'price' => 'required|numeric',
        ]);

        $user = Auth::user();
        $planDays = (int) $validated['plan'];
        $price = (float) $validated['price'];

        // Create a new VIP subscription record with pending status
        $subscription = VipSubscription::create([
            'user_id' => $user->id,
            'plan_days' => $planDays,
            'amount' => $price,
            'status' => 'pending',
        ]);

        try {
            // Log the attempt
            logger()->info('Iniciando criação de sessão de checkout VIP', [
                'user_id' => $user->id,
                'plan_days' => $planDays,
                'price' => $price,
                'subscription_id' => $subscription->id
            ]);

            Stripe::setApiKey(config('cashier.secret'));

            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'brl',
                        'product_data' => [
                            'name' => "Assinatura VIP {$planDays} dias",
                            'description' => "Assinatura VIP por {$planDays} dias",
                        ],
                        'unit_amount' => (int)($price * 100), // Convert to cents
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => route('vip.payment.success', ['subscription' => $subscription->id]),
                'cancel_url' => route('vip.payment.cancel', ['subscription' => $subscription->id]),
                'customer_email' => $user->email,
                'metadata' => [
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'plan_days' => $planDays,
                ],
            ]);

            // Update subscription with session ID
            $subscription->update([
                'stripe_session_id' => $session->id,
            ]);

            logger()->info('Sessão de checkout VIP criada com sucesso', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'session_id' => $session->id,
                'session_url' => $session->url
            ]);

            return redirect($session->url);

        } catch (ApiErrorException $e) {
            // Delete the subscription if payment creation fails
            $subscription->delete();

            logger()->error('Erro na API do Stripe ao criar sessão VIP', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'stripe_code' => $e->getStripeCode() ?? null,
                'decline_code' => method_exists($e, 'getDeclineCode') ? $e->getDeclineCode() : null,
                'trace' => $e->getTraceAsString()
            ]);

            // Mensagem de erro mais específica baseada no tipo de erro
            $errorMessage = $this->getStripeErrorMessage($e);

            return redirect()->route('renovar-vip')
                ->with('error', $errorMessage);
        } catch (\Exception $e) {
            // Delete the subscription if payment creation fails
            $subscription->delete();

            logger()->error('Erro geral ao criar sessão de checkout VIP', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('renovar-vip')
                ->with('error', 'Ocorreu um erro interno ao processar o pagamento. Por favor, tente novamente em alguns minutos.');
        }
    }

    /**
     * Handle successful payment
     */
    public function paymentSuccess(Request $request, VipSubscription $subscription)
    {
        // Verify that the subscription belongs to the current user
        if ($subscription->user_id != Auth::id()) {
            return redirect()->route('renovar-vip')
                ->with('error', 'Assinatura inválida.');
        }

        // Process the successful payment
        $this->vipSubscriptionService->activateSubscription($subscription);

        return redirect()->route('renovar-vip')
            ->with('success', 'Pagamento realizado com sucesso! Sua assinatura VIP foi ativada.');
    }

    /**
     * Handle cancelled payment
     */
    public function paymentCancel(Request $request, VipSubscription $subscription)
    {
        // Verify that the subscription belongs to the current user
        if ($subscription->user_id != Auth::id()) {
            return redirect()->route('renovar-vip')
                ->with('error', 'Assinatura inválida.');
        }

        // Mark the subscription as cancelled
        $subscription->update([
            'status' => 'cancelled',
        ]);

        return redirect()->route('renovar-vip')
            ->with('info', 'Pagamento cancelado. Você pode tentar novamente a qualquer momento.');
    }

    /**
     * Get user-friendly error message from Stripe exception
     */
    private function getStripeErrorMessage(ApiErrorException $e): string
    {
        $stripeCode = $e->getStripeCode();
        $message = $e->getMessage();

        switch ($stripeCode) {
            case 'card_declined':
                return 'Cartão recusado. Verifique os dados ou use outro cartão.';
            case 'expired_card':
                return 'Cartão expirado. Use um cartão válido.';
            case 'incorrect_cvc':
                return 'Código de segurança incorreto.';
            case 'incorrect_number':
                return 'Número do cartão inválido.';
            case 'invalid_expiry_month':
            case 'invalid_expiry_year':
                return 'Data de validade inválida.';
            case 'insufficient_funds':
                return 'Cartão recusado por fundos insuficientes.';
            case 'processing_error':
                return 'Erro no processamento do pagamento. Tente novamente em alguns minutos.';
            case 'rate_limit':
                return 'Muitas tentativas. Aguarde alguns minutos antes de tentar novamente.';
            case 'api_key_expired':
            case 'invalid_api_key':
                return 'Erro de configuração do sistema de pagamento. Entre em contato com o suporte.';
            default:
                // Para outros erros, verificar se contém palavras-chave específicas
                if (str_contains(strtolower($message), 'network') || str_contains(strtolower($message), 'connection')) {
                    return 'Erro de conexão. Verifique sua internet e tente novamente.';
                } elseif (str_contains(strtolower($message), 'timeout')) {
                    return 'Tempo limite excedido. Tente novamente.';
                } elseif (str_contains(strtolower($message), 'invalid')) {
                    return 'Dados inválidos fornecidos. Verifique as informações e tente novamente.';
                }

                return 'Ocorreu um erro ao processar o pagamento. Por favor, tente novamente.';
        }
    }
}
