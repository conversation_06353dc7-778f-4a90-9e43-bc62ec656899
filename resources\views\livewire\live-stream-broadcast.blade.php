<div class="min-h-screen bg-black text-white">
    @if(!$liveStream)
        <!-- Criar <PERSON> Live -->
        <div class="flex items-center justify-center min-h-screen">
            <div class="text-center">
                <x-flux::icon name="video-camera" class="w-24 h-24 mx-auto mb-6 text-purple-500" />
                <h1 class="text-3xl font-bold mb-4">Iniciar Live Stream</h1>
                <p class="text-gray-400 mb-8">Compartilhe momentos especiais com seus seguidores</p>
                <flux:button wire:click="openCreateModal" variant="primary" size="base">
                    <x-flux::icon name="plus" class="w-5 h-5 mr-2" />
                    Criar Live
                </flux:button>
            </div>
        </div>
    @else
        <!-- Interface da Live -->
        <div class="flex h-screen">
            <!-- Área do Vídeo -->
            <div class="flex-1 flex flex-col">
                <!-- Header da Live -->
                <div class="bg-zinc-900 p-4 flex justify-between items-center">
                    <div>
                        <h1 class="text-xl font-bold">{{ $title }}</h1>
                        <p class="text-gray-400">{{ $description }}</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <div class="flex items-center gap-2">
                            <x-flux::icon name="eye" class="w-5 h-5 text-purple-500" />
                            <span>{{ $viewers }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <x-flux::icon name="currency-dollar" class="w-5 h-5 text-green-500" />
                            <span>R$ {{ number_format($totalDonations, 2, ',', '.') }}</span>
                        </div>
                        @if($isLive)
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                <span class="text-red-500 font-bold">AO VIVO</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Área do Vídeo -->
                <div class="flex-1 bg-black relative">
                    <div id="video-container" class="w-full h-full flex items-center justify-center">
                        @if(!$isLive)
                            <div class="text-center">
                                <x-flux::icon name="video-camera-slash" class="w-16 h-16 mx-auto mb-4 text-gray-500" />
                                <p class="text-gray-400 mb-4">Câmera desconectada</p>
                                <div class="space-y-3">
                                    <flux:button onclick="window.startCameraSimple()" variant="outline">
                                        <x-flux::icon name="video-camera" class="w-5 h-5 mr-2" />
                                        Testar Câmera Simples
                                    </flux:button>
                                    <flux:button onclick="window.testWebRTCConnection()" variant="outline">
                                        <x-flux::icon name="signal" class="w-5 h-5 mr-2" />
                                        Testar WebRTC
                                    </flux:button>
                                    <flux:button wire:click="startLive" variant="primary">
                                        <x-flux::icon name="play" class="w-5 h-5 mr-2" />
                                        Iniciar Live
                                    </flux:button>
                                </div>
                            </div>
                        @else
                            <video id="localVideo" autoplay muted playsinline class="w-full h-full object-cover bg-black"></video>
                            <div id="camera-status" class="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm" style="display: none;">
                                Conectando câmera...
                            </div>

                            <!-- Data attribute para stream ID -->
                            <div data-stream-id="live-{{ $liveStream->id }}" style="display: none;"></div>
                            <!-- Debug info -->
                            <div class="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
                                <div id="debug-info">Debug: Live ativa, aguardando câmera...</div>
                            </div>
                            <!-- Auto-start script -->
                            <script>
                                // Tentar iniciar câmera automaticamente quando o elemento for criado
                                document.addEventListener('DOMContentLoaded', function() {
                                    console.log('DOM carregado, tentando iniciar câmera automaticamente...');
                                    setTimeout(() => {
                                        if (typeof window.startCameraSimple === 'function') {
                                            console.log('Iniciando câmera simples automaticamente...');
                                            window.startCameraSimple();
                                        } else {
                                            console.log('Função startCameraSimple não encontrada, tentando carregar...');
                                            // Tentar novamente após um delay
                                            setTimeout(() => {
                                                if (typeof window.startCameraSimple === 'function') {
                                                    window.startCameraSimple();
                                                }
                                            }, 2000);
                                        }
                                    }, 1000);
                                });
                            </script>
                        @endif
                    </div>

                    <!-- Controles da Live -->
                    @if($isLive)
                        <div class="absolute bottom-4 left-4 right-4 flex justify-center">
                            <div class="bg-black bg-opacity-50 rounded-lg p-4 flex gap-4">
                                <flux:button onclick="window.startCameraSimple()" variant="primary" size="sm">
                                    <x-flux::icon name="video-camera" class="w-5 h-5 mr-2" />
                                    Câmera Simples
                                </flux:button>

                                <flux:button onclick="window.startCameraAdvanced()" variant="outline" size="sm">
                                    <x-flux::icon name="signal" class="w-5 h-5 mr-2" />
                                    WebRTC
                                </flux:button>

                                <flux:button onclick="window.testWebRTCConnection()" variant="ghost" size="sm">
                                    <x-flux::icon name="wifi" class="w-5 h-5 mr-2" />
                                    Testar
                                </flux:button>
                                <flux:button id="toggleCamera" variant="ghost" size="sm">
                                    <x-flux::icon name="video-camera" class="w-5 h-5" />
                                </flux:button>
                                <flux:button id="toggleMicrophone" variant="ghost" size="sm">
                                    <x-flux::icon name="microphone" class="w-5 h-5" />
                                </flux:button>

                                <!-- Controles de Gravação -->
                                <flux:button id="startRecording" variant="outline" size="sm" style="display: none;">
                                    <x-flux::icon name="play" class="w-5 h-5 mr-2" />
                                    Gravar
                                </flux:button>
                                <flux:button id="stopRecording" variant="danger" size="sm" style="display: none;">
                                    <x-flux::icon name="stop" class="w-5 h-5 mr-2" />
                                    Parar Gravação
                                </flux:button>

                                <flux:button wire:click="openEndModal" variant="danger" size="sm">
                                    <x-flux::icon name="stop" class="w-5 h-5 mr-2" />
                                    Encerrar Live
                                </flux:button>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Chat da Live -->
            <div class="w-80 bg-zinc-900 flex flex-col">
                <!-- Header do Chat -->
                <div class="p-4 border-b border-zinc-700">
                    <h3 class="font-bold">Chat da Live</h3>
                </div>

                <!-- Mensagens -->
                <div class="flex-1 overflow-y-auto p-4 space-y-3" id="chat-messages">
                    @foreach($messages as $message)
                        <div class="flex gap-3">
                            @if($message['type'] === 'system')
                                <div class="w-full text-center">
                                    <span class="text-yellow-400 text-sm">{{ $message['message'] }}</span>
                                </div>
                            @elseif($message['type'] === 'donation')
                                <div class="w-full bg-purple-900 bg-opacity-50 rounded-lg p-3">
                                    <div class="flex items-center gap-2 mb-1">
                                        <img src="{{ $message['user']['user_photos'][0]['photo_path'] ?? asset('images/default-avatar.jpg') }}" 
                                             class="w-6 h-6 rounded-full">
                                        <span class="font-bold text-purple-300">{{ $message['user']['name'] }}</span>
                                        <x-flux::icon name="heart" class="w-4 h-4 text-red-500" />
                                    </div>
                                    <p class="text-sm">{{ $message['message'] }}</p>
                                    <p class="text-xs text-purple-400 mt-1">
                                        Enviou {{ $message['data']['charm_name'] ?? 'um presente' }} 
                                        (R$ {{ number_format($message['data']['amount'] ?? 0, 2, ',', '.') }})
                                    </p>
                                </div>
                            @else
                                <img src="{{ $message['user']['user_photos'][0]['photo_path'] ?? asset('images/default-avatar.jpg') }}" 
                                     class="w-8 h-8 rounded-full">
                                <div class="flex-1">
                                    <div class="flex items-center gap-2">
                                        <span class="font-bold text-sm">{{ $message['user']['name'] }}</span>
                                        <span class="text-xs text-gray-400">
                                            {{ \Carbon\Carbon::parse($message['created_at'])->format('H:i') }}
                                        </span>
                                    </div>
                                    <p class="text-sm">{{ $message['message'] }}</p>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>

                <!-- Input de Mensagem -->
                <div class="p-4 border-t border-zinc-700">
                    <div class="flex gap-2">
                        <input 
                            wire:model="newMessage" 
                            wire:keydown.enter="sendMessage"
                            type="text" 
                            placeholder="Digite uma mensagem..."
                            class="flex-1 bg-zinc-800 border border-zinc-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        >
                        <flux:button wire:click="sendMessage" variant="primary" size="sm">
                            <x-flux::icon name="paper-airplane" class="w-4 h-4" />
                        </flux:button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Modal Criar Live -->
    @if($showCreateModal)
        <flux:modal wire:model="showCreateModal" size="lg">
            <flux:modal.header>
                <flux:heading size="base">Criar Nova Live</flux:heading>
            </flux:modal.header>

            <flux:modal.body>
                <div class="space-y-4">
                    <div>
                        <flux:field>
                            <flux:label>Título da Live</flux:label>
                            <flux:input wire:model="title" placeholder="Digite o título da sua live" />
                            <flux:error name="title" />
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Descrição (opcional)</flux:label>
                            <flux:textarea wire:model="description" placeholder="Descreva sobre o que será sua live" rows="3" />
                            <flux:error name="description" />
                        </flux:field>
                    </div>
                </div>
            </flux:modal.body>

            <flux:modal.footer>
                <flux:button wire:click="closeCreateModal" variant="ghost">Cancelar</flux:button>
                <flux:button wire:click="createLiveStream" variant="primary">Criar Live</flux:button>
            </flux:modal.footer>
        </flux:modal>
    @endif

    <!-- Modal Encerrar Live -->
    @if($showEndModal)
        <flux:modal wire:model="showEndModal" size="sm">
            <flux:modal.header>
                <flux:heading size="base">Encerrar Live</flux:heading>
            </flux:modal.header>

            <flux:modal.body>
                <p>Tem certeza que deseja encerrar a live? Esta ação não pode ser desfeita.</p>
            </flux:modal.body>

            <flux:modal.footer>
                <flux:button wire:click="closeEndModal" variant="ghost">Cancelar</flux:button>
                <flux:button wire:click="endLive" variant="danger">Encerrar Live</flux:button>
            </flux:modal.footer>
        </flux:modal>
    @endif

    <!-- Scripts internos -->
    <div style="display: none;">
        <!-- Importar funções de Live Stream -->
        @vite('resources/js/live-stream-broadcast.js')

        <script>
        // Aguardar carregamento das funções
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Live Stream Broadcast carregado');

            // Verificar se as funções estão disponíveis
            if (typeof window.testCamera === 'function') {
                console.log('✅ Funções de broadcast carregadas com sucesso');
            } else {
                console.error('❌ Erro ao carregar funções de broadcast');
            }
        });

        // Eventos Livewire para live stream
        document.addEventListener('livewire:init', () => {
            console.log('Livewire inicializado para live stream');

            Livewire.on('live-stream-created', (streamId) => {
                console.log('Live criada:', streamId);
            });

            Livewire.on('live-stream-started', async (streamId) => {
                console.log('Live iniciada:', streamId);
                console.log('Tentando iniciar câmera simples...');

                // Aguardar um pouco para garantir que o DOM foi atualizado
                setTimeout(async () => {
                    if (typeof window.startCameraSimple === 'function') {
                        await window.startCameraSimple();
                    } else {
                        console.error('Função startCameraSimple não encontrada');
                    }
                }, 1000);
            });

            Livewire.on('start-real-streaming', async () => {
                console.log('Iniciando streaming real...');
                if (typeof window.startStreamingReal === 'function') {
                    await window.startStreamingReal();
                }
            });

            Livewire.on('live-stream-ended', async (streamId) => {
                console.log('Live encerrada:', streamId);

                // Parar streaming real
                if (typeof window.stopStreamingReal === 'function') {
                    await window.stopStreamingReal();
                }

                if (typeof window.stopCameraSimple === 'function') {
                    window.stopCameraSimple();
                }
            });

            // Listener para quando a câmera é ativada
            Livewire.on('camera-activated', () => {
                console.log('Câmera ativada, notificando viewers...');
                @this.call('notifyCameraActive');
            });
        });

        // Auto-scroll do chat
        function scrollChatToBottom() {
            const chatMessages = document.getElementById('chat-messages');
            if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        // Observer para auto-scroll quando novas mensagens chegarem
        const chatObserver = new MutationObserver(scrollChatToBottom);
        const chatContainer = document.getElementById('chat-messages');
        if (chatContainer) {
            chatObserver.observe(chatContainer, { childList: true, subtree: true });
        }
        </script>
    </div>
</div>
