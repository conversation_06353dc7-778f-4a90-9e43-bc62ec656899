<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('night_board_plans', function (Blueprint $table) {
            $table->string('background_position')->default('C3')->after('background_image');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('night_board_plans', function (Blueprint $table) {
            $table->dropColumn('background_position');
        });
    }
};
