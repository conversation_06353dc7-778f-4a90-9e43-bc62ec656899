<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('live_stream_donations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('live_stream_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Quem recebe
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade'); // Quem envia
            $table->decimal('amount', 10, 2);
            $table->string('charm_type')->nullable(); // Tipo do charm enviado
            $table->text('message')->nullable();
            $table->string('status')->default('completed');
            $table->timestamps();
            
            $table->index(['live_stream_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('live_stream_donations');
    }
};
