<div>
    <div class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Formulário de Criação/Edição -->
            <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
                    <h3 class="text-lg font-semibold leading-6 text-gray-900 dark:text-white">{{ $is_editing ? 'Editar Nível' : 'Criar Novo Nível' }}</h3>
                </div>
                <div class="p-6">
                    <form wire:submit.prevent="{{ $is_editing ? 'updateLevel' : 'createLevel' }}">
                        <flux:input type="number" wire:model="level" label="Nível" placeholder="Número do Nível" min="1" />
                        <flux:input type="number" wire:model="min_points" label="Pontos Mínimos" placeholder="Pontos para atingir este nível" class="mt-4" />
                        <flux:textarea wire:model="permissions" label="Permissões (JSON)" placeholder="Ex: [\"create_post\", \"upload_video\"]" class="mt-4" />

                        <flux:button type="submit" class="mt-6">{{ $is_editing ? 'Atualizar Nível' : 'Criar Nível' }}</flux:button>
                        @if ($is_editing)
                            <flux:button type="button" wire:click="resetForm" class="mt-6 ml-2" color="secondary">Cancelar Edição</flux:button>
                        @endif
                    </form>

                    @if (session()->has('message'))
                        <div class="mt-4 p-4 text-sm rounded-md {{ session('message_type', 'success') === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                            {{ session('message') }}
                        </div>
                    @endif

                    @error('level') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    @error('min_points') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    @error('permissions') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>
            </div>

            <!-- Tabela de Níveis Existentes -->
            <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
                 <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
                    <h3 class="text-lg font-semibold leading-6 text-gray-900 dark:text-white">Lista de Níveis</h3>
                </div>
                <div class="p-6">

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
                            <thead class="bg-gray-50 dark:bg-zinc-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Nível</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Pontos Mínimos</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Permissões</th>
                                    <th scope="col" class="relative px-6 py-3"><span class="sr-only">Ações</span></th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
                                @forelse ($levels as $levelItem)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $levelItem->level }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $levelItem->min_points }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                            @if (!empty($levelItem->permissions))
                                                @foreach ($levelItem->permissions as $permission)
                                                    <flux:badge size="sm" class="mr-1 mb-1">{{ $permission }}</flux:badge>
                                                @endforeach
                                            @else
                                                Nenhuma permissão
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <flux:button size="sm" color="secondary" wire:click="editLevel({{ $levelItem->id }})" class="mr-2">Editar</flux:button>
                                            <flux:button size="sm" color="danger" wire:click="deleteLevel({{ $levelItem->id }})" wire:confirm="Tem certeza que deseja deletar este nível?">Deletar</flux:button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 text-center">Nenhum nível encontrado.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $levels->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
