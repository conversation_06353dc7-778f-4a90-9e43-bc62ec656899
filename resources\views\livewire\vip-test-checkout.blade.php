<div class="max-w-4xl mx-auto p-6 bg-zinc-900 text-white rounded-lg">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-white mb-2">Teste Específico - Checkout VIP</h2>
        <p class="text-gray-300">Teste detalhado do sistema de checkout VIP</p>
    </div>

    <!-- <PERSON><PERSON><PERSON> de Teste -->
    <div class="mb-6">
        <button 
            wire:click="testVipCheckout" 
            wire:loading.attr="disabled"
            class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg disabled:opacity-50 mr-4"
        >
            <span wire:loading.remove wire:target="testVipCheckout">Testar Checkout VIP Completo</span>
            <span wire:loading wire:target="testVipCheckout">Testando...</span>
        </button>

        <button 
            wire:click="clearResults" 
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
        >
            Limpar Resultados
        </button>
    </div>

    <!-- Resultados dos Testes -->
    @if(count($testResults) > 0)
        <div class="space-y-4">
            <h3 class="text-xl font-semibold text-white mb-4">Resultados do Teste</h3>
            
            @foreach($testResults as $result)
                <div class="border rounded-lg p-4 
                    @if($result['status'] === 'success') border-green-600 bg-green-900/20
                    @else border-red-600 bg-red-900/20
                    @endif">
                    
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-lg">{{ $result['test'] }}</h4>
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($result['status'] === 'success') bg-green-600 text-white
                            @else bg-red-600 text-white
                            @endif">
                            {{ $result['status'] === 'success' ? 'Sucesso' : 'Erro' }}
                        </span>
                    </div>
                    
                    <p class="text-gray-300 mb-2">{{ $result['message'] }}</p>
                    
                    @if(!empty($result['details']))
                        <div class="mt-3 p-3 bg-zinc-800 rounded">
                            <h5 class="font-medium text-sm text-gray-400 mb-2">Detalhes:</h5>
                            <pre class="text-xs text-gray-300 overflow-x-auto">{{ json_encode($result['details'], JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    @endif

    <!-- Informações Importantes -->
    <div class="mt-8 p-4 bg-yellow-900/20 border border-yellow-600 rounded-lg">
        <h3 class="text-lg font-semibold text-yellow-300 mb-2">⚠️ Importante</h3>
        <ul class="text-yellow-200 text-sm space-y-1">
            <li>• Este teste cria uma assinatura temporária que é removida automaticamente</li>
            <li>• Nenhum pagamento real será processado</li>
            <li>• O teste verifica apenas a criação da sessão de checkout</li>
            <li>• Verifique os logs em storage/logs para mais detalhes</li>
        </ul>
    </div>

    <!-- Link para voltar -->
    <div class="mt-6">
        <a href="{{ route('test.payments') }}" class="text-blue-400 hover:text-blue-300 underline">
            ← Voltar para Testes Gerais
        </a>
    </div>
</div>
