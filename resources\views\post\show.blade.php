<?php
use Illuminate\Support\Facades\Storage;
?>

<x-layouts.app :title="$post->content ? Str::limit($post->content, 50) : 'Post'">
    <div class="min-h-screen py-2 sm:py-8 lg:py-2">
        <div class="mx-auto max-w-screen-md px-4 md:px-8">
            @if($post->content)
                <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ Str::limit($post->content, 100) }}</h1>
            @endif
            <div class="flex items-center space-x-3 mt-4">
                @php
                    $userPhoto = $post->user->userPhotos->first();
                    $avatarUrl = $userPhoto ? Storage::url($userPhoto->photo_path) : asset('images/users/avatar.jpg');
                @endphp
                <img src="{{ $avatarUrl }}" alt="{{ $post->user->name }}" class="w-10 h-10 rounded-full object-cover">
                <div>
                    <h4 class="font-semibold text-gray-800 dark:text-gray-100">{{ $post->user->name }}</h4>
                    <p class="text-sm text-gray-500">
                        <a href="/{{ $post->user->username }}" class="hover:underline"> {{ '@'.$post->user->username }}</a>
                    </p>
                </div>
            </div>
            @if($post->image)
                <img src="{{ Storage::url($post->image) }}" alt="Post image" class="mt-4 rounded-lg shadow-lg w-full">
            @endif
            @if($post->content)
                <p class="mt-6 text-gray-700 dark:text-gray-300">{{ $post->content }}</p>
            @endif
            @if($post->video)
                <div class="mt-4">
                    <video controls class="w-full rounded-lg shadow-lg" preload="metadata">
                        <source src="{{ Storage::url($post->video) }}" type="video/mp4">
                        Seu navegador não suporta o elemento de vídeo.
                    </video>
                </div>
            @endif
            <div class="mt-2 text-xs text-gray-400">
                <span>Created: {{ $post->created_at->format('M d, Y H:i') }}</span>
                @if($post->updated_at && $post->updated_at != $post->created_at)
                    <span class="ml-2">Updated: {{ $post->updated_at->format('M d, Y H:i') }}</span>
                @endif
            </div>
            <div class="mt-4">
                <span class="text-sm text-gray-500">Likes: {{ $post->likes()->count() }}</span>
                <span class="ml-2 text-sm text-gray-500">Comments: {{ $post->comments()->count() }}</span>
            </div>
            @if(Auth::id() === $post->user_id)
                <div class="mt-4">
                    <button wire:click="openDeleteModal({{ $post->id }})" class="text-red-500 hover:text-red-700 p-1" title="Excluir postagem">
                        <x-flux::icon icon="trash" variant="outline" class="w-5 h-5" />
                    </button>
                </div>
            @endif
        </div>
    </div>
</x-layouts.app>