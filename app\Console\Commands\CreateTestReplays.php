<?php

namespace App\Console\Commands;

use App\Models\LiveStream;
use App\Models\User;
use Illuminate\Console\Command;

class CreateTestReplays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:create-replays';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cria<PERSON> lives de teste com replays';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = User::first();

        if (!$user) {
            $this->error('Nenhum usuário encontrado!');
            return;
        }

        // Verificar se já existem replays
        $existingReplays = LiveStream::where('has_replay', true)->count();
        $this->info("Replays existentes: {$existingReplays}");

        // Criar 3 lives de teste com replay
        for ($i = 1; $i <= 3; $i++) {
            $live = LiveStream::create([
                'user_id' => $user->id,
                'title' => "Live de Teste #{$i} - Replay Disponível",
                'description' => "Esta é uma live de teste #{$i} que foi gravada e agora está disponível como replay.",
                'status' => 'ended',
                'viewers_count' => rand(50, 300),
                'total_donations' => rand(100, 500),
                'started_at' => now()->subHours(rand(1, 24)),
                'ended_at' => now()->subHours(rand(1, 12)),
                'video_path' => "videos/example-live-{$i}.mp4",
                'duration' => rand(1800, 7200), // 30min a 2h
                'has_replay' => true
            ]);

            $this->info("Live criada: {$live->title} (ID: {$live->id})");
        }

        $totalReplays = LiveStream::where('has_replay', true)->count();
        $this->info("Total de replays agora: {$totalReplays}");
    }
}
