<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\JobApplication;

class JobApplicationConfirmation extends Notification
{
    use Queueable;

    protected $application;

    /**
     * Create a new notification instance.
     */
    public function __construct(JobApplication $application)
    {
        $this->application = $application;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $job = $this->application->job;
        
        $subject = 'Candidatura Confirmada - ' . $job->title;
        
        if ($this->application->is_vip_priority) {
            $subject = '[VIP] ' . $subject;
        }

        return (new MailMessage)
            ->subject($subject)
            ->view('emails.job-application-confirmation', [
                'application' => $this->application,
                'job' => $job,
                'user' => $notifiable,
            ]);
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'application_id' => $this->application->id,
            'job_title' => $this->application->job->title,
            'status' => $this->application->status,
            'is_vip' => $this->application->is_vip_priority,
        ];
    }
}
