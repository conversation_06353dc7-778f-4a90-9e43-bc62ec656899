<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckUploadLimits extends Command
{
    protected $signature = 'upload:check-limits';
    protected $description = 'Verifica os limites de upload configurados no sistema';

    public function handle()
    {
        $this->info('🔍 Verificando Configurações de Upload');
        $this->newLine();

        // Verificar configurações PHP
        $this->info('📋 Configurações PHP:');
        $this->line('   - upload_max_filesize: ' . ini_get('upload_max_filesize'));
        $this->line('   - post_max_size: ' . ini_get('post_max_size'));
        $this->line('   - max_execution_time: ' . ini_get('max_execution_time') . 's');
        $this->line('   - max_input_time: ' . ini_get('max_input_time') . 's');
        $this->line('   - memory_limit: ' . ini_get('memory_limit'));
        $this->line('   - file_uploads: ' . (ini_get('file_uploads') ? 'Habilitado' : 'Desabilitado'));
        $this->line('   - max_file_uploads: ' . ini_get('max_file_uploads'));

        $this->newLine();

        // Verificar configurações Livewire
        $this->info('⚡ Configurações Livewire:');
        $livewireConfig = config('livewire.temporary_file_upload');
        $this->line('   - Max file size: ' . ($livewireConfig['rules'][1] ?? 'N/A'));
        $this->line('   - Max upload time: ' . ($livewireConfig['max_upload_time'] ?? 'N/A') . ' minutos');
        $this->line('   - Disk: ' . ($livewireConfig['disk'] ?? 'N/A'));
        $this->line('   - Directory: ' . ($livewireConfig['directory'] ?? 'N/A'));

        $this->newLine();

        // Converter para bytes e verificar
        $uploadMaxBytes = $this->convertToBytes(ini_get('upload_max_filesize'));
        $postMaxBytes = $this->convertToBytes(ini_get('post_max_size'));
        $targetBytes = 200 * 1024 * 1024; // 200MB

        $this->info('🎯 Verificação de Limites para 200MB:');
        
        if ($uploadMaxBytes >= $targetBytes) {
            $this->info('   ✅ upload_max_filesize: OK (' . $this->formatBytes($uploadMaxBytes) . ')');
        } else {
            $this->error('   ❌ upload_max_filesize: Insuficiente (' . $this->formatBytes($uploadMaxBytes) . ')');
        }

        if ($postMaxBytes >= $targetBytes) {
            $this->info('   ✅ post_max_size: OK (' . $this->formatBytes($postMaxBytes) . ')');
        } else {
            $this->error('   ❌ post_max_size: Insuficiente (' . $this->formatBytes($postMaxBytes) . ')');
        }

        $this->newLine();

        // Verificar diretórios
        $this->info('📁 Verificação de Diretórios:');
        $storagePublic = storage_path('app/public');
        $postsImages = storage_path('app/public/posts/images');
        $postsVideos = storage_path('app/public/posts/videos');
        $livewireTmp = storage_path('app/livewire-tmp');

        $this->checkDirectory($storagePublic, 'Storage Public');
        $this->checkDirectory($postsImages, 'Posts Images');
        $this->checkDirectory($postsVideos, 'Posts Videos');
        $this->checkDirectory($livewireTmp, 'Livewire Temp');

        $this->newLine();

        // Recomendações
        if ($uploadMaxBytes < $targetBytes || $postMaxBytes < $targetBytes) {
            $this->warn('⚠️  Recomendações:');
            $this->line('   1. Atualize o arquivo .htaccess ou php.ini');
            $this->line('   2. Reinicie o servidor web');
            $this->line('   3. Execute este comando novamente para verificar');
        } else {
            $this->info('🎉 Todas as configurações estão adequadas para uploads de 200MB!');
        }

        return 0;
    }

    private function convertToBytes($value)
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    private function formatBytes($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    private function checkDirectory($path, $name)
    {
        if (is_dir($path)) {
            if (is_writable($path)) {
                $this->info("   ✅ {$name}: OK (gravável)");
            } else {
                $this->error("   ❌ {$name}: Não gravável");
            }
        } else {
            $this->warn("   ⚠️  {$name}: Não existe (será criado automaticamente)");
        }
    }
}
