<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::dropIfExists('taggables');
   Schema::create('taggables', function (Blueprint $table) {
       $table->id();
       $table->unsignedBigInteger('hashtag_id');
       $table->string('taggable_type');
       $table->unsignedBigInteger('taggable_id');
       $table->timestamps();
   });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('taggables');
    }
};
