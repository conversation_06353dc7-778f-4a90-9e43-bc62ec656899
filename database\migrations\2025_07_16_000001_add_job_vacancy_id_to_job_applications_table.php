<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddJobVacancyIdToJobApplicationsTable extends Migration
{
    public function up()
    {
        Schema::table('job_applications', function (Blueprint $table) {
            $table->unsignedBigInteger('job_vacancy_id')->after('id');
            $table->foreign('job_vacancy_id')->references('id')->on('job_vacancies')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('job_applications', function (Blueprint $table) {
            $table->dropForeign(['job_vacancy_id']);
            $table->dropColumn('job_vacancy_id');
        });
    }
}
