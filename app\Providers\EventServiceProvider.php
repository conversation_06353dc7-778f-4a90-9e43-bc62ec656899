<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Models\User;
use App\Observers\UserObserver;
use App\Models\Achievement;
use App\Models\UserPoint;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * The model observers for your application.
     *
     * @var array
     */
    protected $observers = [
        User::class => [UserObserver::class],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        // Ouvir o evento quando um pivot é anexado a um relacionamento BelongsToMany do modelo User
        Event::listen('eloquent.pivotAttached: App\\Models\\User', function ($relationName, $pivotIds, $model, $attached) {
            // $model é a instância do modelo User onde o pivot foi anexado
            // $relationName é o nome do relacionamento (ex: 'followers', 'groups', 'attendingEvents')
            // $pivotIds contém os IDs dos modelos anexados (ex: user_id do novo seguidor)
            // $attached são os atributos anexados à tabela pivô

            // Lógica para a conquista 'Influenciador Inicial'
            if ($relationName === 'followers') {
                $followedUser = $model; // O usuário que ganhou o seguidor

                // Verificar se a conquista 'Influenciador Inicial' existe
                $influencerAchievement = Achievement::where('name', 'Influenciador Inicial')->first();

                // TODO: Obter o limiar de seguidores da configuração da conquista (precisa de coluna na tabela achievements)
                // Por enquanto, usando um valor fixo
                $followersThreshold = 50; // Exemplo de limiar de seguidores

                // Recarregar o relacionamento followers para obter a contagem atualizada
                $followedUser->load('followers');
                $followersCount = $followedUser->followers->count();

                if ($influencerAchievement && $followersCount >= $followersThreshold) {
                    // Verificar se o usuário seguido já tem a conquista
                    if (!$followedUser->achievements->contains($influencerAchievement->id)) {
                        // Conceder a conquista
                        $followedUser->achievements()->attach($influencerAchievement->id);

                        // Adicionar os pontos da conquista
                        UserPoint::addPoints(
                            $followedUser->id,
                            'achievement',
                            $influencerAchievement->points,
                            "Ganhou a conquista \"" . $influencerAchievement->name . "\"",
                            $influencerAchievement->id,
                            Achievement::class
                        );

                        // Opcional: disparar evento para notificar o usuário no frontend
                        // $followedUser->notify(new AchievementUnlocked($influencerAchievement));
                    }
                }
            }

            // TODO: Adicionar lógica para outras conquistas baseadas em pivots, se necessário
            // Ex: participação em eventos (event_attendees), entrada em grupos (group_user)
             if ($relationName === 'attendingEvents') {
                 // Lógica para conquista de eventos (precisa verificar se o evento é elegível, etc.)
             }

             if ($relationName === 'groups') {
                 // Lógica para conquista de grupos (precisa verificar se entrou no grupo, etc.)
             }

        });

        // Aqui você pode registrar outros listeners de eventos Eloquent, se necessário
        // Event::listen('eloquent.created: App\\Models\\Post', ...);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
