<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Hashtag; // Assuming you have a Hashtag model

class TrendingHashtags extends Component
{
    public $limit = 5; // Default limit

    public function mount($limit = null)
    {
        if ($limit) {
            $this->limit = $limit;
        }
    }

    public function render()
    {
        $hashtags = Hashtag::trending($this->limit);

        return view('livewire.trending-hashtags', [
            'hashtags' => $hashtags,
        ]);
    }
}