<x-layouts.app :title="__('Sugestões de Conexões')">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {{-- Header --}}
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Descubra Novas Conexões
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
                Encontre pessoas com interesses similares, da sua região ou que participam dos mesmos grupos.
            </p>
        </div>

        {{-- Componente de Sugestões Inteligentes --}}
        <livewire:smart-suggestions />

        {{-- Seção de Estatísticas da Rede --}}
        <div class="mt-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
            <h2 class="text-2xl font-bold mb-4">Sua Rede Social</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold">{{ auth()->user()->followers()->count() }}</div>
                    <div class="text-purple-100">Seguidores</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">{{ auth()->user()->following()->count() }}</div>
                    <div class="text-purple-100">Seguindo</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">{{ auth()->user()->groups()->where('is_approved', true)->count() }}</div>
                    <div class="text-purple-100">Grupos</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">{{ auth()->user()->posts()->count() }}</div>
                    <div class="text-purple-100">Posts</div>
                </div>
            </div>
        </div>

        {{-- Dicas para Expandir a Rede --}}
        <div class="mt-8 bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <flux:icon.light-bulb class="w-6 h-6 mr-2 text-yellow-500" />
                Dicas para Expandir sua Rede
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="flex items-start">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <flux:icon.map-pin class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-1">Complete sua Localização</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Adicione sua cidade para encontrar pessoas próximas.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <flux:icon.heart class="w-5 h-5 text-red-600 dark:text-red-400" />
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-1">Adicione seus Interesses</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Quanto mais hobbies, mais conexões relevantes.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <flux:icon.user-group class="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-1">Participe de Grupos</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Junte-se a comunidades com interesses similares.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <flux:icon.chat-bubble-left class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-1">Seja Ativo</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Poste, comente e interaja regularmente.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <flux:icon.photo class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-1">Adicione Fotos</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Perfis com fotos recebem mais conexões.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <flux:icon.star class="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-1">Seja Autêntico</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Perfis genuínos atraem conexões de qualidade.</p>
                    </div>
                </div>
            </div>

            <div class="mt-6 text-center">
                <flux:button 
                    href="{{ route('settings.profile') }}" 
                    wire:navigate
                    variant="primary"
                    class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-none"
                >
                    <flux:icon.pencil class="w-4 h-4 mr-2" />
                    Melhorar meu Perfil
                </flux:button>
            </div>
        </div>
    </div>
</x-layouts.app>
