<x-layouts.app :title="__('Pedido #' . $order->id)">
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto text-center">
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold">Sucesso!</strong>
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif
        @if(session('info'))
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline">{{ session('info') }}</span>
            </div>
        @endif

        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-8">
            <svg class="w-16 h-16 text-green-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h1 class="text-2xl font-bold text-title mb-2">Obrigado pela sua compra!</h1>
            <p class="text-body-light mb-4">Seu pedido <span class="font-bold text-title">#{{ $order->id }}</span> foi recebido e está sendo processado.</p>

            <div class="text-left border-t border-gray-200 dark:border-gray-700 mt-6 pt-6">
                <h2 class="text-xl font-semibold text-title mb-3">Próximos Passos</h2>
                <ul class="list-disc list-inside text-body-light space-y-2">
                    <li>Você receberá uma confirmação por e-mail em breve.</li>
                    @if($order->items->contains(fn($item) => $item->product->is_digital))
                        <li>Os produtos digitais estarão disponíveis na sua área de <a href="{{ route('shop.downloads') }}" class="text-link hover:underline">Meus Downloads</a>.</li>
                    @endif
                    @if($order->items->contains(fn($item) => !$item->product->is_digital))
                        <li>Para produtos físicos, você será notificado quando estiverem prontos para retirada.</li>
                    @endif
                </ul>
            </div>

            <div class="mt-8">
                <a href="{{ route('shop.index') }}" class="text-white bg-blue-600 hover:bg-blue-700 font-semibold py-2 px-6 rounded-md">
                    Continuar Comprando
                </a>
            </div>
        </div>
    </div>
</div>
</x-layouts.app>