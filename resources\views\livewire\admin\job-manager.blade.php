<div class="p-6">
    <!-- Header -->
    <div class="mb-6 flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gerenciar Vagas</h1>
            <p class="text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON><PERSON> to<PERSON> as vagas de emprego da plataforma</p>
        </div>
        <flux:button wire:click="createJob" variant="primary">
            <flux:icon icon="plus" class="w-4 h-4 mr-2" />
            Nova Vaga
        </flux:button>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
            {{ session('message') }}
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <flux:icon icon="briefcase" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobs->total() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Total de Vagas</div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <flux:icon icon="check-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobs->where('is_active', true)->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Vagas Ativas</div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <flux:icon icon="star" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobs->where('is_featured', true)->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Em Destaque</div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <flux:icon icon="users" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $jobs->sum('applications_count') }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Total Candidaturas</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filtros</h3>
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <flux:field>
                        <flux:label>Buscar</flux:label>
                        <flux:input
                            wire:model.live.debounce.300ms="search"
                            placeholder="Título, descrição..."
                            icon="magnifying-glass"
                        />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Categoria</flux:label>
                        <flux:select wire:model.live="categoryFilter">
                            <option value="">Todas as categorias</option>
                            @foreach($categories as $cat)
                                <option value="{{ $cat->id }}">{{ $cat->name }}</option>
                            @endforeach
                        </flux:select>
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Status</flux:label>
                        <flux:select wire:model.live="statusFilter">
                            <option value="active">Ativas</option>
                            <option value="inactive">Inativas</option>
                            <option value="featured">Em Destaque</option>
                            <option value="">Todas</option>
                        </flux:select>
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Itens por página</flux:label>
                        <flux:select wire:model.live="perPage">
                            <option value="10">10 por página</option>
                            <option value="25">25 por página</option>
                            <option value="50">50 por página</option>
                        </flux:select>
                    </flux:field>
                </div>

                <div class="flex items-end">
                    <flux:button wire:click="$set('search', '')" variant="ghost" size="sm" class="w-full">
                        <flux:icon icon="x-mark" class="w-4 h-4 mr-1" />
                        Limpar Filtros
                    </flux:button>
                </div>
            </div>
        </div>
    </div>

    <!-- Jobs Table -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-zinc-700">
                    <tr>
                        <th wire:click="sortBy('title')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>Título</span>
                                @if($sortBy === 'title')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Categoria
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Detalhes
                        </th>
                        <th wire:click="sortBy('applications_count')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>Candidaturas</span>
                                @if($sortBy === 'applications_count')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Status
                        </th>
                        <th wire:click="sortBy('created_at')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>Criada em</span>
                                @if($sortBy === 'created_at')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($jobs as $job)
                        <tr class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                            <td class="px-6 py-4">
                                <div class="flex items-start">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $job->title }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            {{ Str::limit($job->description, 60) }}
                                        </div>
                                        @if($job->is_featured)
                                            <div class="mt-1">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                                    <flux:icon icon="star" class="w-3 h-3 mr-1" />
                                                    Destaque
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $job->category?->name ?? '-' }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <div class="flex items-center gap-2 mb-1">
                                        <flux:icon icon="map-pin" class="w-3 h-3 text-gray-400" />
                                        <span>{{ $job->location ?: 'Não informado' }}</span>
                                    </div>
                                    <div class="flex items-center gap-2 mb-1">
                                        <flux:icon icon="briefcase" class="w-3 h-3 text-gray-400" />
                                        <span>{{ $job->contract_type }}</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <flux:icon icon="computer-desktop" class="w-3 h-3 text-gray-400" />
                                        <span>{{ $job->work_mode }}</span>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <div class="flex items-center gap-2 mb-1">
                                        <flux:icon icon="users" class="w-4 h-4 text-blue-500" />
                                        <span class="font-medium">{{ $job->applications_count }}</span>
                                        <span class="text-gray-500">candidaturas</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <flux:icon icon="eye" class="w-4 h-4 text-gray-400" />
                                        <span class="text-gray-500">{{ $job->views_count }} visualizações</span>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-col gap-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $job->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                        <flux:icon icon="{{ $job->is_active ? 'check-circle' : 'x-circle' }}" class="w-3 h-3 mr-1" />
                                        {{ $job->is_active ? 'Ativa' : 'Inativa' }}
                                    </span>
                                    @if($job->application_deadline && $job->application_deadline->isPast())
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300">
                                            <flux:icon icon="clock" class="w-3 h-3 mr-1" />
                                            Expirada
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $job->created_at->format('d/m/Y') }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $job->created_at->format('H:i') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <flux:button wire:click="edit({{ $job->id }})" size="sm" variant="ghost">
                                        <flux:icon icon="pencil" class="w-4 h-4" />
                                    </flux:button>

                                    <flux:button
                                        wire:click="toggleFeatured({{ $job->id }})"
                                        size="sm"
                                        variant="ghost"
                                        class="{{ $job->is_featured ? 'text-yellow-600 hover:text-yellow-800' : 'text-gray-400 hover:text-gray-600' }}"
                                        title="Alternar destaque">
                                        <flux:icon icon="star" class="w-4 h-4" />
                                    </flux:button>

                                    <flux:button
                                        wire:click="toggleActive({{ $job->id }})"
                                        size="sm"
                                        variant="ghost"
                                        class="{{ $job->is_active ? 'text-green-600 hover:text-green-800' : 'text-red-600 hover:text-red-800' }}"
                                        title="Alternar status">
                                        <flux:icon icon="{{ $job->is_active ? 'eye' : 'eye-slash' }}" class="w-4 h-4" />
                                    </flux:button>

                                    <flux:button
                                        wire:click="delete({{ $job->id }})"
                                        size="sm"
                                        variant="ghost"
                                        class="text-red-600 hover:text-red-800"
                                        onclick="return confirm('Tem certeza que deseja excluir esta vaga?')">
                                        <flux:icon icon="trash" class="w-4 h-4" />
                                    </flux:button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <flux:icon icon="briefcase" class="w-12 h-12 text-gray-400 dark:text-gray-600 mb-4" />
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhuma vaga encontrada</h3>
                                    <p class="text-gray-500 dark:text-gray-400">Não há vagas que correspondam aos filtros aplicados.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($jobs->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $jobs->links() }}
            </div>
        @endif
    </div>

    <!-- Create/Edit Modal -->
    @if($showModal)
        <flux:modal wire:model="showModal" class="max-w-4xl">
            <flux:modal.header>
                <flux:heading size="lg">{{ $isEditing ? 'Editar Vaga' : 'Nova Vaga' }}</flux:heading>
            </flux:modal.header>

            <form wire:submit.prevent="save">
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Informações Básicas</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="md:col-span-2">
                                <flux:field>
                                    <flux:label>Título da Vaga</flux:label>
                                    <flux:input wire:model="title" placeholder="Ex: Desenvolvedor Full Stack" />
                                    <flux:error name="title" />
                                </flux:field>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Categoria</flux:label>
                                    <flux:select wire:model="categoryId">
                                        <option value="">Selecione uma categoria</option>
                                        @foreach($categories as $cat)
                                            <option value="{{ $cat->id }}">{{ $cat->name }}</option>
                                        @endforeach
                                    </flux:select>
                                    <flux:error name="categoryId" />
                                </flux:field>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Localização</flux:label>
                                    <flux:input wire:model="location" placeholder="Ex: São Paulo, SP" />
                                    <flux:error name="location" />
                                </flux:field>
                            </div>

                            <div class="md:col-span-2">
                                <flux:field>
                                    <flux:label>Descrição</flux:label>
                                    <flux:textarea wire:model="description" rows="4" placeholder="Descreva a vaga, responsabilidades e o que a empresa oferece..." />
                                    <flux:error name="description" />
                                </flux:field>
                            </div>
                        </div>
                    </div>

                    <!-- Job Details -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Detalhes da Vaga</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <flux:field>
                                    <flux:label>Tipo de Contrato</flux:label>
                                    <flux:select wire:model="contractType">
                                        <option value="CLT">CLT</option>
                                        <option value="PJ">PJ</option>
                                        <option value="Freelance">Freelance</option>
                                        <option value="Estágio">Estágio</option>
                                        <option value="Temporário">Temporário</option>
                                    </flux:select>
                                    <flux:error name="contractType" />
                                </flux:field>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Modalidade</flux:label>
                                    <flux:select wire:model="workMode">
                                        <option value="Presencial">Presencial</option>
                                        <option value="Remoto">Remoto</option>
                                        <option value="Híbrido">Híbrido</option>
                                    </flux:select>
                                    <flux:error name="workMode" />
                                </flux:field>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Nível de Experiência</flux:label>
                                    <flux:select wire:model="experienceLevel">
                                        <option value="Júnior">Júnior</option>
                                        <option value="Pleno">Pleno</option>
                                        <option value="Sênior">Sênior</option>
                                        <option value="Especialista">Especialista</option>
                                    </flux:select>
                                    <flux:error name="experienceLevel" />
                                </flux:field>
                            </div>
                        </div>
                    </div>

                    <!-- Salary Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Remuneração</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <flux:field>
                                    <flux:label>Salário Mínimo (R$)</flux:label>
                                    <flux:input wire:model="salaryMin" type="number" min="0" step="0.01" placeholder="0,00" />
                                    <flux:error name="salaryMin" />
                                </flux:field>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Salário Máximo (R$)</flux:label>
                                    <flux:input wire:model="salaryMax" type="number" min="0" step="0.01" placeholder="0,00" />
                                    <flux:error name="salaryMax" />
                                </flux:field>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Período</flux:label>
                                    <flux:select wire:model="salaryPeriod">
                                        <option value="Mensal">Mensal</option>
                                        <option value="Semanal">Semanal</option>
                                        <option value="Diário">Diário</option>
                                        <option value="Por hora">Por hora</option>
                                    </flux:select>
                                    <flux:error name="salaryPeriod" />
                                </flux:field>
                            </div>

                            <div class="md:col-span-3">
                                <flux:checkbox wire:model="salaryNegotiable" label="Salário negociável" />
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Informações Adicionais</h3>
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <flux:field>
                                        <flux:label>Quantidade de Vagas</flux:label>
                                        <flux:input wire:model="vacancies" type="number" min="1" />
                                        <flux:error name="vacancies" />
                                    </flux:field>
                                </div>

                                <div>
                                    <flux:field>
                                        <flux:label>Data Limite para Candidatura</flux:label>
                                        <flux:input wire:model="applicationDeadline" type="date" />
                                        <flux:error name="applicationDeadline" />
                                    </flux:field>
                                </div>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Requisitos</flux:label>
                                    <flux:textarea wire:model="requirements" rows="3" placeholder="Liste os requisitos necessários para a vaga..." />
                                    <flux:error name="requirements" />
                                </flux:field>
                            </div>

                            <div>
                                <flux:field>
                                    <flux:label>Benefícios</flux:label>
                                    <flux:textarea wire:model="benefits" rows="3" placeholder="Liste os benefícios oferecidos..." />
                                    <flux:error name="benefits" />
                                </flux:field>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Configurações</h3>
                        <div class="space-y-3">
                            <flux:checkbox wire:model="requiresResume" label="Exigir currículo" />
                            <flux:checkbox wire:model="requiresCoverLetter" label="Exigir carta de apresentação" />
                            <flux:checkbox wire:model="isFeatured" label="Vaga em destaque" />
                            <flux:checkbox wire:model="isActive" label="Vaga ativa" />
                        </div>
                    </div>
                </div>

                <flux:modal.footer>
                    <div class="flex justify-between w-full">
                        <flux:button wire:click="closeModal" variant="ghost">
                            Cancelar
                        </flux:button>
                        <flux:button type="submit" variant="primary">
                            <flux:icon icon="check" class="w-4 h-4 mr-1" />
                            {{ $isEditing ? 'Atualizar Vaga' : 'Criar Vaga' }}
                        </flux:button>
                    </div>
                </flux:modal.footer>
            </form>
        </flux:modal>
    @endif

</div>
