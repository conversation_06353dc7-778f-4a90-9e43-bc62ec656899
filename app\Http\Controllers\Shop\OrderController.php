<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Cart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class OrderController extends Controller
{
    /**
     * Mostra a página de sucesso do pedido.
     */
    public function showSuccess(Order $order)
    {
        // Garante que o usuário só possa ver seus próprios pedidos
        if ($order->user_id !== Auth::id()) {
            abort(404);
        }

        return view('shop.order-success', compact('order'));
    }
    
    /**
     * Mostra os detalhes de um pedido.
     */
    public function show(Order $order)
    {
        // Garante que o usuário só possa ver seus próprios pedidos
        if ($order->user_id !== Auth::id()) {
            abort(403, "Você não tem permissão para ver este pedido.");
        }

        return view('shop.order-show', compact('order'));
    }

    /**
     * Lida com o retorno bem-sucedido do Stripe.
     */
    public function paymentSuccess(Request $request, Order $order)
    {
        if ($order->user_id !== Auth::id()) {
            abort(404);
        }

        if ($order->isPaid()) {
            return redirect()->route('shop.order.success', $order)
                ->with('info', 'O pagamento para este pedido já foi confirmado.');
        }

        try {
            Stripe::setApiKey(config('cashier.secret'));
            $sessionId = $request->get('session_id');

            if (!$sessionId) {
                return redirect()->route('shop.order.show', $order)->with('error', 'ID da sessão de pagamento não encontrado.');
            }

            $session = Session::retrieve($sessionId);

            if ($session->metadata->order_id != $order->id) {
                 return redirect()->route('shop.order.show', $order)->with('error', 'Sessão de pagamento inválida.');
            }
            
            if ($session->payment_status == 'paid') {
                $order->markAsPaid('stripe', $session->id);
                
                $cart = Cart::where('user_id', Auth::id())->first();
                if ($cart) {
                    $cart->clear();
                }

                return redirect()->route('shop.order.success', $order)->with('success', 'Pagamento confirmado com sucesso!');
            }

            return redirect()->route('shop.order.show', $order)->with('error', 'O pagamento não foi concluído. Tente novamente.');

        } catch (\Exception $e) {
            logger()->error('Erro ao verificar sessão do Stripe: ' . $e->getMessage(), ['order_id' => $order->id]);
            return redirect()->route('shop.order.show', $order)->with('error', 'Ocorreu um erro ao verificar seu pagamento. Entre em contato com o suporte.');
        }
    }

    /**
     * Lida com o cancelamento do pagamento no Stripe.
     */
    public function paymentCancel(Request $request, Order $order)
    {
        if ($order->user_id !== Auth::id()) {
            abort(404);
        }
        
        $order->update(['status' => Order::STATUS_FAILED]);
        
        return redirect()->route('shop.order.show', $order)->with('info', 'O processo de pagamento foi cancelado. Você pode tentar novamente a partir desta página.');
    }
}
