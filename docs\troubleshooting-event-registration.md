# Troubleshooting - Inscrições de Eventos

## Problemas Corrigidos

### ✅ Correções Implementadas:

1. **Query SQL incorreta** - Corrigido `event_attendees.status` para `status` em:
   - `app/Livewire/Events/EventRegistration.php` (linha 37)
   - `app/Http/Controllers/EventAttendeeController.php` (linha 42 e 219)
   - `app/Models/Event.php` (linhas 185 e 197)

2. **Tratamento de erros melhorado** - Adicionados logs e try-catch

3. **Validações robustas** - Verificações de evento ativo, data, capacidade

4. **Middleware temporariamente removido** - Para evitar bloqueios de tempo

## Como Testar

### 1. Teste Básico no Browser

1. Acesse um evento: `/eventos/{slug}`
2. Abra o DevTools (F12)
3. Vá para a aba Console
4. Procure por mensagens de debug que começam com `[Event Registration Debug]`

### 2. Teste com Componente Simplificado

O componente `SimpleEventRegistration` foi criado para debug. Ele mostra:
- Informações detalhadas do evento
- Status de inscrição
- Debug info em tempo real
- Modal simplificado

### 3. Verificações no Banco de Dados

Execute estas queries no phpMyAdmin:

```sql
-- Verificar eventos ativos
SELECT id, name, date, is_active, price, capacity 
FROM events 
WHERE is_active = 1 
ORDER BY date ASC;

-- Verificar inscrições
SELECT 
    ea.id,
    ea.event_id,
    ea.user_id,
    ea.status,
    ea.payment_status,
    ea.ticket_code,
    e.name as evento_nome,
    u.name as usuario_nome
FROM event_attendees ea
JOIN events e ON ea.event_id = e.id
JOIN users u ON ea.user_id = u.id
ORDER BY ea.created_at DESC
LIMIT 10;
```

### 4. Verificar Logs

Verifique o arquivo `storage/logs/laravel.log` para mensagens como:
- `Erro ao criar inscrição gratuita`
- `Erro ao criar sessão Stripe`
- `Erro geral ao processar inscrição`

### 5. Teste Manual via Console

No DevTools, execute:
```javascript
// Testar inscrição manualmente
debugEventRegistration();
```

## Possíveis Problemas e Soluções

### Problema: Botão não responde

**Verificar:**
1. Console do browser para erros JavaScript
2. Se Livewire está carregado: `window.Livewire`
3. Se Alpine.js está funcionando: `window.Alpine`

**Solução:**
```javascript
// No console do browser
console.log('Livewire:', window.Livewire);
console.log('Alpine:', window.Alpine);
```

### Problema: Modal não abre

**Verificar:**
1. Se `showConfirmModal` está sendo definido como `true`
2. Se há erros de CSS/Tailwind
3. Se Flux UI está funcionando

**Solução:**
- Use o componente simplificado que tem modal básico

### Problema: Inscrição não é salva

**Verificar:**
1. Logs do Laravel
2. Estrutura da tabela `event_attendees`
3. Permissões do usuário

**Solução:**
```sql
-- Verificar estrutura da tabela
DESCRIBE event_attendees;

-- Verificar se há registros sendo criados
SELECT COUNT(*) FROM event_attendees WHERE created_at > NOW() - INTERVAL 1 HOUR;
```

### Problema: Erro de CSRF

**Verificar:**
1. Se meta tag CSRF está presente
2. Se token não expirou

**Solução:**
```html
<!-- Verificar se existe no head -->
<meta name="csrf-token" content="{{ csrf_token() }}">
```

### Problema: Middleware bloqueando

**Verificar:**
1. Se usuário tem role adequada
2. Se tempo de visitante não expirou

**Solução:**
- Middleware foi temporariamente removido das rotas de eventos

## Scripts de Debug

### 1. Executar Comando de Correção
```bash
php artisan events:fix-registrations --dry-run
```

### 2. Limpar Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
```

### 3. Verificar Configuração
```bash
php artisan config:show database
php artisan route:list | grep events
```

## Arquivos Importantes

### Controllers
- `app/Http/Controllers/EventAttendeeController.php`
- `app/Http/Controllers/EventController.php`

### Livewire Components
- `app/Livewire/Events/EventRegistration.php` (original)
- `app/Livewire/Events/SimpleEventRegistration.php` (debug)

### Views
- `resources/views/events/show.blade.php`
- `resources/views/livewire/events/event-registration.blade.php`
- `resources/views/livewire/events/simple-event-registration.blade.php`

### Models
- `app/Models/Event.php`
- `app/Models/EventAttendee.php`

### Routes
- `routes/web.php` (linhas 147-156)

## Próximos Passos

1. **Teste o componente simplificado** primeiro
2. **Verifique os logs** para erros específicos
3. **Execute as queries SQL** para verificar dados
4. **Use o script de debug JavaScript** no browser
5. **Se tudo funcionar**, volte para o componente original

## Contato para Suporte

Se os problemas persistirem, forneça:
1. Logs específicos do `storage/logs/laravel.log`
2. Erros do console do browser
3. Screenshots do comportamento
4. Resultado das queries SQL de verificação

## Restaurar Componente Original

Quando tudo estiver funcionando, restaure o componente original:

```blade
<!-- Trocar de: -->
<livewire:events.simple-event-registration :event="$event" />

<!-- Para: -->
<livewire:events.event-registration :event="$event" />
```
