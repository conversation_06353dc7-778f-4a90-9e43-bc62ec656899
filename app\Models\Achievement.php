<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\User; // Importar o modelo User

class Achievement extends Model
{
    protected $fillable = ['name', 'description', 'icon', 'points', 'type'];

    // Relacionamento com usuários que ganharam esta conquista
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_achievements')->withTimestamps();
    }
}
