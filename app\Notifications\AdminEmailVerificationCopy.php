<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminEmailVerificationCopy extends Notification
{

    protected $data;

    /**
     * Create a new notification instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('[ADMIN COPY] Novo Usuário Registrado - ' . config('app.name'))
            ->view('emails.admin-verification-copy', [
                'user' => $this->data['user'],
                'verificationUrl' => $this->data['verificationUrl'],
                'adminRecipient' => $notifiable,
            ]);
    }
}
