<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\AlbumMedia;
use App\Models\Album;
use App\Models\UserPhoto;
use App\Models\UserCoverPhoto;
use App\Models\Post;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MediaGalleryFeatured extends Component
{
    public User $user;
    public $featuredMedias = [];
    public $recentAlbums = [];
    public $stats = [];
    public $selectedMedia = null;
    public $showLightbox = false;
    public $currentIndex = 0;

    // Novas propriedades para categorias
    public $selectedCategory = 'all';
    public $avatarPhotos = [];
    public $coverPhotos = [];
    public $postMedias = [];
    public $albumMedias = [];

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadFeaturedContent();
    }

    public function loadFeaturedContent()
    {
        $this->loadStats();
        $this->loadFeaturedMedias();
        $this->loadRecentAlbums();
        $this->loadCategorizedMedias();
    }

    protected function loadStats()
    {
        $query = AlbumMedia::where('user_id', $this->user->id);

        // Se não é o próprio usuário, contar apenas mídias públicas
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function ($q) {
                $q->where('privacy', 'public');
            });
        }

        $totalMedias = $query->count();
        $totalPhotos = $query->where('type', 'photo')->count();
        $totalVideos = $query->where('type', 'video')->count();

        $albumQuery = Album::where('user_id', $this->user->id);
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $albumQuery->where('privacy', 'public');
        }
        $totalAlbums = $albumQuery->count();

        // Contar mídias por categoria
        $avatarCount = UserPhoto::where('user_id', $this->user->id)->count();
        $coverCount = UserCoverPhoto::where('user_id', $this->user->id)->count();
        $postMediaCount = Post::where('user_id', $this->user->id)
            ->where(function ($q) {
                $q->whereNotNull('image')->orWhereNotNull('video');
            })->count();

        $this->stats = [
            'total_medias' => $totalMedias + $avatarCount + $coverCount + $postMediaCount,
            'total_photos' => $totalPhotos + $avatarCount + $coverCount,
            'total_videos' => $totalVideos,
            'total_albums' => $totalAlbums,
            'avatar_count' => $avatarCount,
            'cover_count' => $coverCount,
            'post_media_count' => $postMediaCount,
            'album_media_count' => $totalMedias,
        ];
    }

    protected function loadFeaturedMedias()
    {
        $query = AlbumMedia::where('user_id', $this->user->id)
            ->with('album');

        // Se não é o próprio usuário, mostrar apenas mídias de álbuns públicos
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function ($q) {
                $q->where('privacy', 'public');
            });
        }

        // Combinar todas as mídias baseado na categoria selecionada
        $this->combineFeaturedMedias();
    }

    protected function combineFeaturedMedias()
    {
        $allMedias = collect();

        if ($this->selectedCategory === 'all' || $this->selectedCategory === 'avatar') {
            $allMedias = $allMedias->merge($this->avatarPhotos);
        }

        if ($this->selectedCategory === 'all' || $this->selectedCategory === 'cover') {
            $allMedias = $allMedias->merge($this->coverPhotos);
        }

        if ($this->selectedCategory === 'all' || $this->selectedCategory === 'post') {
            $allMedias = $allMedias->merge($this->postMedias);
        }

        if ($this->selectedCategory === 'all' || $this->selectedCategory === 'album') {
            $allMedias = $allMedias->merge($this->albumMedias);
        }

        // Ordenar por data de criação (mais recente primeiro) e limitar
        $this->featuredMedias = $allMedias->sortByDesc('created_at')->take(12);
    }

    public function setCategory($category)
    {
        $this->selectedCategory = $category;
        $this->combineFeaturedMedias();
    }

    protected function loadRecentAlbums()
    {
        $query = Album::where('user_id', $this->user->id)
            ->with(['medias' => function ($q) {
                $q->take(3);
            }])
            ->withCount('medias');

        // Se não é o próprio usuário, mostrar apenas álbuns públicos
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->where('privacy', 'public');
        }

        $this->recentAlbums = $query->latest()->take(4)->get();
    }

    protected function loadCategorizedMedias()
    {
        // Carregar fotos de avatar
        $this->avatarPhotos = UserPhoto::where('user_id', $this->user->id)
            ->latest()
            ->get()
            ->map(function ($photo) {
                return (object) [
                    'id' => 'avatar_' . $photo->id,
                    'type' => 'photo',
                    'category' => 'avatar',
                    'url' => Storage::url($photo->photo_path),
                    'thumbnail_url' => Storage::url($photo->photo_path),
                    'title' => 'Foto de Perfil',
                    'description' => 'Avatar do usuário',
                    'created_at' => $photo->created_at,
                    'is_current' => $photo->is_current ?? false,
                    'formatted_duration' => null,
                    'formatted_size' => 'N/A',
                    'width' => null,
                    'height' => null,
                    'album' => (object) ['name' => 'Avatares'],
                ];
            });

        // Carregar fotos de capa
        $this->coverPhotos = UserCoverPhoto::where('user_id', $this->user->id)
            ->latest()
            ->get()
            ->map(function ($cover) {
                $photoPath = $cover->cropped_photo_path ?? $cover->photo_path;
                return (object) [
                    'id' => 'cover_' . $cover->id,
                    'type' => 'photo',
                    'category' => 'cover',
                    'url' => Storage::url($photoPath),
                    'thumbnail_url' => Storage::url($photoPath),
                    'title' => 'Foto de Capa',
                    'description' => 'Capa do perfil',
                    'created_at' => $cover->created_at,
                    'formatted_duration' => null,
                    'formatted_size' => 'N/A',
                    'width' => null,
                    'height' => null,
                    'album' => (object) ['name' => 'Capas'],
                ];
            });

        // Carregar mídias de posts
        $this->postMedias = Post::where('user_id', $this->user->id)
            ->where(function ($q) {
                $q->whereNotNull('image')->orWhereNotNull('video');
            })
            ->latest()
            ->get()
            ->flatMap(function ($post) {
                $medias = [];

                if ($post->image) {
                    $medias[] = (object) [
                        'id' => 'post_image_' . $post->id,
                        'type' => 'photo',
                        'category' => 'post',
                        'url' => Storage::url($post->image),
                        'thumbnail_url' => Storage::url($post->image),
                        'title' => 'Imagem do Post',
                        'description' => $post->content ? substr($post->content, 0, 100) . '...' : 'Post com imagem',
                        'created_at' => $post->created_at,
                        'post_id' => $post->id,
                        'formatted_duration' => null,
                        'formatted_size' => 'N/A',
                        'width' => null,
                        'height' => null,
                        'album' => (object) ['name' => 'Posts'],
                    ];
                }

                if ($post->video) {
                    $medias[] = (object) [
                        'id' => 'post_video_' . $post->id,
                        'type' => 'video',
                        'category' => 'post',
                        'url' => Storage::url($post->video),
                        'thumbnail_url' => asset('images/video-thumbnail.svg'),
                        'title' => 'Vídeo do Post',
                        'description' => $post->content ? substr($post->content, 0, 100) . '...' : 'Post com vídeo',
                        'created_at' => $post->created_at,
                        'post_id' => $post->id,
                        'formatted_duration' => null,
                        'formatted_size' => 'N/A',
                        'width' => null,
                        'height' => null,
                        'album' => (object) ['name' => 'Posts'],
                    ];
                }

                return $medias;
            });

        // Carregar mídias de álbuns (já existente)
        $query = AlbumMedia::where('user_id', $this->user->id)
            ->with('album');

        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function ($q) {
                $q->where('privacy', 'public');
            });
        }

        $this->albumMedias = $query->latest()
            ->get()
            ->map(function ($media) {
                $media->category = 'album';
                return $media;
            });
    }

    public function openLightbox($mediaId)
    {
        $featuredArray = $this->featuredMedias->values()->all();
        $this->selectedMedia = collect($featuredArray)->firstWhere('id', $mediaId);
        $this->currentIndex = collect($featuredArray)->search(function ($media) use ($mediaId) {
            return $media->id == $mediaId;
        });
        $this->showLightbox = true;
    }

    public function closeLightbox()
    {
        $this->showLightbox = false;
        $this->selectedMedia = null;
        $this->currentIndex = 0;
    }

    public function nextMedia()
    {
        $featuredArray = $this->featuredMedias->values()->all();
        if ($this->currentIndex < count($featuredArray) - 1) {
            $this->currentIndex++;
            $this->selectedMedia = $featuredArray[$this->currentIndex];
        }
    }

    public function previousMedia()
    {
        $featuredArray = $this->featuredMedias->values()->all();
        if ($this->currentIndex > 0) {
            $this->currentIndex--;
            $this->selectedMedia = $featuredArray[$this->currentIndex];
        }
    }

    public function scrollToFullGallery()
    {
        $this->dispatch('scroll-to-element', element: '#media-gallery');
    }

    public function render()
    {
        return view('livewire.media-gallery-featured');
    }
}
