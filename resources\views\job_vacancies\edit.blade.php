<x-layouts.app :title="__('Editar Vaga:') . ' ' . $job->title">
    <div class="container mx-auto py-8 max-w-3xl">
        <a href="{{ route('job_vacancies.index') }}" class="mb-4">Voltar para vagas</a>
        <div class="bg-white dark:bg-zinc-900 rounded-lg shadow p-8 border border-primary/10">
            <h1 class="text-2xl font-bold mb-4">Editar Vaga</h1>

            <form action="{{ route('job_vacancies.update', $job->slug) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="mb-4">
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tí<PERSON><PERSON> da Vaga</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $job->title) }}" required
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Categoria</label>
                    <select name="category_id" id="category_id" required
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                        <option value="">Selecione uma categoria</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}"
                                {{ old('category_id', $job->category_id) == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descrição</label>
                    <textarea name="description" id="description" rows="4" required
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">{{ old('description', $job->description) }}</textarea>
                </div>

                <div class="mb-4">
                    <label for="requirements" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Requisitos</label>
                    <textarea name="requirements" id="requirements" rows="4"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">{{ old('requirements', $job->requirements) }}</textarea>
                </div>

                <div class="mb-4">
                    <label for="benefits" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Benefícios</label>
                    <textarea name="benefits" id="benefits" rows="4"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">{{ old('benefits', $job->benefits) }}</textarea>
                </div>

                <div class="mb-4">
                    <label for="salary" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Salário</label>
                    <input type="text" name="salary" id="salary" value="{{ old('salary', $job->salary) }}"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="salary_negotiable" class="flex items-center">
                        <input type="checkbox" name="salary_negotiable" id="salary_negotiable" value="1"
                            {{ old('salary_negotiable', $job->salary_negotiable) ? 'checked' : '' }}
                            class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Salário negociável?</span>
                    </label>
                </div>

                <div class="mb-4">
                    <label for="contract_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tipo de Contrato</label>
                    <input type="text" name="contract_type" id="contract_type" value="{{ old('contract_type', $job->contract_type) }}"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="work_mode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Modo de Trabalho</label>
                    <input type="text" name="work_mode" id="work_mode" value="{{ old('work_mode', $job->work_mode) }}"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="experience_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Nível de Experiência</label>
                    <input type="text" name="experience_level" id="experience_level" value="{{ old('experience_level', $job->experience_level) }}"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Localização</label>
                    <input type="text" name="location" id="location" value="{{ old('location', $job->location) }}"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="vacancies" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Número de Vagas</label>
                    <input type="number" name="vacancies" id="vacancies" value="{{ old('vacancies', $job->vacancies) }}"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="application_deadline" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Prazo para Candidatura</label>
                    <input type="date" name="application_deadline" id="application_deadline" value="{{ old('application_deadline', $job->application_deadline?->format('Y-m-d')) }}"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="featured" class="flex items-center">
                        <input type="checkbox" name="featured" id="featured" value="1"
                            {{ old('featured', $job->is_featured) ? 'checked' : '' }}
                            class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Destacar esta vaga?</span>
                    </label>
                </div>

                <div class="mb-4">
                    <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Imagem da Vaga</label>
                    <input type="file" name="image" id="image"
                        class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-zinc-800 dark:border-zinc-700 dark:text-white">
                    @if($job->image)
                        <div class="mt-2">
                            <img src="{{ asset('storage/' . $job->image) }}" alt="Imagem da Vaga" class="w-full h-auto rounded-md">
                        </div>
                    @endif
                </div>

                <div class="flex gap-4">
                    <button type="submit"
                        class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium bg-primary text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        Salvar Alterações
                    </button>
                    <a href="{{ route('job_vacancies.show', $job->slug) }}"
                        class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</x-layouts.app>