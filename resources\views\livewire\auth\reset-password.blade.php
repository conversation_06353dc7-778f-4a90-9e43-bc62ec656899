<?php

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component
{
    #[Locked]
    public string $token = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public bool $isLoading = false;
    public bool $showPassword = false;

    /**
     * Mount the component.
     */
    public function mount(string $token): void
    {
        $this->token = $token;
        $this->email = request()->string('email');
    }

    /**
     * Reset the password for the given user.
     */
    public function resetPassword(): void
    {
        $this->isLoading = true;

        $this->validate([
            'token' => ['required'],
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
        ], [
            'token.required' => 'Token de redefinição é obrigatório.',
            'email.required' => 'O campo email é obrigatório.',
            'email.email' => 'Por favor, insira um endereço de email válido.',
            'password.required' => 'O campo senha é obrigatório.',
            'password.confirmed' => 'A confirmação da senha não confere.',
            'password.min' => 'A senha deve ter pelo menos 8 caracteres.',
        ]);

        try {
            // Here we will attempt to reset the user's password. If it is successful we
            // will update the password on an actual user model and persist it to the
            // database. Otherwise we will parse the error and return the response.
            $status = Password::reset(
                $this->only('email', 'password', 'password_confirmation', 'token'),
                function ($user) {
                    $user->forceFill([
                        'password' => Hash::make($this->password),
                        'remember_token' => Str::random(60),
                    ])->save();

                    event(new PasswordReset($user));
                }
            );

            // If the password was successfully reset, we will redirect the user back to
            // the application's home authenticated view. If there is an error we can
            // redirect them back to where they came from with their error message.
            if ($status != Password::PASSWORD_RESET) {
                $this->addError('email', $this->getPasswordResetErrorMessage($status));
                $this->isLoading = false;
                return;
            }

            Session::flash('status', 'Sua senha foi redefinida com sucesso! Você pode fazer login agora.');
            Session::flash('status_type', 'success');

            $this->redirectRoute('login', navigate: true);
        } catch (\Exception $e) {
            $this->addError('email', 'Ocorreu um erro ao redefinir sua senha. Tente novamente.');
            $this->isLoading = false;
        }
    }

    /**
     * Get user-friendly error message for password reset status
     */
    private function getPasswordResetErrorMessage(string $status): string
    {
        return match($status) {
            Password::INVALID_TOKEN => 'Este link de redefinição de senha é inválido ou expirou.',
            Password::INVALID_USER => 'Não foi possível encontrar um usuário com este endereço de email.',
            Password::RESET_THROTTLED => 'Muitas tentativas de redefinição. Tente novamente em alguns minutos.',
            default => 'Ocorreu um erro ao redefinir sua senha. Tente novamente.',
        };
    }
}; ?>

<div class="flex flex-col gap-6">
    <x-auth-header
        title="Redefinir Senha"
        description="Digite sua nova senha para acessar sua conta"
    />

    <!-- Session Status -->
    @if (session('status'))
        <div class="text-center p-4 rounded-lg {{ session('status_type') === 'success' ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-red-50 text-red-700 border border-red-200' }}">
            <div class="flex items-center justify-center gap-2">
                @if (session('status_type') === 'success')
                    <flux:icon.check-circle class="w-5 h-5" />
                @else
                    <flux:icon.exclamation-circle class="w-5 h-5" />
                @endif
                <span class="font-medium">{{ session('status') }}</span>
            </div>
        </div>
    @endif

    <form wire:submit="resetPassword" class="flex flex-col gap-6">
        <!-- Email Address -->
        <flux:input
            wire:model="email"
            label="Email"
            type="email"
            required
            autocomplete="email"
            :disabled="$isLoading"
            readonly
            class="bg-gray-50"
        />

        <!-- Password -->
        <flux:input
            wire:model="password"
            label="Nova Senha"
            type="{{ $showPassword ? 'text' : 'password' }}"
            required
            autocomplete="new-password"
            placeholder="Digite sua nova senha"
            :disabled="$isLoading"
        >
            <x-slot name="iconTrailing">
                <button
                    type="button"
                    wire:click="$toggle('showPassword')"
                    class="text-zinc-400 hover:text-zinc-600"
                >
                    @if ($showPassword)
                        <flux:icon.eye-slash class="w-5 h-5" />
                    @else
                        <flux:icon.eye class="w-5 h-5" />
                    @endif
                </button>
            </x-slot>
        </flux:input>

        <!-- Confirm Password -->
        <flux:input
            wire:model="password_confirmation"
            label="Confirme a Nova Senha"
            type="{{ $showPassword ? 'text' : 'password' }}"
            required
            autocomplete="new-password"
            placeholder="Digite novamente sua nova senha"
            :disabled="$isLoading"
        />

        <!-- Password Requirements -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
            <div class="flex items-start gap-2">
                <flux:icon.information-circle class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                <div>
                    <p class="font-medium text-blue-700 mb-2">Requisitos da senha:</p>
                    <ul class="text-blue-600 space-y-1">
                        <li class="flex items-center gap-2">
                            <span class="w-1 h-1 bg-blue-400 rounded-full"></span>
                            Mínimo de 8 caracteres
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-1 h-1 bg-blue-400 rounded-full"></span>
                            Pelo menos uma letra maiúscula
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-1 h-1 bg-blue-400 rounded-full"></span>
                            Pelo menos uma letra minúscula
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-1 h-1 bg-blue-400 rounded-full"></span>
                            Pelo menos um número
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <flux:button
            type="submit"
            variant="primary"
            class="w-full"
            :disabled="$isLoading"
        >
            @if ($isLoading)
                <flux:icon.arrow-path class="w-4 h-4 animate-spin mr-2" />
                Redefinindo...
            @else
                <flux:icon.key class="w-4 h-4 mr-2" />
                Redefinir Senha
            @endif
        </flux:button>
    </form>

    <div class="text-center text-sm text-zinc-400">
        <flux:link :href="route('login')" wire:navigate class="text-zinc-600 hover:text-zinc-800 font-medium">
            ← Voltar para o login
        </flux:link>
    </div>
</div>
