<x-layouts.app :title="__('Pedido #' . $order->id)">
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-white dark:bg-zinc-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
            <h1 class="text-2xl font-bold text-title mb-2">Pedido #{{ $order->id }}</h1>
            <p class="text-body-light mb-4">Data: {{ $order->created_at->format('d/m/Y H:i') }}</p>

            @if(session('info'))
                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('info') }}</span>
                </div>
            @endif
            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            <div class="mb-6">
                <h2 class="text-xl font-semibold text-title mb-3">Resumo do Pedido</h2>
                <div class="border-b border-gray-200 dark:border-gray-700">
                    @foreach($order->items as $item)
                    <div class="flex items-center justify-between py-3">
                        <div class="flex items-center">
                            <img src="{{ $item->product->getImageUrl() ?? 'https://placehold.co/100' }}" alt="{{ $item->product->name }}" class="w-16 h-16 object-cover rounded mr-4">
                            <div>
                                <p class="font-semibold text-title">{{ $item->product->name }}</p>
                                <p class="text-sm text-body-light">Qtd: {{ $item->quantity }}</p>
                            </div>
                        </div>
                        <p class="text-title font-semibold">R$ {{ number_format($item->price * $item->quantity, 2, ',', '.') }}</p>
                    </div>
                    @endforeach
                </div>
                <div class="py-3 space-y-2">
                    <div class="flex justify-between">
                        <span class="text-body-light">Subtotal</span>
                        <span class="font-semibold text-title">R$ {{ number_format($order->total, 2, ',', '.') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-body-light">Desconto</span>
                        <span class="font-semibold text-success">- R$ {{ number_format($order->discount, 2, ',', '.') }}</span>
                    </div>
                    <div class="flex justify-between text-lg font-bold text-title">
                        <span>Total</span>
                        <span>R$ {{ number_format($order->total - $order->discount, 2, ',', '.') }}</span>
                    </div>
                </div>
            </div>

            <div>
                <h2 class="text-xl font-semibold text-title mb-3">Status do Pagamento</h2>
                @if($order->status === 'pending' || $order->status === 'failed')
                    <p class="text-warning">Seu pagamento está pendente. Por favor, conclua a ação abaixo.</p>
                    <div class="mt-4">
                        <a href="{{ route('shop.checkout') }}" class="w-full text-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md">
                            Tentar Pagar Novamente
                        </a>
                    </div>
                @else
                     <p class="text-success">Seu pagamento foi confirmado!</p>
                @endif
            </div>

        </div>
    </div>
</div>
</x-layouts.app>