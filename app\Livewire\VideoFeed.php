<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Post;
use App\Models\Like;
use App\Models\Comment;
use App\Models\Notification;
use App\Models\UserPoint;
use App\Services\ContentProcessor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http; // Added for API calls
use Illuminate\Support\Facades\Log;  // For logging errors

class VideoFeed extends Component
{
    public $posts = [];
    public $currentIndex = 0;

    // --- Autocomplete Properties for VideoFeed Comments ---
    public $activeCommentPostId = null; // To track which post's comment input is active

    public $hashtagQuery = '';
    public $hashtagSuggestions = [];
    public $showHashtagSuggestions = false;
    protected $currentHashtagStartPosition = null;

    public $mentionQuery = '';
    public $mentionSuggestions = [];
    public $showMentionSuggestions = false;
    protected $currentMentionStartPosition = null;
    // --- End Autocomplete Properties ---

    public function mount()
    {
        $this->loadPosts();
    }

    public function loadPosts()
    {
        // Carregar posts com vídeos
        $posts = Post::whereNotNull('video')
            ->whereNull('group_id') // Exclui postagens de grupos do feed principal
            ->with(['user', 'user.userPhotos', 'comments.user.userPhotos'])
            ->latest()
            ->get()
            ->map(function ($post) {
                return [
                    'id' => $post->id,
                    'type' => 'post',
                    'video' => Storage::url($post->video),
                    'title' => $post->title ?? '',
                    'content' => ContentProcessor::processContent($post->content ?? ''), // Processed content
                    'user' => [
                        'id' => $post->user->id ?? null,
                        'name' => $post->user->name ?? 'Usuário',
                        'username' => $post->user->username ?? '',
                        'avatar' => $post->user->userPhotos->first()
                            ? Storage::url($post->user->userPhotos->first()->photo_path)
                            : asset('images/default-avatar.jpg')
                    ],
                    'likes_count' => $post->likes()->count(),
                    'comments_count' => $post->comments()->count() ?? 0,
                    'liked_by_user' => Auth::check() && $post->likes()->where('user_id', Auth::id())->exists(),
                    'showComments' => false,
                    'comments' => $post->comments()->with(['user.userPhotos'])->latest()->take(5)->get()->map(function ($comment) {
                        return [
                            'id' => $comment->id,
                            'body' => ContentProcessor::processContent($comment->body ?? ''), // Processed comment body
                            'created_at' => $comment->created_at->diffForHumans(),
                            'user' => [
                                'id' => $comment->user->id,
                                'name' => $comment->user->name,
                                'username' => $comment->user->username,
                                'avatar' => $comment->user->userPhotos->first()
                                    ? Storage::url($comment->user->userPhotos->first()->photo_path)
                                    : asset('images/default-avatar.jpg')
                            ]
                        ];
                    })->toArray(),
                    'created_at' => $post->created_at
                ];
            });

        // Ordenar posts por data
        $this->posts = $posts->sortByDesc('created_at')
            ->values()
            ->toArray();
    }

    public function likePost($postId)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $post = Post::find($postId);

        if (!$post) {
            return;
        }

        $isLiked = $post->likes()->where('user_id', Auth::id())->exists();

        if ($isLiked) {
            // Remover o like
            $post->likes()->where('user_id', Auth::id())->delete();

            // Remove notificação
            Notification::where([
                'sender_id' => Auth::id(),
                'post_id' => $post->id,
                'type' => 'like'
            ])->delete();

            // Remove pontos (apenas se o post não for do próprio usuário)
            if ($post->user_id !== Auth::id()) {
                UserPoint::removePoints(
                    $post->user_id,
                    'like',
                    5,
                    "Perdeu curtida de " . Auth::user()->name,
                    $post->id,
                    Post::class
                );
            }

            $likesCount = $post->likes()->count();
            $isLiked = false;
        } else {
            // Adicionar o like
            $like = new Like();
            $like->user_id = Auth::id();
            $like->post_id = $postId;
            $like->save();

            // Adiciona pontos ao usuário que curtiu (recompensa por engajamento)
            UserPoint::addPoints(
                Auth::id(),
                'like',
                2,
                "Curtiu postagem de " . ($post->user_id === Auth::id() ? "sua autoria" : $post->user->name),
                $post->id,
                Post::class
            );

            // Adiciona pontos ao autor do post (se não for o mesmo usuário)
            if ($post->user_id !== Auth::id()) {
                UserPoint::addPoints(
                    $post->user_id,
                    'like_received',
                    5,
                    "Recebeu curtida de " . Auth::user()->name,
                    $post->id,
                    Post::class
                );

                // Cria notificação se não for post próprio
                Notification::create([
                    'user_id' => $post->user_id,
                    'sender_id' => Auth::id(),
                    'type' => 'like',
                    'post_id' => $post->id
                ]);
            }

            // Dispara animação de recompensa
            $this->dispatch('reward-earned', points: 2);

            $likesCount = $post->likes()->count();
            $isLiked = true;
        }

        // Atualizar o estado do post imediatamente
        foreach ($this->posts as $index => $p) {
            if ($p['id'] == $postId) {
                $this->posts[$index]['likes_count'] = $likesCount;
                $this->posts[$index]['liked_by_user'] = $isLiked;
                break;
            }
        }

        // Disparar evento para atualizar a UI
        $this->dispatch('postLiked', [
            'postId' => $postId,
            'likesCount' => $likesCount,
            'isLiked' => $isLiked
        ]);
    }

    public function addComment($postId, $commentText)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        if (empty(trim($commentText))) {
            return;
        }

        $post = Post::find($postId);

        if (!$post) {
            return;
        }

        $comment = Comment::create([
            'user_id' => Auth::id(),
            'post_id' => $postId,
            'body' => $commentText
        ]);

        // Processar hashtags para o comentário
        if ($comment) {
            ContentProcessor::processHashtags($commentText, $comment);
            ContentProcessor::processMentions($commentText, $comment, Auth::id()); // Add this line
        }

        // Adiciona pontos ao usuário que comentou
        UserPoint::addPoints(
            Auth::id(),
            'comment',
            5,
            "Comentou na postagem de " . ($post->user_id === Auth::id() ? "sua autoria" : $post->user->name),
            $comment->id,
            Comment::class
        );

        // Adiciona pontos ao autor do post (se não for o mesmo usuário)
        if ($post->user_id !== Auth::id()) {
            UserPoint::addPoints(
                $post->user_id,
                'comment_received',
                3,
                "Recebeu comentário de " . Auth::user()->name,
                $comment->id,
                Comment::class
            );

            // Cria notificação para o autor do post
            Notification::create([
                'user_id' => $post->user_id,
                'sender_id' => Auth::id(),
                'type' => 'comment',
                'post_id' => $post->id,
                'comment_id' => $comment->id
            ]);
        }

        // Dispara animação de recompensa
        $this->dispatch('reward-earned', points: 5);

        // Atualizar a lista de comentários do post
        $newComment = [
            'id' => $comment->id,
            'body' => ContentProcessor::processContent($comment->body ?? ''), // Processed new comment body
            'created_at' => $comment->created_at->diffForHumans(),
            'user' => [
                'id' => Auth::user()->id,
                'name' => Auth::user()->name,
                'username' => Auth::user()->username,
                'avatar' => Auth::user()->userPhotos->first()
                    ? Storage::url(Auth::user()->userPhotos->first()->photo_path)
                    : asset('images/default-avatar.jpg')
            ]
        ];

        foreach ($this->posts as $index => $p) {
            if ($p['id'] == $postId) {
                // Adicionar o novo comentário no início da lista
                array_unshift($this->posts[$index]['comments'], $newComment);
                // Atualizar o contador de comentários
                $this->posts[$index]['comments_count']++;
                break;
            }
        }

        // Disparar evento para atualizar a UI
        $this->dispatch('commentAdded', [
            'postId' => $postId,
            'comment' => $newComment,
            'commentsCount' => $post->comments()->count()
        ]);
    }

    // --- Autocomplete Methods for VideoFeed Comments ---

    public function setActiveCommentPostId($postId)
    {
        if ($this->activeCommentPostId !== $postId) {
            $this->resetAutocompleteState(); // Reset if switching to a new comment box
        }
        $this->activeCommentPostId = $postId;
    }

    public function handleCommentInput($postId, $currentCommentText)
    {
        // Ensure we're handling input for the currently active comment box
        if ($this->activeCommentPostId !== $postId) {
            // If a different comment box became active without focus event, update context
            // Or, this might indicate a stale call, so we could ignore or reset.
            // For simplicity, let's assume setActiveCommentPostId was called on focus.
            $this->resetAutocompleteState();
            $this->activeCommentPostId = $postId; // Or simply return if strict context matching is desired
        }

        $this->showHashtagSuggestions = false;
        $this->showMentionSuggestions = false;

        // Check for Hashtag: #tag
        if (preg_match('/(?<=\s|^)\#([a-zA-Z0-9_]*)$/', $currentCommentText, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentHashtagStartPosition = $matches[0][1];
            $this->hashtagQuery = $matches[1][0]; // Triggers updatedHashtagQuery
            $this->mentionQuery = '';
            $this->mentionSuggestions = [];
            return;
        } else {
            if (!empty($this->hashtagQuery)) {
                $this->hashtagQuery = '';
                $this->hashtagSuggestions = [];
            }
        }

        // Check for Mention: @user
        if (preg_match('/(?<=\s|^)\@([a-zA-Z0-9_.]*)$/', $currentCommentText, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentMentionStartPosition = $matches[0][1];
            $this->mentionQuery = $matches[1][0]; // Triggers updatedMentionQuery
            $this->hashtagQuery = '';
            $this->hashtagSuggestions = [];
            return;
        } else {
            if (!empty($this->mentionQuery)) {
                $this->mentionQuery = '';
                $this->mentionSuggestions = [];
            }
        }
    }

    public function updatedHashtagQuery($query)
    {
        if ($this->activeCommentPostId === null) return; // Only proceed if a comment box is active

        if (strlen($query) >= 1) {
            try {
                $response = Http::get(url('/api/hashtags/search'), ['query' => $query, 'limit' => 5]);
                if ($response->successful()) {
                    $this->hashtagSuggestions = $response->json();
                    $this->showHashtagSuggestions = !empty($this->hashtagSuggestions);
                } else {
                    $this->hashtagSuggestions = [];
                    $this->showHashtagSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling hashtag API in VideoFeed: ' . $e->getMessage());
                $this->hashtagSuggestions = [];
                $this->showHashtagSuggestions = false;
            }
        } else {
            $this->hashtagSuggestions = [];
            $this->showHashtagSuggestions = false;
        }
    }

    public function updatedMentionQuery($query)
    {
        if ($this->activeCommentPostId === null) return; // Only proceed if a comment box is active

        if (strlen($query) >= 1) {
            try {
                $response = Http::get(url('/api/users/search'), ['query' => $query, 'limit' => 5]);
                if ($response->successful()) {
                    $this->mentionSuggestions = $response->json();
                    $this->showMentionSuggestions = !empty($this->mentionSuggestions);
                } else {
                    $this->mentionSuggestions = [];
                    $this->showMentionSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling user API in VideoFeed: ' . $e->getMessage());
                $this->mentionSuggestions = [];
                $this->showMentionSuggestions = false;
            }
        } else {
            $this->mentionSuggestions = [];
            $this->showMentionSuggestions = false;
        }
    }

    public function selectHashtagForPost($postId, $currentCommentText, $selectedHashtagName)
    {
        if ($this->currentHashtagStartPosition === null || $this->activeCommentPostId !== $postId) return;

        $textBeforeHashtag = substr($currentCommentText, 0, $this->currentHashtagStartPosition);
        $originalHashtagLength = 1 + strlen($this->hashtagQuery);
        $textAfterOriginalHashtag = substr($currentCommentText, $this->currentHashtagStartPosition + $originalHashtagLength);
        $updatedText = $textBeforeHashtag . '#' . $selectedHashtagName . ' ' . $textAfterOriginalHashtag;

        $this->dispatch("update-comment-text-{$postId}", text: $updatedText);
        $this->resetAutocompleteState();
    }

    public function selectMentionForPost($postId, $currentCommentText, $selectedUsername)
    {
        if ($this->currentMentionStartPosition === null || $this->activeCommentPostId !== $postId) return;

        $textBeforeMention = substr($currentCommentText, 0, $this->currentMentionStartPosition);
        $originalMentionLength = 1 + strlen($this->mentionQuery);
        $textAfterOriginalMention = substr($currentCommentText, $this->currentMentionStartPosition + $originalMentionLength);
        $updatedText = $textBeforeMention . '@' . $selectedUsername . ' ' . $textAfterOriginalMention;

        $this->dispatch("update-comment-text-{$postId}", text: $updatedText);
        $this->resetAutocompleteState();
    }

    protected function resetAutocompleteState()
    {
        $this->hashtagQuery = '';
        $this->hashtagSuggestions = [];
        $this->showHashtagSuggestions = false;
        $this->currentHashtagStartPosition = null;
        $this->mentionQuery = '';
        $this->mentionSuggestions = [];
        $this->showMentionSuggestions = false;
        $this->currentMentionStartPosition = null;
        // $this->activeCommentPostId = null; // Keep active post ID until focus changes
    }

    public function closeSuggestions()
    {
        $this->showHashtagSuggestions = false;
        $this->showMentionSuggestions = false;
    }

    public function deleteComment($postId, $commentId)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $comment = Comment::find($commentId);

        if (!$comment || $comment->user_id !== Auth::id()) {
            return;
        }

        // Remove pontos do usuário que comentou
        UserPoint::removePoints(
            Auth::id(),
            'comment',
            5,
            "Comentário removido",
            $comment->id,
            Comment::class
        );

        // Remove pontos do autor do post (se não for o mesmo usuário)
        if ($comment->post->user_id !== Auth::id()) {
            UserPoint::removePoints(
                $comment->post->user_id,
                'comment_received',
                3,
                "Comentário removido",
                $comment->id,
                Comment::class
            );
        }

        // Remove a notificação associada ao comentário
        Notification::where([
            'sender_id' => Auth::id(),
            'post_id' => $postId,
            'type' => 'comment'
        ])->delete();

        // Deleta o comentário
        $comment->delete();

        // Atualiza o contador de comentários do post
        $post = Post::find($postId);
        if ($post) {
            $commentsCount = $post->comments()->count();

            // Atualiza o estado do post
            foreach ($this->posts as $index => $p) {
                if ($p['id'] == $postId) {
                    $this->posts[$index]['comments_count'] = $commentsCount;
                    // Remove o comentário da lista
                    $this->posts[$index]['comments'] = array_filter($this->posts[$index]['comments'], function ($c) use ($commentId) {
                        return $c['id'] != $commentId;
                    });
                    break;
                }
            }
        }
    }

    public function editComment($postId, $commentId, $newBody)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $comment = Comment::find($commentId);

        if (!$comment || $comment->user_id !== Auth::id()) {
            return;
        }

        if (empty(trim($newBody))) {
            return;
        }

        // Atualiza o comentário
        $comment->body = $newBody;
        $comment->save();

        // Processa hashtags e menções novamente
        ContentProcessor::processHashtags($newBody, $comment);
        ContentProcessor::processMentions($newBody, $comment, Auth::id());

        // Atualiza o comentário na lista
        foreach ($this->posts as $index => $post) {
            if ($post['id'] == $postId) {
                foreach ($post['comments'] as $commentIndex => $c) {
                    if ($c['id'] == $commentId) {
                        $this->posts[$index]['comments'][$commentIndex]['body'] = ContentProcessor::processContent($newBody);
                        break;
                    }
                }
                break;
            }
        }
    }

    // --- End Autocomplete Methods ---

    public function render()
    {
        return view('livewire.video-feed');
    }
}
