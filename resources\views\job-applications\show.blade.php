<x-layouts.app :title="'Candidatura #' . $application->id">
    <div class="p-6">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex items-center gap-3 mb-2">
                <flux:button :href="route('job_vacancies.my-applications')" variant="ghost" size="sm" wire:navigate>
                    <flux:icon icon="arrow-left" class="w-4 h-4 mr-1" />
                    Voltar
                </flux:button>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                <flux:icon icon="clipboard-document-list" class="w-6 h-6" />
                Candidatura #{{ $application->id }}
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Detalhes da sua candidatura para a vaga</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Job Information -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <flux:icon icon="briefcase" class="w-5 h-5" />
                            Informações da Vaga
                        </h2>
                        
                        <div class="space-y-4">
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                    <a href="{{ route('job_vacancies.show', $application->job->slug) }}" 
                                       class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                                       wire:navigate>
                                        {{ $application->job->title }}
                                    </a>
                                </h3>
                                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                                    <div class="flex items-center gap-1">
                                        <flux:icon icon="building-office" class="w-4 h-4" />
                                        <span>{{ $application->job->category->name ?? 'Categoria não definida' }}</span>
                                    </div>
                                    @if($application->job->location)
                                        <div class="flex items-center gap-1">
                                            <flux:icon icon="map-pin" class="w-4 h-4" />
                                            <span>{{ $application->job->location }}</span>
                                        </div>
                                    @endif
                                    <div class="flex items-center gap-1">
                                        <flux:icon icon="clock" class="w-4 h-4" />
                                        <span>{{ $application->job->work_mode }}</span>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <flux:icon icon="document-text" class="w-4 h-4" />
                                        <span>{{ $application->job->contract_type }}</span>
                                    </div>
                                </div>
                            </div>

                            @if($application->job->salary_min || $application->job->salary_max || $application->job->salary_negotiable)
                                <div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Salário</div>
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ $application->job->formatted_salary }}
                                    </div>
                                </div>
                            @endif

                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">Descrição da Vaga</div>
                                <div class="text-gray-700 dark:text-gray-300 prose prose-sm max-w-none">
                                    {!! nl2br(e($application->job->description)) !!}
                                </div>
                            </div>

                            @if($application->job->requirements)
                                <div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">Requisitos</div>
                                    <div class="text-gray-700 dark:text-gray-300 prose prose-sm max-w-none">
                                        {!! nl2br(e($application->job->requirements)) !!}
                                    </div>
                                </div>
                            @endif

                            @if($application->job->benefits)
                                <div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">Benefícios</div>
                                    <div class="text-gray-700 dark:text-gray-300 prose prose-sm max-w-none">
                                        {!! nl2br(e($application->job->benefits)) !!}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Application Details -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <flux:icon icon="user" class="w-5 h-5" />
                            Sua Candidatura
                        </h2>

                        @if($application->cover_letter)
                            <div class="mb-6">
                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">Carta de Apresentação</div>
                                <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4">
                                    <div class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ $application->cover_letter }}</div>
                                </div>
                            </div>
                        @endif

                        @if($application->resume_path)
                            <div class="mb-6">
                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">Currículo Enviado</div>
                                <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <flux:icon icon="document-text" class="w-5 h-5 text-gray-400" />
                                            <span class="text-gray-900 dark:text-white font-medium">{{ $application->resume_filename }}</span>
                                        </div>
                                        <flux:button 
                                            :href="route('job_vacancies.application.resume', $application)" 
                                            size="sm" 
                                            variant="ghost">
                                            <flux:icon icon="arrow-down-tray" class="w-4 h-4 mr-1" />
                                            Download
                                        </flux:button>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($application->admin_notes)
                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">Observações da Empresa</div>
                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                                    <div class="text-blue-800 dark:text-blue-200 whitespace-pre-wrap">{{ $application->admin_notes }}</div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Status Card -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Status da Candidatura</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Status Atual</div>
                                @php
                                    $statusColors = [
                                        'Pendente' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                        'Em Análise' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                        'Aprovada' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'Rejeitada' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                        'Contratada' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                                    ];
                                @endphp
                                <div class="mt-1">
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $statusColors[$application->status] ?? 'bg-gray-100 text-gray-800' }}">
                                        {{ $application->status }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Prioridade</div>
                                <div class="mt-1">
                                    @if($application->is_vip_priority)
                                        <span class="inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300">
                                            <flux:icon icon="star" class="w-4 h-4 mr-1" />
                                            VIP
                                        </span>
                                    @else
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            Regular
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Data da Candidatura</div>
                                <div class="font-medium text-gray-900 dark:text-white">
                                    {{ $application->created_at->format('d/m/Y H:i') }}
                                </div>
                            </div>

                            @if($application->reviewed_at)
                                <div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Última Atualização</div>
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ $application->reviewed_at->format('d/m/Y H:i') }}
                                    </div>
                                    @if($application->reviewer)
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            por {{ $application->reviewer->name }}
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Ações</h3>
                        
                        <div class="space-y-3">
                            <flux:button 
                                :href="route('job_vacancies.show', $application->job->slug)" 
                                variant="outline" 
                                class="w-full"
                                wire:navigate>
                                <flux:icon icon="eye" class="w-4 h-4 mr-2" />
                                Ver Vaga Completa
                            </flux:button>

                            @if($application->status === 'Pendente')
                                <flux:button 
                                    wire:click="cancelApplication"
                                    variant="outline" 
                                    class="w-full text-red-600 hover:text-red-800 border-red-300 hover:border-red-400"
                                    onclick="return confirm('Tem certeza que deseja cancelar esta candidatura?')">
                                    <flux:icon icon="x-mark" class="w-4 h-4 mr-2" />
                                    Cancelar Candidatura
                                </flux:button>
                            @endif

                            <flux:button 
                                :href="route('job_vacancies.index')" 
                                variant="primary" 
                                class="w-full"
                                wire:navigate>
                                <flux:icon icon="briefcase" class="w-4 h-4 mr-2" />
                                Ver Outras Vagas
                            </flux:button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
