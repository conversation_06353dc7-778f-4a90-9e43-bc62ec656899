<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gerenciar Candidaturas</h1>
        <p class="text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON><PERSON> to<PERSON> as candidaturas às vagas de emprego</p>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
            {{ session('error') }}
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-6">
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $statusStats['total'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $statusStats['pending'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Pendentes</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $statusStats['in_review'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Em Análise</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $statusStats['approved'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Aprovadas</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $statusStats['rejected'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Rejeitadas</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $statusStats['hired'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Contratadas</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
            <div class="text-2xl font-bold text-pink-600 dark:text-pink-400">{{ $statusStats['vip'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">VIP</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Buscar</label>
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="Nome, email, vaga..."
                        icon="magnifying-glass"
                    />
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                    <flux:select wire:model.live="statusFilter">
                        <option value="">Todos os status</option>
                        <option value="Pendente">Pendente</option>
                        <option value="Em Análise">Em Análise</option>
                        <option value="Aprovada">Aprovada</option>
                        <option value="Rejeitada">Rejeitada</option>
                        <option value="Contratada">Contratada</option>
                    </flux:select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Vaga</label>
                    <flux:select wire:model.live="jobFilter">
                        <option value="">Todas as vagas</option>
                        @foreach($jobs as $job)
                            <option value="{{ $job->id }}">{{ $job->title }}</option>
                        @endforeach
                    </flux:select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Prioridade</label>
                    <flux:select wire:model.live="priorityFilter">
                        <option value="">Todas</option>
                        <option value="vip">VIP</option>
                        <option value="regular">Regular</option>
                    </flux:select>
                </div>

                <div class="flex items-end">
                    <flux:button wire:click="$set('search', '')" variant="ghost" size="sm" class="w-full">
                        <flux:icon icon="x-mark" class="w-4 h-4 mr-1" />
                        Limpar Filtros
                    </flux:button>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Table -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-zinc-700">
                    <tr>
                        <th wire:click="sortBy('id')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>ID</span>
                                @if($sortBy === 'id')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th wire:click="sortBy('user_id')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>Candidato</span>
                                @if($sortBy === 'user_id')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th wire:click="sortBy('job_id')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>Vaga</span>
                                @if($sortBy === 'job_id')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th wire:click="sortBy('status')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>Status</span>
                                @if($sortBy === 'status')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Prioridade
                        </th>
                        <th wire:click="sortBy('created_at')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            <div class="flex items-center space-x-1">
                                <span>Data</span>
                                @if($sortBy === 'created_at')
                                    <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($applications as $application)
                        <tr class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                #{{ $application->id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @php
                                            $currentPhoto = $application->user->userPhotos()->where('is_current', true)->first();
                                            $avatarUrl = $currentPhoto ? Storage::url($currentPhoto->photo_path) : asset('images/users/avatar.svg');
                                        @endphp
                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ $avatarUrl }}" alt="{{ $application->user->name }}">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $application->user->name }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $application->user->email }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 dark:text-white font-medium">
                                    {{ $application->job->title }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $application->job->category->name ?? 'Sem categoria' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $statusColors = [
                                        'Pendente' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                        'Em Análise' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                        'Aprovada' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'Rejeitada' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                        'Contratada' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                                    ];
                                @endphp
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $statusColors[$application->status] ?? 'bg-gray-100 text-gray-800' }}">
                                    {{ $application->status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($application->is_vip_priority)
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300">
                                        <flux:icon icon="star" class="w-3 h-3 mr-1" />
                                        VIP
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        Regular
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                <div>{{ $application->created_at->format('d/m/Y') }}</div>
                                <div class="text-xs">{{ $application->created_at->format('H:i') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <flux:button wire:click="viewApplication({{ $application->id }})" size="sm" variant="ghost">
                                        <flux:icon icon="eye" class="w-4 h-4" />
                                    </flux:button>

                                    @if($application->resume_path)
                                        <flux:button wire:click="downloadResume({{ $application->id }})" size="sm" variant="ghost" class="text-blue-600 hover:text-blue-800">
                                            <flux:icon icon="document-arrow-down" class="w-4 h-4" />
                                        </flux:button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <flux:icon icon="document-text" class="w-12 h-12 text-gray-400 dark:text-gray-600 mb-4" />
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhuma candidatura encontrada</h3>
                                    <p class="text-gray-500 dark:text-gray-400">Não há candidaturas que correspondam aos filtros aplicados.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($applications->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $applications->links() }}
            </div>
        @endif
    </div>

    <!-- Application Detail Modal -->
    @if($showModal && $selectedApplication)
        <flux:modal wire:model="showModal" class="max-w-4xl">
            <flux:modal.header>
                <flux:heading size="lg">Candidatura #{{ $selectedApplication->id }}</flux:heading>
            </flux:modal.header>

            <div class="space-y-6">
                <!-- Application Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Informações do Candidato</h3>
                            <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4 space-y-3">
                                <div class="flex items-center space-x-3">
                                    @php
                                        $currentPhoto = $selectedApplication->user->userPhotos()->where('is_current', true)->first();
                                        $avatarUrl = $currentPhoto ? Storage::url($currentPhoto->photo_path) : asset('images/users/avatar.svg');
                                    @endphp
                                    <img class="h-12 w-12 rounded-full object-cover" src="{{ $avatarUrl }}" alt="{{ $selectedApplication->user->name }}">
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-white">{{ $selectedApplication->user->name }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ $selectedApplication->user->email }}</div>
                                    </div>
                                </div>

                                @if($selectedApplication->user->city)
                                    <div class="text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Localização:</span>
                                        <span class="text-gray-900 dark:text-white">{{ $selectedApplication->user->city->name }}, {{ $selectedApplication->user->state->name }}</span>
                                    </div>
                                @endif

                                @if($selectedApplication->is_vip_priority)
                                    <div class="flex items-center">
                                        <flux:icon icon="star" class="w-4 h-4 text-pink-500 mr-1" />
                                        <span class="text-sm font-medium text-pink-600 dark:text-pink-400">Candidatura VIP</span>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Informações da Vaga</h3>
                            <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4 space-y-2">
                                <div class="font-medium text-gray-900 dark:text-white">{{ $selectedApplication->job->title }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ $selectedApplication->job->category->name ?? 'Sem categoria' }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ $selectedApplication->job->location }}</div>
                                @if($selectedApplication->job->salary_min || $selectedApplication->job->salary_max)
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        Salário: {{ $selectedApplication->job->formatted_salary }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Status da Candidatura</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                                    <flux:select wire:model="newStatus">
                                        <option value="Pendente">Pendente</option>
                                        <option value="Em Análise">Em Análise</option>
                                        <option value="Aprovada">Aprovada</option>
                                        <option value="Rejeitada">Rejeitada</option>
                                        <option value="Contratada">Contratada</option>
                                    </flux:select>
                                </div>

                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <div>Candidatura enviada em: {{ $selectedApplication->created_at->format('d/m/Y H:i') }}</div>
                                    @if($selectedApplication->reviewed_at)
                                        <div>Última revisão: {{ $selectedApplication->reviewed_at->format('d/m/Y H:i') }}</div>
                                        @if($selectedApplication->reviewer)
                                            <div>Revisado por: {{ $selectedApplication->reviewer->name }}</div>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if($selectedApplication->resume_path)
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Currículo</h3>
                                <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <flux:icon icon="document-text" class="w-5 h-5 text-gray-400" />
                                            <span class="text-sm text-gray-900 dark:text-white">{{ $selectedApplication->resume_filename }}</span>
                                        </div>
                                        <flux:button wire:click="downloadResume({{ $selectedApplication->id }})" size="sm" variant="ghost">
                                            <flux:icon icon="arrow-down-tray" class="w-4 h-4 mr-1" />
                                            Download
                                        </flux:button>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Cover Letter -->
                @if($selectedApplication->cover_letter)
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Carta de Apresentação</h3>
                        <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4">
                            <p class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ $selectedApplication->cover_letter }}</p>
                        </div>
                    </div>
                @endif

                <!-- Admin Notes -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Notas Administrativas</h3>
                    <flux:textarea
                        wire:model="adminNotes"
                        placeholder="Adicione suas observações sobre esta candidatura..."
                        rows="4"
                        class="w-full"
                    />
                    @if($selectedApplication->admin_notes && $selectedApplication->admin_notes !== $adminNotes)
                        <div class="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">Notas anteriores:</div>
                            <div class="text-sm text-blue-700 dark:text-blue-300">{{ $selectedApplication->admin_notes }}</div>
                        </div>
                    @endif
                </div>
            </div>

            <flux:modal.footer>
                <div class="flex justify-between w-full">
                    <flux:button wire:click="closeModal" variant="ghost">
                        Cancelar
                    </flux:button>
                    <flux:button wire:click="updateApplication" variant="primary">
                        <flux:icon icon="check" class="w-4 h-4 mr-1" />
                        Salvar Alterações
                    </flux:button>
                </div>
            </flux:modal.footer>
        </flux:modal>
    @endif
</div>
