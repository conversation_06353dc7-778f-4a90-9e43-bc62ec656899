# Tooltips Customizados - Documentação

## Visão Geral

Os tooltips do projeto foram customizados para ter **fundo preto** e **letras brancas maiores**, proporcionando melhor legibilidade e uma aparência mais moderna.

## Características dos Tooltips Customizados

### Estilo Visual
- **Fundo**: Preto (#000000)
- **Texto**: Branco (#ffffff)
- **<PERSON><PERSON><PERSON> da fonte**: `text-sm` (14px) em vez de `text-xs` (12px)
- **Padding**: `py-3 px-4` em vez de `py-2 px-2.5`
- **Largura mínima**: 120px
- **Largura máxima**: 300px
- **Borda**: Cinza escuro com sombra
- **Z-index**: 9999 (sempre no topo)
- **Border radius**: `rounded-lg`

### Funcionalidades
- Quebra automática de linha para textos longos
- Suporte a atalhos de teclado (kbd)
- Posicionamento flexível (top, bottom, left, right)
- Modo interativo e clicável
- Responsivo e acessível

## Como Usar

### 1. Tooltip Básico
```blade
<flux:tooltip content="Texto do tooltip">
    <flux:button>Hover aqui</flux:button>
</flux:tooltip>
```

### 2. Tooltip com Posição Específica
```blade
<flux:tooltip content="Tooltip no topo" position="top">
    <flux:button>Botão</flux:button>
</flux:tooltip>

<flux:tooltip content="Tooltip à direita" position="right">
    <flux:button>Botão</flux:button>
</flux:tooltip>
```

**Posições disponíveis:**
- `top` (padrão)
- `bottom`
- `left`
- `right`

### 3. Tooltip com Atalho de Teclado
```blade
<flux:tooltip content="Salvar documento" kbd="Ctrl+S">
    <flux:button icon="document-arrow-down">Salvar</flux:button>
</flux:tooltip>
```

### 4. Tooltip Clicável (Toggleable)
```blade
<flux:tooltip content="Clique para mais informações" toggleable>
    <flux:button>Informações</flux:button>
</flux:tooltip>
```

### 5. Tooltip Interativo
```blade
<flux:tooltip content="Tooltip que permite interação" interactive>
    <flux:button>Interativo</flux:button>
</flux:tooltip>
```

### 6. Tooltip em Ícones
```blade
<flux:tooltip content="Página inicial" position="bottom">
    <button class="p-2 text-gray-600 hover:text-gray-900">
        <x-flux::icon name="home" class="w-6 h-6" />
    </button>
</flux:tooltip>
```

## Exemplos Práticos no Projeto

### Sidebar Navigation
```blade
<flux:tooltip content="Buscar usuários e conteúdo no sistema" position="right">
    <a href="{{ route('busca') }}" class="sidebar-link">
        <x-flux::icon name="magnifying-glass" class="w-5 h-5" />
        <span class="lg:block hidden">Busca</span>
    </a>
</flux:tooltip>
```

### Status de Usuário
```blade
<flux:tooltip content="Usuário online - Última atividade: agora" position="top">
    <div class="relative">
        <img src="{{ $user->avatar }}" class="w-10 h-10 rounded-full">
        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
    </div>
</flux:tooltip>
```

### Botões de Ação
```blade
<flux:tooltip content="Enviar mensagem privada" position="top">
    <flux:button variant="outline" size="sm" icon="chat-bubble-left">
        Mensagem
    </flux:button>
</flux:tooltip>
```

### Campos de Formulário
```blade
<flux:tooltip content="Seu nome completo como aparece no perfil" position="top">
    <flux:field>
        <flux:label>Nome Completo</flux:label>
        <flux:input placeholder="Digite seu nome completo" />
    </flux:field>
</flux:tooltip>
```

## Customizações Avançadas

### Tooltip Maior para Textos Longos
Para textos muito longos, você pode adicionar a classe `tooltip-large`:

```blade
<flux:tooltip content="Este é um texto muito longo que precisa de mais espaço para ser exibido adequadamente">
    <flux:button>Texto longo</flux:button>
</flux:tooltip>
```

A classe `tooltip-large` aplica:
- `text-base` (16px)
- `py-4 px-5`
- Largura mínima: 200px
- Largura máxima: 400px

### Tooltip com Conteúdo HTML
```blade
<flux:tooltip>
    <flux:tooltip.content>
        <div class="space-y-1">
            <div class="font-semibold">Título do Tooltip</div>
            <div class="text-sm">Descrição detalhada aqui</div>
        </div>
    </flux:tooltip.content>
    
    <flux:button>Hover para HTML</flux:button>
</flux:tooltip>
```

## Implementação Técnica

### CSS Customizado
Os estilos estão definidos em `resources/css/app.css`:

```css
/* Tooltip do Flux UI - Customização */
[data-flux-tooltip-content] {
    @apply !bg-black !text-white !text-sm !py-3 !px-4 !font-medium;
    @apply !border !border-gray-600 !shadow-xl;
    @apply !rounded-lg;
    min-width: 120px !important;
    max-width: 300px !important;
    line-height: 1.4 !important;
    z-index: 9999 !important;
}
```

### Seletores CSS Utilizados
- `[data-flux-tooltip-content]` - Conteúdo do tooltip
- `ui-tooltip [popover]` - Elemento popover do tooltip
- `ui-dropdown[data-flux-tooltip] [popover]` - Dropdown usado como tooltip

## Boas Práticas

### 1. Textos Concisos
- Mantenha os textos dos tooltips claros e objetivos
- Use no máximo 2-3 linhas para melhor legibilidade

### 2. Posicionamento Inteligente
- Use `position="right"` para elementos da sidebar
- Use `position="top"` para botões na parte inferior
- Use `position="bottom"` para elementos no topo da página

### 3. Atalhos de Teclado
- Sempre inclua atalhos quando disponíveis
- Use o formato padrão: `Ctrl+S`, `Alt+F4`, etc.

### 4. Contexto Relevante
- Forneça informações úteis que não estão óbvias na interface
- Inclua status, contadores ou informações adicionais quando relevante

## Acessibilidade

Os tooltips customizados mantêm a acessibilidade do Flux UI:
- Suporte a navegação por teclado
- Compatibilidade com leitores de tela
- Contraste adequado (preto/branco)
- Timing apropriado para exibição/ocultação

## Integração no Monte Sua Noite

Os tooltips customizados foram aplicados especificamente ao sistema "Monte Sua Noite":

### Texto Personalizado
O texto personalizado nos blocos do tabuleiro agora usa tooltips do Flux UI:

```blade
<!-- Antes (CSS customizado) -->
<div class="custom-text-indicator text-zinc-200 relative group">
    <div class="flex items-center justify-center">
        <flux:icon name="document-text" class="w-3 h-3 text-cyan-400" />
        <span class="ml-1 text-xs">Texto</span>
    </div>
    <!-- Tooltip CSS customizado -->
    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-50 min-w-max max-w-xs">
        {{ $square['custom_text'] }}
        <!-- Seta -->
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
    </div>
</div>

<!-- Depois (Flux UI) -->
<flux:tooltip content="{{ $square['custom_text'] }}" position="top">
    <div class="custom-text-indicator text-zinc-200 cursor-help">
        <div class="flex items-center justify-center">
            <flux:icon name="document-text" class="w-3 h-3 text-cyan-400" />
            <span class="ml-1 text-xs">Texto</span>
        </div>
    </div>
</flux:tooltip>
```

### Outros Tooltips Adicionados
- **Botões de ordenação**: Explicam cada tipo de ordenação
- **Estatísticas dos planos**: Informações sobre criador, data, valores
- **Preview do tabuleiro**: Legenda dos elementos visuais
- **Blocos temáticos**: Descrições detalhadas de cada categoria

## Páginas de Exemplo

Para ver os tooltips em ação, acesse:
- `/examples/tooltips` - Exemplos básicos
- `/examples/tooltip-integration` - Integração no projeto
- `/examples/monte-sua-noite-tooltips` - Tooltips específicos do Monte Sua Noite

## Troubleshooting

### Tooltip não aparece
1. Verifique se o CSS foi compilado: `npm run build`
2. Confirme que o elemento pai tem posição relativa
3. Verifique se não há conflitos de z-index

### Estilo não aplicado
1. Limpe o cache: `php artisan view:clear`
2. Recompile os assets: `npm run build`
3. Verifique se o CSS está sendo carregado corretamente

### Posicionamento incorreto
1. Ajuste a posição usando os atributos `position` e `align`
2. Verifique se há elementos com overflow hidden
3. Considere usar `interactive` para tooltips complexos
