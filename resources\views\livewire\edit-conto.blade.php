<div class="container mx-auto p-4 relative">
    <form wire:submit.prevent="update" class="space-y-4">
        @if (session()->has('message'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
                {{ session('message') }}
            </div>
        @endif

        <div>
            <label for="title" class="block text-sm font-medium text-gray-300">T<PERSON><PERSON><PERSON> do Conto:</label>
            <input wire:model="title" id="title" type="text" class="w-full mt-1 px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-300">
            @error('title') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
        </div>

        <div>
            <label for="category_id" class="block text-sm font-medium text-gray-300">Categoria:</label>
            <select wire:model="category_id" id="category_id" class="w-full mt-1 px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-300 bg-zinc-800">
                <option value="">Selecione uma categoria</option>
                @foreach($categories as $category)
                    <option class="text-gray-300" value="{{ $category->id }}">{{ $category->title }}</option>
                @endforeach
            </select>
            @error('category_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
        </div>

        <div>
            <label for="content" class="block text-sm font-medium text-gray-300">Conteúdo do Conto:</label>
            <textarea wire:model="content" id="content" rows="6" class="w-full mt-1 px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-300" ></textarea>
            @error('content') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
        </div>  

        <div>
            <button type="submit" class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Atualizar Conto</button>
        </div>
    </form>
</div>
