// Live Stream Camera Management
let localStream = null;
let localVideo = null;

// Função para verificar suporte do navegador
function checkBrowserSupport() {
    console.log('=== Verificação de Suporte do Navegador ===');
    console.log('User Agent:', navigator.userAgent);
    console.log('Protocol:', window.location.protocol);
    console.log('Hostname:', window.location.hostname);
    console.log('MediaDevices disponível:', !!navigator.mediaDevices);
    console.log('getUserMedia disponível:', !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia));

    if (!navigator.mediaDevices) {
        console.error('navigator.mediaDevices não está disponível');
        return false;
    }

    if (!navigator.mediaDevices.getUserMedia) {
        console.error('getUserMedia não está disponível');
        return false;
    }

    return true;
}

// Função para listar dispositivos disponíveis
async function listDevices() {
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        console.log('=== Dispositivos Disponíveis ===');
        devices.forEach((device, index) => {
            console.log(`${index}: ${device.kind} - ${device.label || 'Sem nome'} (${device.deviceId})`);
        });

        const videoDevices = devices.filter(device => device.kind === 'videoinput');
        const audioDevices = devices.filter(device => device.kind === 'audioinput');

        console.log(`Total de câmeras: ${videoDevices.length}`);
        console.log(`Total de microfones: ${audioDevices.length}`);

        return { videoDevices, audioDevices };
    } catch (error) {
        console.error('Erro ao listar dispositivos:', error);
        return { videoDevices: [], audioDevices: [] };
    }
}

// Função principal para iniciar a câmera
async function startCamera() {
    console.log('=== Iniciando Câmera ===');

    // Verificar suporte do navegador
    if (!checkBrowserSupport()) {
        updateStatus('Navegador não suportado', 'error');
        return false;
    }

    // Verificar HTTPS
    if (window.location.protocol !== 'https:' &&
        window.location.hostname !== 'localhost' &&
        window.location.hostname !== '127.0.0.1' &&
        !window.location.hostname.endsWith('.test')) {
        updateStatus('HTTPS necessário para acesso à câmera', 'error');
        return false;
    }

    try {
        localVideo = document.getElementById('localVideo');
        if (!localVideo) {
            console.error('Elemento de vídeo não encontrado');
            updateStatus('Elemento de vídeo não encontrado', 'error');
            return false;
        }

        updateStatus('Listando dispositivos...');
        await listDevices();

        updateStatus('Solicitando permissão...');

        // Configurações de vídeo progressivas (do mais específico para o mais básico)
        const videoConfigs = [
            {
                video: {
                    width: { ideal: 1280, max: 1920 },
                    height: { ideal: 720, max: 1080 },
                    frameRate: { ideal: 30, max: 60 },
                    facingMode: 'user'
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            },
            {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                },
                audio: true
            },
            {
                video: true,
                audio: true
            },
            {
                video: true,
                audio: false
            }
        ];

        let streamObtained = false;

        for (let i = 0; i < videoConfigs.length; i++) {
            try {
                console.log(`Tentativa ${i + 1}:`, videoConfigs[i]);
                updateStatus(`Tentativa ${i + 1} de ${videoConfigs.length}...`);

                localStream = await navigator.mediaDevices.getUserMedia(videoConfigs[i]);
                streamObtained = true;
                console.log('Stream obtido com sucesso:', localStream);
                break;
            } catch (error) {
                console.log(`Tentativa ${i + 1} falhou:`, error.name, error.message);
                if (i === videoConfigs.length - 1) {
                    throw error;
                }
            }
        }

        if (!streamObtained) {
            throw new Error('Não foi possível obter stream de vídeo');
        }

        updateStatus('Conectando vídeo...');

        // Configurar o elemento de vídeo
        localVideo.srcObject = localStream;

        // Event listeners para debug
        localVideo.onloadstart = () => console.log('Video: loadstart');
        localVideo.onloadeddata = () => console.log('Video: loadeddata');
        localVideo.onloadedmetadata = () => {
            console.log('Video: loadedmetadata');
            console.log('Dimensões do vídeo:', localVideo.videoWidth, 'x', localVideo.videoHeight);
            updateStatus('Câmera conectada!', 'success');
            setTimeout(() => hideStatus(), 2000);
        };
        localVideo.oncanplay = () => console.log('Video: canplay');
        localVideo.onplay = () => console.log('Video: play');
        localVideo.onplaying = () => console.log('Video: playing');
        localVideo.onerror = (e) => {
            console.error('Erro no elemento de vídeo:', e);
            updateStatus('Erro no elemento de vídeo', 'error');
        };

        // Forçar play se necessário
        try {
            await localVideo.play();
        } catch (playError) {
            console.log('Erro ao dar play (normal em alguns navegadores):', playError);
        }

        // Configurar controles
        setupControls();

        // Log das tracks
        localStream.getTracks().forEach((track, index) => {
            console.log(`Track ${index}:`, track.kind, track.label, track.enabled ? 'enabled' : 'disabled');
        });

        return true;

    } catch (error) {
        console.error('=== Erro ao Acessar Câmera ===');
        console.error('Nome:', error.name);
        console.error('Mensagem:', error.message);
        console.error('Stack:', error.stack);

        let errorMessage = 'Erro ao acessar câmera: ';

        switch (error.name) {
            case 'NotAllowedError':
                errorMessage += 'Permissão negada. Clique no ícone da câmera na barra de endereços e permita o acesso.';
                break;
            case 'NotFoundError':
                errorMessage += 'Nenhuma câmera encontrada no dispositivo.';
                break;
            case 'NotReadableError':
                errorMessage += 'Câmera está sendo usada por outro aplicativo.';
                break;
            case 'OverconstrainedError':
                errorMessage += 'Configurações de vídeo não suportadas pela câmera.';
                break;
            case 'SecurityError':
                errorMessage += 'Erro de segurança. Verifique se está usando HTTPS.';
                break;
            default:
                errorMessage += error.message;
        }

        updateStatus(errorMessage, 'error');
        alert(errorMessage);
        return false;
    }
}

// Função para parar a câmera
function stopCamera() {
    console.log('=== Parando Câmera ===');

    if (localStream) {
        localStream.getTracks().forEach(track => {
            console.log('Parando track:', track.kind, track.label);
            track.stop();
        });
        localStream = null;
    }

    if (localVideo) {
        localVideo.srcObject = null;
    }

    updateStatus('Câmera desconectada');
}

// Função para atualizar status
function updateStatus(message, type = 'info') {
    const statusDiv = document.getElementById('camera-status');
    if (!statusDiv) return;

    statusDiv.textContent = message;
    statusDiv.style.display = 'block';

    // Remover classes anteriores
    statusDiv.className = statusDiv.className.replace(/bg-\w+-\d+/g, '');

    // Adicionar classe baseada no tipo
    switch (type) {
        case 'success':
            statusDiv.className += ' bg-green-500';
            break;
        case 'error':
            statusDiv.className += ' bg-red-500';
            break;
        default:
            statusDiv.className += ' bg-blue-500';
    }

    console.log('Status:', message);
}

// Função para esconder status
function hideStatus() {
    const statusDiv = document.getElementById('camera-status');
    if (statusDiv) {
        statusDiv.style.display = 'none';
    }
}

// Função para configurar controles
function setupControls() {
    const toggleCamera = document.getElementById('toggleCamera');
    const toggleMicrophone = document.getElementById('toggleMicrophone');

    if (toggleCamera) {
        toggleCamera.addEventListener('click', () => {
            const videoTrack = localStream.getVideoTracks()[0];
            if (videoTrack) {
                videoTrack.enabled = !videoTrack.enabled;
                toggleCamera.classList.toggle('opacity-50', !videoTrack.enabled);
                console.log('Câmera:', videoTrack.enabled ? 'ligada' : 'desligada');
            }
        });
    }

    if (toggleMicrophone) {
        toggleMicrophone.addEventListener('click', () => {
            const audioTrack = localStream.getAudioTracks()[0];
            if (audioTrack) {
                audioTrack.enabled = !audioTrack.enabled;
                toggleMicrophone.classList.toggle('opacity-50', !audioTrack.enabled);
                console.log('Microfone:', audioTrack.enabled ? 'ligado' : 'desligado');
            }
        });
    }
}

// Função para testar câmera (disponível globalmente)
window.testCamera = startCamera;
window.stopCamera = stopCamera;

// Inicializar eventos do Livewire quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    // Aguardar o Livewire estar disponível
    document.addEventListener('livewire:init', () => {
        console.log('Live Stream Camera: Livewire inicializado');

        Livewire.on('live-stream-created', (streamId) => {
            console.log('Live Stream Camera: Live criada:', streamId);
        });

        Livewire.on('live-stream-started', async (streamId) => {
            console.log('Live Stream Camera: Live iniciada:', streamId);
            await startCamera();
        });

        Livewire.on('live-stream-ended', (streamId) => {
            console.log('Live Stream Camera: Live encerrada:', streamId);
            stopCamera();
        });
    });
});
