<?php

use App\Models\UserPhoto;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Volt\Component;
use Livewire\WithFileUploads; // Add WithFileUploads trait
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Collection; // Import Collection

new class extends Component {
    use WithFileUploads; // Use the trait

    public $avatar;
    public Collection $previousAvatars; // Add property for previous avatars

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $this->loadPreviousAvatars();
    }

    /**
     * Load previous avatars for the authenticated user.
     */
    public function loadPreviousAvatars(): void
    {
        $user = Auth::user();
        if ($user) {
            // Get all user photos
            // Order by latest first for display, but the 'is_current' flag determines the actual current one
            $this->previousAvatars = $user->userPhotos()->latest()->get();
        } else {
            $this->previousAvatars = new Collection();
        }
    }

    /**
     * Update the user's avatar.
     */
    public function updateAvatar(): void
    {
        try {
            $user = Auth::user();

            if (!$user) {
                throw new \Exception('Authenticated user not found.');
            }

            $validated = $this->validate([
                'avatar' => ['required', 'image', 'mimes:jpg,png', 'max:5120'],
            ]);

            if ($this->avatar) {
                // Store the new file and get the path
                $avatarPath = $this->avatar->store('avatars', 'public');

                if (!$avatarPath) {
                    throw new \Exception('Failed to store the avatar.');
                }

                // Set all user's photos to not current before creating the new one
                $user->userPhotos()->update(['is_current' => false]);

                // Save new avatar in user_photos table and set it as current
                UserPhoto::create([
                    'user_id' => $user->id,
                    'photo_path' => $avatarPath,
                    'is_current' => true, // Set the new one as current
                ]);


                // Reload previous avatars including the new one
                $this->loadPreviousAvatars();

                // Clear the file upload input after successful upload
                $this->avatar = null;

            }

            $this->dispatch('avatar-updated');
        } catch (\Throwable $e) {
            Log::error('Error updating avatar: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString(),
            ]);

            session()->flash('error', __('Ocorreu um erro ao atualizar a foto. Tente novamente.'));
        }
    }

    /**
     * Select a previous avatar as the current one.
     */
    public function selectAvatar(int $photoId): void
    {
        try {
            $user = Auth::user();
            if (!$user) {
                 throw new \Exception('Authenticated user not found.');
            }

            // Ensure the photo belongs to the user for security
            $selectedPhoto = $user->userPhotos()->find($photoId);

            if (!$selectedPhoto) {
                 throw new \Exception('Selected photo not found or does not belong to the user.');
            }

            // Set all user's photos to not current
            $user->userPhotos()->update(['is_current' => false]);
            // Set the selected photo to current
            $selectedPhoto->is_current = true;
            $selectedPhoto->save();

             // Reload avatars to reflect the change in the UI
            $this->loadPreviousAvatars();


            $this->dispatch('avatar-selected');
            // Using session flash for status message
            session()->flash('status', __('Avatar anterior selecionado com sucesso.'));

        } catch (\Throwable $e) {
             Log::error('Error selecting previous avatar: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'photo_id' => $photoId,
                'trace' => $e->getTraceAsString(),
            ]);
             session()->flash('error', __('Ocorreu um erro ao selecionar o avatar. Tente novamente.'));
        }
    }
}; ?>

<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Avatar')" :subheading="__('Atualizar sua foto de perfil')">
        <form wire:submit.prevent="updateAvatar" class="my-6 w-full space-y-6">
            <div>
                <x-file-upload wire:model="avatar" :label="__('Avatar')" accept="image/png, image/jpeg" icon="user" :iconVariant="$avatar ? 'solid' : 'outline'" />
                @error('avatar')
                    <flux:text class="mt-2 font-medium !dark:text-red-400 !text-red-600">{{ $message }}</flux:text>
                @enderror

                {{-- Image Preview --}}
                @if ($avatar)
                    <div class="mt-4">
                        <flux:text class="mb-2 font-semibold">{{ __('Prévia da Nova Foto:') }}</flux:text>
                        <img src="{{ $avatar->temporaryUrl() }}" alt="Avatar Preview" class="w-20 h-20 rounded-full object-cover">
                    </div>
                @else
                    {{-- Show current avatar if no new one is selected --}}
                    @php
                        // Find the current avatar from the loaded collection
                        $currentAvatarPhoto = $previousAvatars->firstWhere('is_current', true);
                    @endphp
                    @if ($currentAvatarPhoto)
                         <div class="mt-4">
                            <flux:text class="mb-2 font-semibold">{{ __('Foto Atual:') }}</flux:text>
                             <img src="{{ Storage::url($currentAvatarPhoto->photo_path) }}" alt="Current Avatar" class="w-20 h-20 rounded-full object-cover">
                        </div>
                    @endif
                @endif
            </div>

            <div class="flex items-center gap-4">
                <div class="flex items-center justify-end">
                    <flux:button variant="primary" type="submit" class="w-full">{{ __('Salvar Nova Foto') }}</flux:button>
                </div>

                <x-action-message class="me-3 text-green-500" on="avatar-updated">
                    {{ __('Foto atualizada.') }}
                </x-action-message>

                @if (session('error'))
                    <flux:text class="mt-2 font-medium !dark:text-red-400 !text-red-600">
                        {{ session('error') }}
                    </flux:text>
                @endif
                 @if (session('status'))
                    <flux:text class="mt-2 font-medium text-green-500">
                        {{ session('status') }}
                    </flux:text>
                @endif
            </div>
        </form>

        {{-- Previous Avatars Section --}}
        {{-- Show only if there's more than just the current one --}}
        @if ($previousAvatars->count() > 1)
             @php
                 // Find the current avatar from the loaded collection for comparison
                 $currentAvatarPhoto = $previousAvatars->firstWhere('is_current', true);
             @endphp
            <div class="my-6 w-full space-y-4" wire:key="previous-avatars-{{ $previousAvatars->count() }}"> {{-- Added wire:key for better reactivity --}}
                <flux:text class="font-semibold">{{ __('Avatares Anteriores:') }}</flux:text>
                <div class="flex flex-wrap gap-4">
                    @foreach ($previousAvatars as $prevAvatar)
                         {{-- Check if this photo is the current one using the found photo --}}
                        @php
                             $isCurrent = $currentAvatarPhoto && $currentAvatarPhoto->id === $prevAvatar->id;
                        @endphp
                        <div
                             wire:click="selectAvatar({{ $prevAvatar->id }})"
                             class="relative cursor-pointer rounded-full overflow-hidden w-16 h-16 border-2 {{ $isCurrent ? 'border-blue-500' : 'border-transparent' }}"
                             title="{{ __('Clique para usar este avatar') }}"
                        >
                            <img src="{{ Storage::url($prevAvatar->photo_path) }}" alt="Previous Avatar" class="w-full h-full object-cover">
                             @if ($isCurrent)
                                <div class="absolute inset-0 flex items-center justify-center bg-blue-500 bg-opacity-50">
                                    <x-flux::icon name="check" class="w-6 h-6 text-white" />
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
                 <x-action-message class="me-3 text-green-500" on="avatar-selected">
                    {{ __('Avatar selecionado.') }}
                </x-action-message>
            </div>
        @endif

    </x-settings.layout>
</section>
