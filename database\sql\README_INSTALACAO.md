# Instalação do Sistema de Suporte em Produção

## Instruções para KingHost via phpMyAdmin

Execute os arquivos SQL na seguinte ordem:

### 1. <PERSON><PERSON><PERSON> <PERSON>belas Principais
Execute o arquivo: `01_create_support_tables.sql`
- <PERSON>ria a tabela `support_tickets`

### 2. <PERSON><PERSON><PERSON>a de Mensagens
Execute o arquivo: `02_create_messages_table.sql`
- Cria a tabela `support_ticket_messages`

### 3. <PERSON>riar Tabelas de Ajuda
Execute o arquivo: `03_create_help_tables.sql`
- Cria as tabelas `help_articles` e `faq_items`

### 4. Verificar e Adicionar Relacionamentos
**IMPORTANTE**: Execute os arquivos na ordem para resolver problemas de chaves estrangeiras:

#### 4a. Verificar Estrutura
Execute: `04a_check_users_table.sql`
- Verifica o tipo da coluna `id` na tabela `users`

#### 4b. Ajustar Tipos (SE NECESSÁRIO)
Execute: `04b_fix_column_types.sql` **APENAS** se a coluna `id` da tabela `users` for `int(11)`
- Se for `bigint(20)`, pule este passo

#### 4c. Adicionar Chaves Estrangeiras
Execute: `04c_add_foreign_keys_step_by_step.sql`
- Adiciona as chaves uma por vez para facilitar debug
- Execute cada comando separadamente

#### 4d. ALTERNATIVA (Se houver problemas)
Execute: `04d_alternative_without_foreign_keys.sql`
- Sistema funcionará sem chaves estrangeiras
- Apenas adiciona índices para performance

### 5. Inserir Artigos de Ajuda
Execute o arquivo: `05_insert_help_articles.sql`
- Insere os tutoriais padrão

### 6. Inserir FAQ
Execute o arquivo: `06_insert_faq_items.sql`
- Insere as perguntas frequentes

## Como Executar no phpMyAdmin

1. Acesse o phpMyAdmin do KingHost
2. Selecione seu banco de dados
3. Vá na aba "SQL"
4. Copie e cole o conteúdo de cada arquivo na ordem indicada
5. Clique em "Executar"
6. Aguarde a confirmação de sucesso antes de executar o próximo

## Verificação

Após executar todos os arquivos, você deve ter as seguintes tabelas criadas:
- `support_tickets`
- `support_ticket_messages`
- `help_articles`
- `faq_items`

E dados de exemplo inseridos nas tabelas de ajuda e FAQ.

## Problemas Comuns

### Erro de Chave Estrangeira
Se houver erro ao executar o arquivo 04, verifique se:
- A tabela `users` existe
- Todas as tabelas anteriores foram criadas com sucesso

### Erro de Charset
Se houver problemas de charset, certifique-se de que:
- O banco está configurado como `utf8mb4_unicode_ci`
- Os arquivos estão sendo executados com a codificação correta

### Erro de JSON
Se houver erro com campos JSON:
- Verifique se a versão do MySQL é 5.7+ ou MariaDB 10.2+
- Se necessário, altere os campos `json` para `text`

## Suporte

Se encontrar problemas durante a instalação, verifique:
1. Versão do MySQL/MariaDB
2. Permissões do usuário do banco
3. Espaço disponível no banco de dados
4. Logs de erro do phpMyAdmin
