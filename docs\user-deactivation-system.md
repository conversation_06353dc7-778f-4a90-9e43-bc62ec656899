# Sistema de Desativação de Usuários

## Visão Geral

Este sistema implementa uma solução segura e reversível para gerenciar usuários problemáticos, substituindo a exclusão física por desativação. Isso preserva a integridade dos dados e permite auditoria completa.

## Funcionalidades Implementadas

### 1. Desativação de Usuários
- **Método**: `UserManager::delete()` - Renomeado para refletir que desativa em vez de excluir
- **Ação**: Define `active = false` e modifica o email para evitar conflitos
- **Preservação**: Todos os dados relacionados são mantidos (posts, mensagens, transações, etc.)

### 2. Reativação de Usuários
- **Método**: `UserManager::reactivateUser()`
- **Ação**: Define `active = true` e restaura o email original
- **Validação**: Verifica se o email original não está em uso por outro usuário

### 3. Interface Administrativa
- **Filtros**: Adicionado filtro para visualizar usuários ativos, inativos ou todos
- **Status Visual**: Coluna de status com badges coloridos
- **Ações Contextuais**: Botões de desativar/reativar baseados no status atual

### 4. Middleware de Segurança
- **Arquivo**: `app/Http/Middleware/CheckActiveUser.php`
- **Função**: Impede login de usuários inativos
- **Ação**: Desloga automaticamente e redireciona para login com mensagem

## Estrutura de Dados

### Campo `active` na tabela `users`
```sql
active BOOLEAN DEFAULT TRUE
```

### Modificação de Email na Desativação
```
<EMAIL> → email_original@domain.com_deactivated_1234567890
```

## Vantagens da Abordagem

### 1. Preservação de Dados
- **Posts**: Mantidos para preservar discussões e contexto
- **Mensagens**: Conversas permanecem íntegras
- **Transações**: Histórico financeiro preservado
- **Relacionamentos**: Likes, follows, matches mantidos

### 2. Integridade Referencial
- **Sem quebra**: Evita problemas de chaves estrangeiras
- **Consistência**: Dados relacionados permanecem consistentes
- **Auditoria**: Rastro completo das ações do usuário

### 3. Reversibilidade
- **Reativação**: Usuários podem ser reativados facilmente
- **Recuperação**: Email original é restaurado
- **Flexibilidade**: Permite gestão mais nuançada

## Como Usar

### Desativar Usuário
1. Acesse o painel administrativo
2. Vá para "Gerenciamento de Usuários"
3. Clique no ícone de desativação (usuário com minus)
4. Confirme a ação no modal

### Reativar Usuário
1. Filtre por "Usuários Inativos"
2. Clique no ícone de reativação (usuário com plus)
3. O usuário será reativado imediatamente

### Filtrar Usuários
- **Usuários Ativos**: Mostra apenas usuários que podem fazer login
- **Usuários Inativos**: Mostra usuários desativados
- **Todos os Status**: Mostra todos os usuários

## Implementação Técnica

### Scopes no Model User
```php
public function scopeActive($query)
{
    return $query->where('active', true);
}

public function scopeInactive($query)
{
    return $query->where('active', false);
}
```

### Verificação de Status
```php
public function isActive()
{
    return $this->active;
}
```

### Middleware de Proteção
```php
if (Auth::check() && !Auth::user()->isActive()) {
    Auth::logout();
    return redirect()->route('login')->with('error', 'Conta desativada.');
}
```

## Considerações de Produção

### 1. Banco de Dados
- Execute o SQL em `database/sql/user_deactivation_system.sql`
- Verifique se todos os usuários existentes estão marcados como ativos
- Considere criar índice na coluna `active` para performance

### 2. Middleware
- Registre o middleware `CheckActiveUser` no kernel
- Aplique em rotas que requerem usuários ativos

### 3. Consultas Existentes
- Revise consultas que buscam usuários
- Adicione filtro `->active()` onde apropriado
- Mantenha consultas administrativas sem filtro

## Monitoramento

### Métricas Importantes
- Total de usuários ativos vs inativos
- Frequência de desativações/reativações
- Impacto nos dados relacionados

### Queries de Monitoramento
```sql
-- Usuários por status
SELECT active, COUNT(*) FROM users GROUP BY active;

-- Usuários desativados recentemente
SELECT * FROM users WHERE active = 0 ORDER BY updated_at DESC LIMIT 10;
```

## Próximos Passos

1. **Registrar Middleware**: Adicionar `CheckActiveUser` ao kernel
2. **Testar Sistema**: Verificar desativação/reativação em ambiente de teste
3. **Aplicar Filtros**: Revisar consultas existentes para filtrar usuários ativos
4. **Documentar Processo**: Criar procedimentos para administradores
5. **Monitorar Impacto**: Acompanhar métricas após implementação
