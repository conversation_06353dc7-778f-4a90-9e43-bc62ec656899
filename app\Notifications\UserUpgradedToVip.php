<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserUpgradedToVip extends Notification
{
    use Queueable;

    protected $user;
    protected $isAdminCopy;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, bool $isAdminCopy = false)
    {
        $this->user = $user;
        $this->isAdminCopy = $isAdminCopy;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        if ($this->isAdminCopy) {
            // Email para administradores
            return (new MailMessage)
                ->subject('[ADMIN COPY] Usuário Promovido a VIP - ' . $this->user->name)
                ->view('emails.admin-vip-upgrade-copy', [
                    'upgradedUser' => $this->user,
                    'adminRecipient' => $notifiable,
                ]);
        } else {
            // Email para o usuário promovido
            return (new MailMessage)
                ->subject('Parabéns! Você foi promovido a VIP - ' . config('app.name'))
                ->view('emails.user-upgraded-to-vip', [
                    'user' => $this->user,
                ]);
        }
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_email' => $this->user->email,
            'upgraded_at' => now(),
            'is_admin_copy' => $this->isAdminCopy,
        ];
    }
}
