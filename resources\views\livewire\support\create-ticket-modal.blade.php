<div>
    @if($showModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div class="bg-white dark:bg-zinc-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Criar Ticket de Suporte</h2>
                <button wire:click="closeModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Form -->
            <form wire:submit="createTicket" class="p-6 space-y-4">
                <!-- Category -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Categoria do Problema *
                    </label>
                    <select wire:model.live="category" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Selecione uma categoria</option>
                        @foreach($this->categories as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('category') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Priority -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Prioridade *
                    </label>
                    <select wire:model.live="priority" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        @foreach($this->priorities as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('priority') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Premium Option -->
                @if($premiumPrice > 0)
                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input 
                                    type="checkbox" 
                                    wire:model.live="isPremium"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            </div>
                            <div class="ml-3">
                                <label class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                    Ticket Premium - R$ {{ number_format($premiumPrice, 2, ',', '.') }}
                                </label>
                                <p class="text-sm text-yellow-700 dark:text-yellow-300">
                                    Atendimento prioritário com resposta em até 2 horas úteis
                                </p>
                                <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                                    Saldo atual: R$ {{ number_format(auth()->user()->wallet_balance ?? 0, 2, ',', '.') }}
                                </p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Title -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Título do Problema *
                    </label>
                    <input 
                        type="text"
                        wire:model="title" 
                        placeholder="Descreva brevemente o problema"
                        maxlength="255"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                    @error('title') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Description -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Descrição Detalhada *
                    </label>
                    <textarea
                        wire:model="description"
                        placeholder="Descreva o problema em detalhes. Inclua passos para reproduzir, mensagens de erro, etc."
                        rows="4"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                    @error('description') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Attachments -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Anexos (Opcional)
                    </label>
                    <div class="space-y-3">
                        <input 
                            type="file" 
                            wire:model="attachments" 
                            multiple
                            accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt"
                            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-200 dark:hover:file:bg-blue-800" />
                        
                        @if(!empty($attachments))
                            <div class="space-y-2">
                                @foreach($attachments as $index => $attachment)
                                    <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                                        <span class="text-sm text-gray-700 dark:text-gray-300">
                                            {{ $attachment->getClientOriginalName() }}
                                            <span class="text-gray-500">({{ number_format($attachment->getSize() / 1024, 1) }} KB)</span>
                                        </span>
                                        <button 
                                            type="button"
                                            wire:click="removeAttachment({{ $index }})"
                                            class="text-red-600 hover:text-red-700 p-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            Tipos permitidos: JPG, PNG, PDF, DOC, DOCX, TXT. Máximo 10MB por arquivo.
                        </p>
                    </div>
                    @error('attachments.*') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>



                <!-- Footer -->
                <div class="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button 
                        type="button"
                        wire:click="closeModal" 
                        class="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md font-medium transition-colors">
                        Cancelar
                    </button>
                    <button 
                        type="submit" 
                        class="flex-1 px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        Criar Ticket
                        @if($isPremium && $premiumPrice > 0)
                            <span class="text-xs bg-yellow-500 text-yellow-900 px-2 py-0.5 rounded">
                                R$ {{ number_format($premiumPrice, 2, ',', '.') }}
                            </span>
                        @endif
                    </button>
                </div>
            </form>
        </div>
        </div>
    @endif
</div>
