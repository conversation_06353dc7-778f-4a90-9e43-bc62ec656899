<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NightBoardSupport extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'position',
        'amount',
        'comment',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * Relacionamento com o usuário que patrocinou
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relacionamento com o plano patrocinado
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(NightBoardPlan::class, 'plan_id');
    }

    /**
     * Formata o valor do patrocínio
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'R$ ' . number_format($this->amount, 2, ',', '.');
    }
}
