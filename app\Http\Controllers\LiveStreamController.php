<?php

namespace App\Http\Controllers;

use App\Models\LiveStream;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class LiveStreamController extends Controller
{
    /**
     * Iniciar stream
     */
    public function startStream(Request $request)
    {
        try {
            $streamId = $request->input('stream_id');

            $liveStream = LiveStream::find($streamId);
            if (!$liveStream) {
                return response()->json(['error' => 'Stream não encontrado'], 404);
            }

            // Verificar se o usuário é o dono do stream
            if ($liveStream->user_id !== Auth::id()) {
                return response()->json(['error' => 'Não autorizado'], 403);
            }

            // Atualizar status para live
            $liveStream->update([
                'status' => 'live',
                'started_at' => now()
            ]);

            Log::info('Stream iniciado via API', [
                'stream_id' => $streamId,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Stream iniciado com sucesso',
                'stream' => $liveStream
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao iniciar stream via API: ' . $e->getMessage());
            return response()->json(['error' => 'Erro interno do servidor'], 500);
        }
    }

    /**
     * Encerrar stream
     */
    public function endStream(Request $request)
    {
        try {
            $streamId = $request->input('stream_id');
            $duration = $request->input('duration', 0);

            $liveStream = LiveStream::find($streamId);
            if (!$liveStream) {
                return response()->json(['error' => 'Stream não encontrado'], 404);
            }

            // Verificar se o usuário é o dono do stream
            if ($liveStream->user_id !== Auth::id()) {
                return response()->json(['error' => 'Não autorizado'], 403);
            }

            // Atualizar status para ended
            $liveStream->update([
                'status' => 'ended',
                'ended_at' => now(),
                'duration' => $duration
            ]);

            Log::info('Stream encerrado via API', [
                'stream_id' => $streamId,
                'duration' => $duration,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Stream encerrado com sucesso',
                'stream' => $liveStream
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao encerrar stream via API: ' . $e->getMessage());
            return response()->json(['error' => 'Erro interno do servidor'], 500);
        }
    }

    /**
     * Atualizar número de viewers
     */
    public function updateViewers(Request $request)
    {
        try {
            $streamId = $request->input('stream_id');
            $viewersCount = $request->input('viewers_count', 0);

            $liveStream = LiveStream::find($streamId);
            if (!$liveStream) {
                return response()->json(['error' => 'Stream não encontrado'], 404);
            }

            // Atualizar contador de viewers
            $liveStream->update([
                'viewers_count' => $viewersCount
            ]);

            return response()->json([
                'success' => true,
                'viewers_count' => $viewersCount
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao atualizar viewers via API: ' . $e->getMessage());
            return response()->json(['error' => 'Erro interno do servidor'], 500);
        }
    }

    /**
     * Salvar gravação do stream
     */
    public function saveRecording(Request $request)
    {
        try {
            $request->validate([
                'video' => 'required|file|mimes:webm,mp4|max:1048576', // Max 1GB
                'stream_id' => 'required|integer',
                'duration' => 'required|integer'
            ]);

            $streamId = $request->input('stream_id');
            $duration = $request->input('duration');

            $liveStream = LiveStream::find($streamId);
            if (!$liveStream) {
                return response()->json(['error' => 'Stream não encontrado'], 404);
            }

            // Verificar se o usuário é o dono do stream
            if ($liveStream->user_id !== Auth::id()) {
                return response()->json(['error' => 'Não autorizado'], 403);
            }

            // Salvar arquivo de vídeo
            $videoFile = $request->file('video');
            $fileName = 'live-streams/' . $streamId . '-' . time() . '.webm';
            $videoPath = $videoFile->storeAs('public', $fileName);

            // Atualizar live stream com informações do replay
            $liveStream->update([
                'video_path' => $fileName,
                'duration' => $duration,
                'has_replay' => true
            ]);

            Log::info('Gravação salva via API', [
                'stream_id' => $streamId,
                'video_path' => $fileName,
                'duration' => $duration,
                'file_size' => $videoFile->getSize()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Gravação salva com sucesso',
                'video_path' => $fileName,
                'replay_url' => Storage::url($fileName)
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao salvar gravação via API: ' . $e->getMessage());
            return response()->json(['error' => 'Erro interno do servidor'], 500);
        }
    }
}
