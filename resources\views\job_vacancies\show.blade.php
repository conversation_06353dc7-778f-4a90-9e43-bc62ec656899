<x-layouts.app :title="$job->title">
    <div class="max-w-4xl mx-auto px-4 py-8">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <flux:button href="{{route('job_vacancies.index')}}" variant="ghost" size="sm">
                    <flux:icon icon="arrow-left" class="w-4 h-4 mr-1" />
                    Voltar para vagas
                </flux:button>
            </div>
        </nav>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Job Header -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 mb-6">
                    <!-- Category and Featured Badge -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-2">
                            <flux:icon icon="tag" class="w-4 h-4 text-gray-400" />
                            <span class="text-sm font-semibold uppercase text-primary bg-primary/10 px-3 py-1 rounded-full">
                                {{$job->category->name}}
                            </span>
                        </div>
                        @if($job->is_featured)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                <flux:icon icon="star" class="w-4 h-4 mr-1" />
                                Destaque
                            </span>
                        @endif
                    </div>

                    <!-- Job Title -->
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">{{$job->title}}</h1>

                    <!-- Job Details Tags -->
                    <div class="flex flex-wrap gap-3 mb-6">
                        <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                            <flux:icon icon="document-text" class="w-4 h-4 mr-2" />
                            {{$job->contract_type}}
                        </span>
                        <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <flux:icon icon="map-pin" class="w-4 h-4 mr-2" />
                            {{$job->work_mode}}
                        </span>
                        <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                            <flux:icon icon="academic-cap" class="w-4 h-4 mr-2" />
                            {{$job->experience_level}}
                        </span>
                        @if($job->location)
                            <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                <flux:icon icon="map-pin" class="w-4 h-4 mr-2" />
                                {{$job->location}}
                            </span>
                        @endif
                    </div>

                    <!-- Salary -->
                    <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4 mb-6">
                        <div class="flex items-center gap-2 mb-2">
                            <flux:icon icon="currency-dollar" class="w-5 h-5 text-green-600" />
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Remuneração</span>
                        </div>
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{$job->formatted_salary}}</div>
                        @if($job->salary_negotiable)
                            <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">Salário negociável</div>
                        @endif
                    </div>
                    <!-- Job Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                        <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-3">
                            <div class="text-lg font-bold text-gray-900 dark:text-white">{{$job->vacancies}}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Vagas</div>
                        </div>
                        <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-3">
                            <div class="text-lg font-bold text-gray-900 dark:text-white">{{$job->views_count}}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Visualizações</div>
                        </div>
                        <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-3">
                            <div class="text-lg font-bold text-gray-900 dark:text-white">{{$job->applications_count}}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Candidaturas</div>
                        </div>
                        @if($job->application_deadline)
                            <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-3">
                                <div class="text-lg font-bold text-gray-900 dark:text-white">{{$job->application_deadline->format('d/m')}}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Prazo</div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Job Description -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 mb-6">
                    <div class="flex items-center gap-2 mb-4">
                        <flux:icon icon="document-text" class="w-5 h-5 text-primary" />
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Descrição da Vaga</h2>
                    </div>
                    <div class="prose prose-gray dark:prose-invert max-w-none">
                        {!! nl2br(e($job->description)) !!}
                    </div>
                </div>

                @if($job->requirements)
                    <!-- Requirements -->
                    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 mb-6">
                        <div class="flex items-center gap-2 mb-4">
                            <flux:icon icon="check-circle" class="w-5 h-5 text-orange-500" />
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Requisitos</h2>
                        </div>
                        <div class="prose prose-gray dark:prose-invert max-w-none">
                            {!! nl2br(e($job->requirements)) !!}
                        </div>
                    </div>
                @endif

                @if($job->benefits)
                    <!-- Benefits -->
                    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 mb-6">
                        <div class="flex items-center gap-2 mb-4">
                            <flux:icon icon="gift" class="w-5 h-5 text-green-500" />
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Benefícios</h2>
                        </div>
                        <div class="prose prose-gray dark:prose-invert max-w-none">
                            {!! nl2br(e($job->benefits)) !!}
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Application Card -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6 mb-6 sticky top-6">
                    <div class="text-center mb-6">
                        <h3 class="text-lg font-bold text-gray-300 mb-2">Candidatar-se</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Interessado nesta vaga? Envie sua candidatura agora!
                        </p>
                    </div>

                    @if($canApply)
                        @livewire('job-application', ['job' => $job])

                    @elseif($userApplication)
                        <!-- Already Applied -->
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6 text-center">
                            <flux:icon icon="check-circle" class="w-12 h-12 text-blue-600 mx-auto mb-3" />
                            <h4 class="font-bold text-blue-800 dark:text-blue-200 mb-2">
                                Candidatura Enviada!
                            </h4>
                            <p class="text-sm text-blue-700 dark:text-blue-300 mb-4">
                                Você já se candidatou para esta vaga. Acompanhe o status na sua área de usuário.
                            </p>
                            <div class="text-xs text-blue-600 dark:text-blue-400">
                                Status: <span class="font-medium">{{ $userApplication->status }}</span>
                            </div>
                        </div>

                    @else
                        <!-- Applications Closed -->
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6 text-center">
                            <flux:icon icon="x-circle" class="w-12 h-12 text-red-600 mx-auto mb-3" />
                            <h4 class="font-bold text-red-800 dark:text-red-200 mb-2">
                                Candidaturas Encerradas
                            </h4>
                            <p class="text-sm text-red-700 dark:text-red-300">
                                O prazo para candidaturas desta vaga já expirou.
                            </p>
                        </div>
                    @endif
                </div>

                <!-- Job Info Card -->
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Informações da Vaga</h3>

                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Publicada em:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                {{ $job->created_at->format('d/m/Y') }}
                            </span>
                        </div>

                        @if($job->application_deadline)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Prazo:</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $job->application_deadline->format('d/m/Y') }}
                                </span>
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Currículo:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                {{ $job->requires_resume ? 'Obrigatório' : 'Opcional' }}
                            </span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Carta:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                {{ $job->requires_cover_letter ? 'Obrigatória' : 'Opcional' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- VIP Benefits Banner -->
        <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-700 p-6">
            <div class="flex items-start gap-4">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <flux:icon icon="star" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Usuários VIP têm prioridade nas candidaturas!
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Qualquer pessoa pode se candidatar às nossas vagas, mas membros VIP têm análise prioritária e mais chances de destaque no processo seletivo.
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                            <flux:icon icon="check" class="w-4 h-4 mr-1" />
                            Análise prioritária
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                            <flux:icon icon="check" class="w-4 h-4 mr-1" />
                            Destaque no processo
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <flux:icon icon="check" class="w-4 h-4 mr-1" />
                            Feedback mais rápido
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Jobs -->
        @if($relatedJobs->count())
            <div class="mt-12">
                <div class="flex items-center gap-2 mb-6">
                    <flux:icon icon="briefcase" class="w-6 h-6 text-primary" />
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Vagas Relacionadas</h2>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($relatedJobs as $related)
                        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 group">
                            <div class="p-6">
                                <!-- Category -->
                                <div class="flex items-center gap-2 mb-3">
                                    <flux:icon icon="tag" class="w-4 h-4 text-gray-400" />
                                    <span class="text-xs font-semibold uppercase text-primary bg-primary/10 px-2 py-1 rounded-full">
                                        {{$related->category->name}}
                                    </span>
                                </div>

                                <!-- Title -->
                                <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors">
                                    <a href="{{route('job_vacancies.show', $related->slug)}}" class="hover:underline">
                                        {{$related->title}}
                                    </a>
                                </h3>

                                <!-- Description -->
                                <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-4">
                                    {{$related->description}}
                                </p>

                                <!-- Tags -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                        {{$related->contract_type}}
                                    </span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                        {{$related->work_mode}}
                                    </span>
                                </div>

                                <!-- Footer -->
                                <div class="flex items-center justify-between">
                                    <div class="text-sm font-semibold text-primary">
                                        {{$related->formatted_salary}}
                                    </div>
                                    <flux:button href="{{route('job_vacancies.show', $related->slug)}}" variant="primary" size="sm">
                                        Ver Detalhes
                                    </flux:button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</x-layouts.app>