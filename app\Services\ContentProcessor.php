<?php

namespace App\Services;

use App\Models\User;
use App\Models\Hashtag;
use App\Models\Mention;
use App\Models\Post;
use App\Models\Comment;
use Illuminate\Database\Eloquent\Model;

class ContentProcessor
{
    /**
     * Processar conteúdo para destacar menções e hashtags
     */
    public static function processContent($content)
    {
        // Processar menções (@username)
        $content = preg_replace_callback(
            '/@([a-zA-Z0-9_]+)/',
            function ($matches) {
                $username = $matches[1];
                $user = User::where('username', $username)->first();
                
                if ($user) {
                    return '<a href="/' . $username . '" class="text-pink-400 hover:text-pink-300 font-medium">@' . $username . '</a>';
                }
                
                return '@' . $username;
            },
            $content
        );

        // Processar hashtags (#hashtag)
        $content = preg_replace_callback(
            '/#([a-zA-Z0-9_]+)/',
            function ($matches) {
                $hashtagName = strtolower($matches[1]);
                $hashtag = Hashtag::where('name', $hashtagName)->first();
                
                if ($hashtag) {
                    return '<a href="' . route('hashtag.show', $hashtag->slug) . '" class="text-cyan-400 hover:text-cyan-300 font-medium">#' . $matches[1] . '</a>';
                }
                
                return '<span class="text-cyan-400">#' . $matches[1] . '</span>';
            },
            $content
        );

        return $content;
    }

    /**
     * Extrair menções do conteúdo
     */
    public static function extractMentions($content)
    {
        preg_match_all('/@([a-zA-Z0-9_]+)/', $content, $matches, PREG_OFFSET_CAPTURE);
        
        $mentions = [];
        foreach ($matches[1] as $index => $match) {
            $username = $match[0];
            $position = $match[1] - 1; // Subtrair 1 para incluir o @
            
            $user = User::where('username', $username)->first();
            if ($user) {
                $mentions[] = [
                    'user_id' => $user->id,
                    'username' => $username,
                    'position' => $position
                ];
            }
        }
        
        return $mentions;
    }

    /**
     * Extrair hashtags do conteúdo
     */
    public static function extractHashtags($content)
    {
        preg_match_all('/#([a-zA-Z0-9_]+)/', $content, $matches);
        
        $hashtags = [];
        foreach ($matches[1] as $hashtagName) {
            $hashtags[] = strtolower($hashtagName);
        }
        
        return array_unique($hashtags);
    }

    /**
     * Processar menções para um post ou comentário
     */
    public static function processMentions($content, $mentionable, $mentionedBy)
    {
        $mentions = self::extractMentions($content);
        
        foreach ($mentions as $mentionData) {
            // Verificar se a menção já existe para evitar duplicatas
            $existingMention = Mention::where([
                'user_id' => $mentionData['user_id'],
                'mentioned_by' => $mentionedBy,
                'mentionable_type' => get_class($mentionable),
                'mentionable_id' => $mentionable->id,
            ])->first();

            if (!$existingMention) {
                try {
                    Mention::create([
                        'user_id' => $mentionData['user_id'],
                        'mentioned_by' => $mentionedBy,
                        'mentionable_type' => get_class($mentionable),
                        'mentionable_id' => $mentionable->id,
                        'position' => $mentionData['position']
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Erro ao criar menção: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * Processar hashtags para um modelo taggable (Post, Comment, etc.)
     */
    public static function processHashtags($content, Model $taggable)
    {
        if (!method_exists($taggable, 'hashtags')) {
            \Log::error('Model ' . get_class($taggable) . ' does not have a hashtags relationship.');
            return;
        }

        $newHashtagNames = self::extractHashtags($content);
        
        $newHashtagIds = [];
        foreach ($newHashtagNames as $name) {
            try {
                $hashtag = Hashtag::findOrCreateByName($name);
                $newHashtagIds[] = $hashtag->id;
            } catch (\Exception $e) {
                \Log::error('Erro ao criar ou encontrar hashtag: ' . $name . ' - ' . $e->getMessage());
            }
        }

        // Get current hashtags to determine which ones are new or removed
        $currentHashtagIds = $taggable->hashtags()->pluck('hashtags.id')->toArray();

        try {
            // Sync hashtags
            $syncResult = $taggable->hashtags()->sync($newHashtagIds);

            // Update counts for detached hashtags
            foreach ($syncResult['detached'] as $detachedId) {
                $hashtag = Hashtag::find($detachedId);
                if ($hashtag) {
                    $hashtag->decrementPostsCount(); // Use the model's method
                }
            }

            // Update counts for attached hashtags
            foreach ($syncResult['attached'] as $attachedId) {
                $hashtag = Hashtag::find($attachedId);
                if ($hashtag) {
                    $hashtag->incrementPostsCount(); // Use the model's method
                }
            }

        } catch (\Exception $e) {
            \Log::error('Erro ao sincronizar hashtags para ' . get_class($taggable) . ' ID ' . $taggable->id . ': ' . $e->getMessage());
        }
    }
}
