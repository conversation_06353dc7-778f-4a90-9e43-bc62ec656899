<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\Response;

class SessionTimeout
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Só verifica se o usuário está logado
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();
        
        // Tempo limite de inatividade em minutos (configurável)
        $timeoutMinutes = config('session.inactivity_timeout', 30); // 30 minutos padrão
        
        // Verifica se existe timestamp da última atividade na sessão
        $lastActivity = Session::get('last_activity');
        
        if ($lastActivity) {
            $lastActivityTime = Carbon::createFromTimestamp($lastActivity);
            $now = Carbon::now();
            
            // Calcula o tempo de inatividade
            $inactiveMinutes = $now->diffInMinutes($lastActivityTime);
            
            // Se excedeu o tempo limite, faz logout
            if ($inactiveMinutes >= $timeoutMinutes) {
                // Log da ação
                \Log::info('Usuário deslogado por inatividade', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'inactive_minutes' => $inactiveMinutes,
                    'timeout_minutes' => $timeoutMinutes
                ]);
                
                // Faz logout
                Auth::logout();
                Session::flush();
                
                // Redireciona para login com mensagem
                return redirect()->route('login')
                    ->with('warning', 'Sua sessão expirou por inatividade. Faça login novamente.');
            }
        }
        
        // Atualiza o timestamp da última atividade
        Session::put('last_activity', time());
        
        return $next($request);
    }
} 