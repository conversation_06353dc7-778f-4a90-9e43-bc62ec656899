<div>
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Inscrição no Evento (Teste Simples)</h2>

        @if(!auth()->check())
            <div class="bg-yellow-50 dark:bg-yellow-900/30 border-l-4 border-yellow-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <x-flux::icon icon="exclamation-triangle" class="h-5 w-5 text-yellow-400" />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700 dark:text-yellow-200">
                            Você precisa estar logado para se inscrever neste evento.
                        </p>
                        <div class="mt-2">
                            <x-flux::button href="{{ route('login') }}" color="primary" size="sm">
                                Fazer <PERSON>
                            </x-flux::button>
                        </div>
                    </div>
                </div>
            </div>
        @elseif($isRegistered)
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <x-flux::icon icon="check-circle" class="h-5 w-5 text-green-400" />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-green-700 dark:text-green-200">
                            Você já está inscrito neste evento.
                        </p>

                        @if($attendee && $attendee->ticket_code)
                            <p class="text-sm text-green-700 dark:text-green-200 mt-2">
                                Seu código de ingresso: <span class="font-bold">{{ $attendee->ticket_code }}</span>
                            </p>
                        @endif

                        <div class="mt-2">
                            <x-flux::button wire:click="cancelRegistration" color="danger" size="sm">
                                Cancelar Inscrição
                            </x-flux::button>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="bg-indigo-50 dark:bg-indigo-900/30 border-l-4 border-indigo-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <x-flux::icon icon="ticket" class="h-5 w-5 text-indigo-400" />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-indigo-700 dark:text-indigo-200">
                            Inscreva-se neste evento para garantir sua vaga!
                        </p>

                        <div class="mt-2">
                            <x-flux::button wire:click="showRegistrationModal" color="primary" size="sm">
                                {{ $event->is_free ? 'Inscrever-se Gratuitamente' : 'Inscrever-se por ' . $event->formatted_price }}
                            </x-flux::button>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Informações do evento -->
        <div class="mt-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Informações do Evento</h3>

            <div class="space-y-2 text-sm">
                <p><strong>Nome:</strong> {{ $event->name }}</p>
                <p><strong>Data:</strong> {{ $event->formatted_date }}</p>
                <p><strong>Preço:</strong> {{ $event->formatted_price }}</p>
                <p><strong>Ativo:</strong> {{ $event->is_active ? 'Sim' : 'Não' }}</p>
                <p><strong>Passou:</strong> {{ $event->has_passed ? 'Sim' : 'Não' }}</p>
                <p><strong>Esgotado:</strong> {{ $event->is_sold_out ? 'Sim' : 'Não' }}</p>
                @if($event->capacity)
                    <p><strong>Vagas disponíveis:</strong> {{ $event->available_spots }}</p>
                @endif
            </div>
        </div>

        <!-- Debug info -->
        @if(config('app.debug') && count($debugInfo) > 0)
            <div class="mt-6 p-4 bg-gray-100 dark:bg-gray-700 rounded">
                <h4 class="font-bold mb-2">Debug Info:</h4>
                <div class="text-xs space-y-1">
                    @foreach($debugInfo as $info)
                        <p>{{ $info }}</p>
                    @endforeach
                </div>
                <button wire:click="clearDebug" class="mt-2 text-xs bg-red-500 text-white px-2 py-1 rounded">
                    Limpar Debug
                </button>
            </div>
        @endif
    </div>

    <!-- Modal de confirmação -->
    @if($showModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-bold mb-4">Confirmar Inscrição</h3>

                <p class="mb-4">Você está prestes a se inscrever no evento:</p>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                    <h4 class="font-bold">{{ $event->name }}</h4>
                    <p class="text-sm">{{ $event->formatted_date }}</p>
                    <p class="text-sm">{{ $event->location }}</p>
                    <p class="font-medium text-indigo-600 dark:text-indigo-400 mt-2">{{ $event->formatted_price }}</p>
                </div>

                @if(!$event->is_free)
                    <div class="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3 mb-4">
                        <p class="text-sm text-yellow-800 dark:text-yellow-200">
                            <strong>💳 Evento Pago:</strong> Ao confirmar, você será redirecionado para o pagamento via Stripe.
                        </p>
                    </div>
                @endif

                <div class="flex space-x-4">
                    <x-flux::button wire:click="$set('showModal', false)" color="secondary">
                        Cancelar
                    </x-flux::button>

                    <x-flux::button wire:click="register" color="primary">
                        Confirmar Inscrição
                    </x-flux::button>
                </div>
            </div>
        </div>
    @endif
</div>
