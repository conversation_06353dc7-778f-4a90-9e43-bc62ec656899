<?php

use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Livewire\Volt\Volt;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('forgot password page loads correctly', function () {
    $response = $this->get('/forgot-password');

    $response->assertStatus(200)
        ->assertSee('Esqueceu a Senha?')
        ->assertSee('Digite seu email para receber um link de redefinição de senha')
        ->assertSee('Endereço de Email')
        ->assertSee('Enviar Link de Redefinição');
});

test('reset password page loads correctly with token', function () {
    $response = $this->get('/reset-password/fake-token?email=<EMAIL>');

    $response->assertStatus(200)
        ->assertSee('Redefinir Senha')
        ->assertSee('Digite sua nova senha para acessar sua conta')
        ->assertSee('Nova Senha')
        ->assertSee('Confirme a Nova Senha');
});

test('forgot password form validates email field', function () {
    Mail::fake();

    Volt::test('auth.forgot-password')
        ->set('email', '')
        ->call('sendPasswordResetLink')
        ->assertHasErrors(['email']);

    Volt::test('auth.forgot-password')
        ->set('email', 'invalid-email')
        ->call('sendPasswordResetLink')
        ->assertHasErrors(['email']);
});

test('forgot password form shows success message', function () {
    Mail::fake();

    $user = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    Volt::test('auth.forgot-password')
        ->set('email', $user->email)
        ->call('sendPasswordResetLink')
        ->assertHasNoErrors()
        ->assertSessionHas('status');
});

test('reset password form validates all fields', function () {
    Volt::test('auth.reset-password', ['token' => 'fake-token'])
        ->set('email', '')
        ->set('password', '')
        ->set('password_confirmation', '')
        ->call('resetPassword')
        ->assertHasErrors(['email', 'password']);
});

test('reset password form validates password confirmation', function () {
    Volt::test('auth.reset-password', ['token' => 'fake-token'])
        ->set('email', '<EMAIL>')
        ->set('password', 'newpassword123')
        ->set('password_confirmation', 'differentpassword')
        ->call('resetPassword')
        ->assertHasErrors(['password']);
});

test('login page shows forgot password link', function () {
    $response = $this->get('/login');

    $response->assertStatus(200)
        ->assertSee('Esqueceu a senha?');
});
