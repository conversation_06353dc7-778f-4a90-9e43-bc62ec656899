<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_vacancies', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('requirements')->nullable();
            $table->text('benefits')->nullable();
            $table->foreignId('category_id')->constrained('job_categories')->onDelete('cascade');
            $table->enum('contract_type', ['CLT', 'PJ', 'Freelance', 'Estágio', 'Temporário'])->default('CLT');
            $table->enum('work_mode', ['Presencial', 'Remoto', 'Híbrido'])->default('Presencial');
            $table->string('location')->nullable();
            $table->decimal('salary_min', 10, 2)->nullable();
            $table->decimal('salary_max', 10, 2)->nullable();
            $table->string('salary_period')->default('Mensal'); // Mensal, Semanal, Por projeto
            $table->boolean('salary_negotiable')->default(false);
            $table->integer('vacancies')->default(1);
            $table->date('application_deadline')->nullable();
            $table->enum('experience_level', ['Júnior', 'Pleno', 'Sênior', 'Especialista'])->default('Pleno');
            $table->boolean('requires_resume')->default(true);
            $table->boolean('requires_cover_letter')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('views_count')->default(0);
            $table->integer('applications_count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_vacancies');
    }
};
