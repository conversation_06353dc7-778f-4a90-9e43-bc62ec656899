<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckEventRegistrationTime
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check for authenticated users with 'visitante' role
        if (Auth::check() && Auth::user()->hasRole('visitante')) {
            $user = Auth::user();
            $timeLimit = 10 * 60; // 10 minutes in seconds
            
            // Get the time when user logged in today
            $loginTime = session('visitor_login_time');
            
            if ($loginTime) {
                $elapsedTime = now()->timestamp - $loginTime;
                $remainingTime = $timeLimit - $elapsedTime;
                
                // If less than 2 minutes remaining, don't allow event registration
                if ($remainingTime < 120) {
                    if ($request->expectsJson()) {
                        return response()->json([
                            'error' => 'Tempo insuficiente para completar a inscrição. Faça upgrade para VIP.'
                        ], 403);
                    }
                    
                    return redirect()->route('events.index')
                        ->with('error', 'Tempo insuficiente para completar a inscrição. Faça upgrade para VIP para ter acesso ilimitado.');
                }
            }
        }

        return $next($request);
    }
}
