<?php

namespace Database\Factories;

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EventAttendee>
 */
class EventAttendeeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EventAttendee::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'event_id' => Event::factory(),
            'user_id' => User::factory(),
            'status' => 'confirmed',
            'ticket_code' => $this->generateTicketCode(),
            'payment_status' => 'completed',
            'payment_method' => 'free',
            'payment_id' => null,
            'amount_paid' => 0,
            'paid_at' => now(),
            'checked_in_at' => null,
        ];
    }

    /**
     * Indicate that the attendee has a pending payment.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'registered',
            'payment_status' => 'pending',
            'payment_method' => null,
            'amount_paid' => 0,
            'paid_at' => null,
            'ticket_code' => null,
        ]);
    }

    /**
     * Indicate that the attendee paid for the event.
     */
    public function paid(float $amount = null): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'confirmed',
            'payment_status' => 'completed',
            'payment_method' => 'stripe',
            'payment_id' => 'pi_' . $this->faker->randomNumber(8),
            'amount_paid' => $amount ?? $this->faker->randomFloat(2, 10, 200),
            'paid_at' => now(),
            'ticket_code' => $this->generateTicketCode(),
        ]);
    }

    /**
     * Indicate that the attendee's payment failed.
     */
    public function paymentFailed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'registered',
            'payment_status' => 'failed',
            'payment_method' => 'stripe',
            'payment_id' => 'pi_' . $this->faker->randomNumber(8),
            'amount_paid' => 0,
            'paid_at' => null,
            'ticket_code' => null,
        ]);
    }

    /**
     * Indicate that the attendee cancelled their registration.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
        ]);
    }

    /**
     * Indicate that the attendee has checked in.
     */
    public function checkedIn(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'checked_in',
            'checked_in_at' => now(),
        ]);
    }

    /**
     * Generate a ticket code.
     */
    private function generateTicketCode(): string
    {
        return strtoupper($this->faker->bothify('########'));
    }
}
