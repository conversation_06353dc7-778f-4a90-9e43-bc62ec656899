<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Hashtag;
use Illuminate\Http\Request;

class HashtagController extends Controller
{
    /**
     * Search for hashtags.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $limit = $request->input('limit', 10);

        if (empty($query)) {
            return response()->json([]);
        }

        // Use the searchByName method from the Hashtag model
        $hashtags = Hashtag::searchByName($query, (int)$limit)
            ->map(function ($hashtag) {
                // We can customize the returned data if needed
                return [
                    'id' => $hashtag->id,
                    'name' => $hashtag->name,
                    'slug' => $hashtag->slug,
                    'posts_count' => $hashtag->posts_count, // This now reflects all taggables
                ];
            });

        return response()->json($hashtags);
    }
}