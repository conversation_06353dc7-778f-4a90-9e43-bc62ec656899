<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
    /**
     * Search for users by username or name.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $limit = $request->input('limit', 5);

        if (empty($query)) {
            return response()->json([]);
        }

        $users = User::where(function ($q) use ($query) {
            $q->where('username', 'LIKE', "%{$query}%")
              ->orWhere('name', 'LIKE', "%{$query}%");
        })
        ->select(['id', 'name', 'username'])
        ->with(['userPhotos' => function($query) {
            $query->latest()->take(1);
        }])
        ->take((int)$limit)
        ->get()
        ->map(function ($user) {
            $avatarPath = $user->userPhotos->first() ? $user->userPhotos->first()->photo_path : null;
            return [
                'id' => $user->id,
                'name' => $user->name,
                'username' => $user->username,
                'avatar' => $avatarPath ? Storage::url($avatarPath) : asset('images/users/avatar.svg'),
            ];
        });

        return response()->json($users);
    }
}