<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mention extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'mentioned_by',
        'mentionable_type',
        'mentionable_id',
        'position'
    ];

    /**
     * Usuário que foi mencionado
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Usuário que fez a menção
     */
    public function mentionedBy()
    {
        return $this->belongsTo(User::class, 'mentioned_by');
    }

    /**
     * Relação polimórfica com Post ou Comment
     */
    public function mentionable()
    {
        return $this->morphTo();
    }

    /**
     * Criar notificação quando uma menção é criada
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($mention) {
            // Não criar notificação se o usuário mencionou a si mesmo
            if ($mention->user_id !== $mention->mentioned_by) {
                try {
                    Notification::create([
                        'user_id' => $mention->user_id,
                        'sender_id' => $mention->mentioned_by,
                        'type' => 'mention',
                        'post_id' => $mention->mentionable_type === 'App\Models\Post' ? $mention->mentionable_id : ($mention->mentionable ? $mention->mentionable->post_id : null),
                        'comment_id' => $mention->mentionable_type === 'App\Models\Comment' ? $mention->mentionable_id : null,
                        'mention_id' => $mention->id,
                        'message' => 'Você foi mencionado em ' . ($mention->mentionable_type === 'App\Models\Post' ? 'um post' : 'um comentário'),
                        'read' => false
                    ]);

                    // Adicionar pontos ao usuário mencionado
                    UserPoint::addPoints(
                        $mention->user_id,
                        'mention_received',
                        3,
                        "Foi mencionado por " . $mention->mentionedBy->name,
                        $mention->id,
                        Mention::class
                    );
                } catch (\Exception $e) {
                    \Log::error('Erro ao criar notificação de menção: ' . $e->getMessage());
                }
            }
        });
    }
}
