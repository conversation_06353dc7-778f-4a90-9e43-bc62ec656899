<?php

namespace App\Livewire\Events;

use App\Models\Event;
use App\Models\EventAttendee;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SimpleEventRegistration extends Component
{
    public Event $event;
    public $isRegistered = false;
    public $attendee = null;
    public $showModal = false;
    public $debugInfo = [];

    public function mount(Event $event)
    {
        $this->event = $event;
        $this->debugInfo[] = "Component mounted for event: {$event->name}";
        $this->checkRegistration();
    }

    public function render()
    {
        return view('livewire.events.simple-event-registration');
    }

    public function checkRegistration()
    {
        $this->debugInfo[] = "Checking registration...";

        if (Auth::check()) {
            $this->debugInfo[] = "User is authenticated: " . Auth::user()->name;

            $attendee = EventAttendee::where('event_id', $this->event->id)
                ->where('user_id', Auth::id())
                ->where('status', '!=', 'cancelled')
                ->first();

            $this->isRegistered = (bool) $attendee;
            $this->attendee = $attendee;

            $this->debugInfo[] = "Registration status: " . ($this->isRegistered ? 'Registered' : 'Not registered');
        } else {
            $this->debugInfo[] = "User is not authenticated";
            $this->isRegistered = false;
            $this->attendee = null;
        }
    }

    public function showRegistrationModal()
    {
        $this->debugInfo[] = "Show registration modal clicked";

        if (!Auth::check()) {
            $this->debugInfo[] = "User not authenticated, redirecting to login";
            return redirect()->route('login');
        }

        $this->debugInfo[] = "Opening modal";
        $this->showModal = true;
    }

    public function register()
    {
        $this->debugInfo[] = "Register method called";

        if (!Auth::check()) {
            $this->debugInfo[] = "User not authenticated";
            session()->flash('error', 'Você precisa estar logado para se inscrever.');
            return;
        }

        // Basic validations
        if (!$this->event->is_active) {
            $this->debugInfo[] = "Event is not active";
            session()->flash('error', 'Este evento não está mais disponível para inscrições.');
            $this->showModal = false;
            return;
        }

        if ($this->event->has_passed) {
            $this->debugInfo[] = "Event has passed";
            session()->flash('error', 'Este evento já ocorreu.');
            $this->showModal = false;
            return;
        }

        if ($this->event->is_sold_out) {
            $this->debugInfo[] = "Event is sold out";
            session()->flash('error', 'Este evento está esgotado.');
            $this->showModal = false;
            return;
        }

        if ($this->isRegistered) {
            $this->debugInfo[] = "User already registered";
            session()->flash('info', 'Você já está inscrito neste evento.');
            $this->showModal = false;
            return;
        }

        // Check if event is free
        if ($this->event->is_free) {
            $this->debugInfo[] = "Event is free, creating direct registration";

            try {
                $attendee = EventAttendee::create([
                    'event_id' => $this->event->id,
                    'user_id' => Auth::id(),
                    'status' => 'confirmed',
                    'ticket_code' => EventAttendee::generateTicketCode(),
                    'payment_status' => 'completed',
                    'payment_method' => 'free',
                    'amount_paid' => 0,
                    'paid_at' => now(),
                ]);

                $this->debugInfo[] = "Free registration created successfully with ID: {$attendee->id}";

                $this->attendee = $attendee;
                $this->isRegistered = true;
                $this->showModal = false;

                session()->flash('success', 'Inscrição realizada com sucesso! Código: ' . $attendee->ticket_code);

            } catch (\Exception $e) {
                $this->debugInfo[] = "Error creating free registration: " . $e->getMessage();
                session()->flash('error', 'Erro ao processar inscrição: ' . $e->getMessage());
                $this->showModal = false;
            }
            return;
        }

        // Event is paid - redirect to payment
        $this->debugInfo[] = "Event is paid (R$ {$this->event->price}), redirecting to payment";

        try {
            // Create pending registration
            $attendee = EventAttendee::create([
                'event_id' => $this->event->id,
                'user_id' => Auth::id(),
                'status' => 'registered',
                'payment_status' => 'pending',
            ]);

            $this->debugInfo[] = "Pending registration created with ID: {$attendee->id}";

            // Redirect to controller for Stripe payment
            return redirect()->route('events.register', $this->event);

        } catch (\Exception $e) {
            $this->debugInfo[] = "Error creating paid registration: " . $e->getMessage();
            session()->flash('error', 'Erro ao processar inscrição: ' . $e->getMessage());
            $this->showModal = false;
        }
    }

    public function cancelRegistration()
    {
        $this->debugInfo[] = "Cancel registration called";

        if (!$this->isRegistered || !$this->attendee) {
            $this->debugInfo[] = "User not registered";
            session()->flash('error', 'Você não está inscrito neste evento.');
            return;
        }

        try {
            $this->attendee->update(['status' => 'cancelled']);

            $this->debugInfo[] = "Registration cancelled successfully";

            $this->isRegistered = false;
            $this->attendee = null;

            session()->flash('success', 'Sua inscrição foi cancelada.');

        } catch (\Exception $e) {
            $this->debugInfo[] = "Error cancelling registration: " . $e->getMessage();
            session()->flash('error', 'Erro ao cancelar inscrição: ' . $e->getMessage());
        }
    }

    public function clearDebug()
    {
        $this->debugInfo = [];
    }
}
