{{-- Exemp<PERSON> de Tooltips no Monte Sua Noite --}}
<x-layouts.app>
    <div class="min-h-screen bg-zinc-50 dark:bg-zinc-900 p-4">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-title mb-2">Tooltips no Monte Sua Noite</h1>
                <p class="text-body">Demonstração dos tooltips aplicados ao sistema Monte Sua Noite com fundo preto e letras brancas.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Blocos Temáticos com Tooltips -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-4">
                        <h3 class="text-lg font-semibold text-title mb-4"><PERSON>os Te<PERSON>á<PERSON></h3>
                        <div class="space-y-3">
                            <flux:tooltip content="Experiências gastronômicas - restaurantes, bares, food trucks" position="right">
                                <div class="flex items-center space-x-3 p-2 bg-zinc-50 dark:bg-zinc-700 rounded-lg cursor-help">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #E60073; color: white;">
                                        🍽️
                                    </div>
                                    <span class="text-sm text-title">Gastronomia</span>
                                </div>
                            </flux:tooltip>

                            <flux:tooltip content="Entretenimento noturno - shows, música ao vivo, karaokê" position="right">
                                <div class="flex items-center space-x-3 p-2 bg-zinc-50 dark:bg-zinc-700 rounded-lg cursor-help">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #00FFF7; color: black;">
                                        🎵
                                    </div>
                                    <span class="text-sm text-title">Entretenimento</span>
                                </div>
                            </flux:tooltip>

                            <flux:tooltip content="Atividades culturais - museus, teatros, exposições" position="right">
                                <div class="flex items-center space-x-3 p-2 bg-zinc-50 dark:bg-zinc-700 rounded-lg cursor-help">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #FFE600; color: black;">
                                        🎭
                                    </div>
                                    <span class="text-sm text-title">Cultura</span>
                                </div>
                            </flux:tooltip>

                            <flux:tooltip content="Vida noturna - baladas, pubs, casas noturnas" position="right">
                                <div class="flex items-center space-x-3 p-2 bg-zinc-50 dark:bg-zinc-700 rounded-lg cursor-help">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #9D4EDD; color: white;">
                                        🌙
                                    </div>
                                    <span class="text-sm text-title">Vida Noturna</span>
                                </div>
                            </flux:tooltip>

                            <flux:tooltip content="Atividades ao ar livre - parques, caminhadas, mirantes" position="right">
                                <div class="flex items-center space-x-3 p-2 bg-zinc-50 dark:bg-zinc-700 rounded-lg cursor-help">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #06FFA5; color: black;">
                                        🌳
                                    </div>
                                    <span class="text-sm text-title">Outdoor</span>
                                </div>
                            </flux:tooltip>

                            <flux:tooltip content="Experiências românticas - jantares íntimos, passeios especiais" position="right">
                                <div class="flex items-center space-x-3 p-2 bg-zinc-50 dark:bg-zinc-700 rounded-lg cursor-help">
                                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #FF6B6B; color: white;">
                                        💕
                                    </div>
                                    <span class="text-sm text-title">Romance</span>
                                </div>
                            </flux:tooltip>
                        </div>
                    </div>
                </div>

                <!-- Tabuleiro Simulado -->
                <div class="lg:col-span-3">
                    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                        <h3 class="text-lg font-semibold text-title mb-4">Tabuleiro Interativo</h3>
                        <p class="text-sm text-body mb-4">
                            <span class="font-medium">Casas Pretas:</span> Arraste blocos temáticos aqui |
                            <span class="font-medium">Casas Brancas:</span> Disponíveis para patrocínio
                        </p>

                        <!-- Tabuleiro 4x4 para demonstração -->
                        <div class="grid grid-cols-4 gap-2 max-w-md mx-auto">
                            @for($i = 1; $i <= 16; $i++)
                                @php
                                    $isBlack = ($i + floor(($i-1)/4)) % 2 === 0;
                                    $hasBlock = in_array($i, [2, 6, 11, 15]);
                                    $hasText = in_array($i, [2, 11]);
                                    $isSponsored = in_array($i, [3, 7, 12]);
                                @endphp

                                <div class="aspect-square border border-zinc-300 dark:border-zinc-600 rounded-lg relative {{ $isBlack ? 'bg-zinc-800 dark:bg-zinc-900' : 'bg-zinc-100 dark:bg-zinc-200' }} flex items-center justify-center">
                                    <!-- Posição -->
                                    <div class="absolute top-1 left-1 text-xs font-mono {{ $isBlack ? 'text-zinc-400' : 'text-zinc-600' }}">
                                        {{ chr(65 + floor(($i-1)/4)) . (($i-1) % 4 + 1) }}
                                    </div>

                                    @if($hasBlock && $isBlack)
                                        <!-- Bloco com texto personalizado -->
                                        <div class="text-center w-full p-1">
                                            <div class="text-lg mb-1">
                                                @if($i === 2) 🍽️
                                                @elseif($i === 6) 🎵
                                                @elseif($i === 11) 🌙
                                                @elseif($i === 15) 💕
                                                @endif
                                            </div>
                                            <div class="text-xs text-zinc-300 font-medium mb-1">
                                                @if($i === 2) Gastronomia
                                                @elseif($i === 6) Show
                                                @elseif($i === 11) Balada
                                                @elseif($i === 15) Romance
                                                @endif
                                            </div>

                                            @if($hasText)
                                                <!-- Texto personalizado com tooltip -->
                                                <flux:tooltip content="@if($i === 2)Jantar no restaurante italiano do centro da cidade com vista para o rio@elseif($i === 11)Balada na casa noturna mais badalada da região com DJ internacional@endif" position="top">
                                                    <div class="custom-text-indicator text-zinc-200 cursor-help">
                                                        <div class="flex items-center justify-center">
                                                            <flux:icon name="document-text" class="w-3 h-3 text-cyan-400" />
                                                            <span class="ml-1 text-xs">Texto</span>
                                                        </div>
                                                    </div>
                                                </flux:tooltip>
                                            @endif
                                        </div>
                                    @elseif(!$isBlack)
                                        @if($isSponsored)
                                            <!-- Casa patrocinada -->
                                            <flux:tooltip content="Patrocinado por João Silva - R$ 25,00 - 'Ótima ideia de plano!'" position="top">
                                                <div class="text-center cursor-help">
                                                    <div class="w-6 h-6 bg-green-500 rounded-full mx-auto mb-1 flex items-center justify-center">
                                                        <flux:icon name="currency-dollar" class="w-3 h-3 text-white" />
                                                    </div>
                                                    <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                                                        R$ 25,00
                                                    </div>
                                                </div>
                                            </flux:tooltip>
                                        @else
                                            <!-- Casa disponível para patrocínio -->
                                            <flux:tooltip content="Clique para patrocinar esta posição e apoiar o criador do plano" position="top">
                                                <div class="text-center opacity-50 hover:opacity-100 transition-opacity cursor-pointer">
                                                    <flux:icon name="plus" class="w-6 h-6 mx-auto text-green-500" />
                                                    <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                                                        Patrocinar
                                                    </div>
                                                </div>
                                            </flux:tooltip>
                                        @endif
                                    @endif
                                </div>
                            @endfor
                        </div>

                        <!-- Legenda com tooltips -->
                        <div class="mt-6 flex justify-center space-x-6 text-sm">
                            <flux:tooltip content="Experiências planejadas pelo criador do plano" position="top">
                                <div class="flex items-center space-x-2 cursor-help">
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                    <span class="text-body">Experiências</span>
                                </div>
                            </flux:tooltip>

                            <flux:tooltip content="Posições patrocinadas por outros usuários" position="top">
                                <div class="flex items-center space-x-2 cursor-help">
                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                    <span class="text-body">Patrocínios</span>
                                </div>
                            </flux:tooltip>

                            <flux:tooltip content="Texto personalizado descrevendo a experiência em detalhes" position="top">
                                <div class="flex items-center space-x-2 cursor-help">
                                    <flux:icon name="document-text" class="w-3 h-3 text-cyan-400" />
                                    <span class="text-body">Texto Personalizado</span>
                                </div>
                            </flux:tooltip>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Botões de Ação com Tooltips -->
            <div class="mt-8 flex justify-center space-x-4">
                <flux:tooltip content="Salvar o plano atual e torná-lo disponível para visualização" kbd="Ctrl+S" position="top">
                    <flux:button variant="primary" class="night-board-btn neon-box-green">
                        <flux:icon name="check" class="w-4 h-4 mr-2" />
                        Salvar Plano
                    </flux:button>
                </flux:tooltip>

                <flux:tooltip content="Visualizar como o plano aparecerá para outros usuários" position="top">
                    <flux:button variant="outline">
                        <flux:icon name="eye" class="w-4 h-4 mr-2" />
                        Pré-visualizar
                    </flux:button>
                </flux:tooltip>

                <flux:tooltip content="Limpar todo o tabuleiro e começar novamente" position="top">
                    <flux:button variant="ghost">
                        <flux:icon name="trash" class="w-4 h-4 mr-2" />
                        Limpar Tabuleiro
                    </flux:button>
                </flux:tooltip>
            </div>

            <!-- Informações sobre os Tooltips -->
            <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <h3 class="text-md font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    Tooltips Aplicados no Monte Sua Noite:
                </h3>
                <div class="text-sm text-blue-700 dark:text-blue-300 space-y-2">
                    <p><strong>✅ Texto Personalizado:</strong> Agora usa tooltips do Flux UI com fundo preto e letras brancas</p>
                    <p><strong>✅ Blocos Temáticos:</strong> Explicações detalhadas sobre cada categoria</p>
                    <p><strong>✅ Patrocínios:</strong> Informações sobre quem patrocinou e valores</p>
                    <p><strong>✅ Botões de Ordenação:</strong> Explicações sobre cada tipo de ordenação</p>
                    <p><strong>✅ Estatísticas:</strong> Detalhes sobre criador, data, valores e contadores</p>
                    <p><strong>✅ Preview do Tabuleiro:</strong> Legenda explicativa dos elementos visuais</p>
                </div>
            </div>

            <!-- Links para Testar -->
            <div class="mt-6 text-center">
                <div class="space-x-4">
                    <flux:tooltip content="Acessar o sistema real do Monte Sua Noite" position="top">
                        <flux:button href="{{ route('night-board.index') }}" variant="primary" wire:navigate>
                            <flux:icon name="puzzle-piece" class="w-4 h-4 mr-2" />
                            Ir para Monte Sua Noite
                        </flux:button>
                    </flux:tooltip>

                    <flux:tooltip content="Criar um novo plano da noite" position="top">
                        <flux:button href="{{ route('night-board.create') }}" variant="outline" wire:navigate>
                            <flux:icon name="plus" class="w-4 h-4 mr-2" />
                            Criar Plano
                        </flux:button>
                    </flux:tooltip>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
