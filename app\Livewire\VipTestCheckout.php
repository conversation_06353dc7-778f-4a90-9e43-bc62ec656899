<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;
use App\Models\VipSubscription;

class VipTestCheckout extends Component
{
    public $testResults = [];
    public $isRunning = false;

    public function testVipCheckout()
    {
        $this->isRunning = true;
        $this->testResults = [];

        if (!Auth::check()) {
            $this->testResults[] = [
                'test' => 'Autenticação',
                'status' => 'error',
                'message' => 'Usuário não autenticado',
                'details' => []
            ];
            $this->isRunning = false;
            return;
        }

        $user = Auth::user();
        $planDays = 30;
        $price = 48.00;

        try {
            // Criar uma assinatura de teste
            $subscription = VipSubscription::create([
                'user_id' => $user->id,
                'plan_days' => $planDays,
                'amount' => $price,
                'status' => 'pending',
            ]);

            $this->testResults[] = [
                'test' => 'Criação de Assinatura',
                'status' => 'success',
                'message' => 'Assinatura criada com sucesso',
                'details' => [
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'plan_days' => $planDays,
                    'amount' => $price
                ]
            ];

            // Configurar Stripe
            Stripe::setApiKey(config('cashier.secret'));

            $this->testResults[] = [
                'test' => 'Configuração Stripe',
                'status' => 'success',
                'message' => 'Stripe configurado',
                'details' => [
                    'api_key_configured' => !empty(config('cashier.secret')),
                    'currency' => config('cashier.currency', 'brl')
                ]
            ];

            // Tentar criar sessão de checkout
            $sessionData = [
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'brl',
                        'product_data' => [
                            'name' => "Assinatura VIP {$planDays} dias",
                            'description' => "Assinatura VIP por {$planDays} dias",
                        ],
                        'unit_amount' => (int)($price * 100), // Convert to cents
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => route('vip.payment.success', ['subscription' => $subscription->id]),
                'cancel_url' => route('vip.payment.cancel', ['subscription' => $subscription->id]),
                'customer_email' => $user->email,
                'metadata' => [
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'plan_days' => $planDays,
                ],
            ];

            Log::info('Tentando criar sessão de checkout Stripe', $sessionData);

            $session = Session::create($sessionData);

            $this->testResults[] = [
                'test' => 'Criação de Sessão Stripe',
                'status' => 'success',
                'message' => 'Sessão criada com sucesso',
                'details' => [
                    'session_id' => $session->id,
                    'session_url' => $session->url,
                    'payment_status' => $session->payment_status,
                    'amount_total' => $session->amount_total
                ]
            ];

            // Atualizar assinatura com session ID
            $subscription->update([
                'stripe_session_id' => $session->id,
            ]);

            // Limpar a assinatura de teste após 1 minuto (para não poluir o banco)
            $subscription->delete();

        } catch (ApiErrorException $e) {
            $this->testResults[] = [
                'test' => 'Criação de Sessão Stripe',
                'status' => 'error',
                'message' => 'Erro da API Stripe: ' . $e->getMessage(),
                'details' => [
                    'error_type' => get_class($e),
                    'error_code' => $e->getCode(),
                    'stripe_code' => $e->getStripeCode() ?? 'N/A',
                    'decline_code' => method_exists($e, 'getDeclineCode') ? $e->getDeclineCode() : 'N/A',
                    'http_status' => $e->getHttpStatus() ?? 'N/A'
                ]
            ];

            // Limpar assinatura se criada
            if (isset($subscription)) {
                $subscription->delete();
            }
        } catch (\Exception $e) {
            $this->testResults[] = [
                'test' => 'Teste Geral',
                'status' => 'error',
                'message' => 'Erro geral: ' . $e->getMessage(),
                'details' => [
                    'error_type' => get_class($e),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ];

            // Limpar assinatura se criada
            if (isset($subscription)) {
                $subscription->delete();
            }
        }

        $this->isRunning = false;
    }

    public function clearResults()
    {
        $this->testResults = [];
    }

    public function render()
    {
        return view('livewire.vip-test-checkout');
    }
}
