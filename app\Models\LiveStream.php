<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LiveStream extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'status',
        'stream_key',
        'viewers_count',
        'total_donations',
        'settings',
        'video_path',
        'duration',
        'has_replay',
        'started_at',
        'ended_at',
    ];

    protected $casts = [
        'settings' => 'array',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'total_donations' => 'decimal:2',
        'duration' => 'integer',
        'has_replay' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($liveStream) {
            $liveStream->stream_key = Str::random(32);
        });
    }

    /**
     * Usuário que está fazendo a live
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mensagens do chat da live
     */
    public function messages()
    {
        return $this->hasMany(LiveStreamMessage::class);
    }

    /**
     * Doações recebidas na live
     */
    public function donations()
    {
        return $this->hasMany(LiveStreamDonation::class);
    }

    /**
     * Verifica se a live está ativa
     */
    public function isLive()
    {
        return $this->status === 'live';
    }

    /**
     * Verifica se a live está aguardando
     */
    public function isWaiting()
    {
        return $this->status === 'waiting';
    }

    /**
     * Verifica se a live terminou
     */
    public function isEnded()
    {
        return $this->status === 'ended';
    }

    /**
     * Inicia a live
     */
    public function start()
    {
        $this->update([
            'status' => 'live',
            'started_at' => now(),
        ]);
    }

    /**
     * Termina a live
     */
    public function end()
    {
        $this->update([
            'status' => 'ended',
            'ended_at' => now(),
        ]);
    }

    /**
     * Incrementa o contador de visualizadores
     */
    public function incrementViewers()
    {
        $this->increment('viewers_count');
    }

    /**
     * Decrementa o contador de visualizadores
     */
    public function decrementViewers()
    {
        $this->decrement('viewers_count');
    }

    /**
     * Adiciona uma doação ao total
     */
    public function addDonation($amount)
    {
        $this->increment('total_donations', $amount);
    }

    /**
     * Scope para lives ativas
     */
    public function scopeLive($query)
    {
        return $query->where('status', 'live');
    }

    /**
     * Scope para lives aguardando
     */
    public function scopeWaiting($query)
    {
        return $query->where('status', 'waiting');
    }

    /**
     * Scope para lives terminadas
     */
    public function scopeEnded($query)
    {
        return $query->where('status', 'ended');
    }

    /**
     * Verifica se a live tem replay disponível
     */
    public function hasReplay()
    {
        return $this->has_replay && !empty($this->video_path);
    }

    /**
     * Obtém a URL do vídeo de replay
     */
    public function getReplayUrl()
    {
        if (!$this->hasReplay()) {
            return null;
        }

        return Storage::url($this->video_path);
    }

    /**
     * Salva o replay da live
     */
    public function saveReplay($videoPath, $duration = null)
    {
        $this->update([
            'video_path' => $videoPath,
            'duration' => $duration,
            'has_replay' => true,
        ]);
    }

    /**
     * Formata a duração da live
     */
    public function getFormattedDuration()
    {
        if (!$this->duration) {
            return '00:00';
        }

        $hours = floor($this->duration / 3600);
        $minutes = floor(($this->duration % 3600) / 60);
        $seconds = $this->duration % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }

        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Scope para lives com replay
     */
    public function scopeWithReplay($query)
    {
        return $query->where('has_replay', true)->whereNotNull('video_path');
    }
}
