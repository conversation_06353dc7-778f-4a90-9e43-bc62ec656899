<?php

namespace Database\Factories;

use App\Models\JobApplication;
use App\Models\JobVacancy;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobApplication>
 */
class JobApplicationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = JobApplication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user = User::factory()->create();
        $job = JobVacancy::factory()->create();

        return [
            'job_id' => $job->id,
            'job_vacancy_id' => $job->id,
            'user_id' => $user->id,
            'status' => $this->faker->randomElement(['Pendente', 'Em Análise', 'Aprovada', 'Rejeitada', 'Contratada']),
            'cover_letter' => $this->faker->paragraphs(3, true),
            'resume_path' => 'job-applications/resume_' . $user->id . '_' . time() . '.pdf',
            'resume_filename' => 'Curriculo_' . $user->name . '.pdf',
            'admin_notes' => $this->faker->optional(0.3)->sentence(),
            'reviewed_at' => $this->faker->optional(0.5)->dateTimeBetween('-1 month', 'now'),
            'reviewed_by' => $this->faker->optional(0.5)->randomElement(User::where('role', 'administrador')->pluck('id')->toArray()),
            'is_vip_priority' => $this->faker->boolean(20),
            
            // Campos de auditoria
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'form_data' => [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $this->faker->phoneNumber(),
                'experience' => $this->faker->optional(0.7)->paragraph(),
                'cover_letter_length' => $this->faker->numberBetween(100, 2000),
                'has_resume' => true,
                'submitted_at' => now()->toISOString(),
                'browser_info' => [
                    'user_agent' => $this->faker->userAgent(),
                    'ip' => $this->faker->ipv4(),
                    'referer' => $this->faker->url(),
                ],
            ],
            'email_sent_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 week', 'now'),
            'email_confirmed_at' => $this->faker->optional(0.6)->dateTimeBetween('-1 week', 'now'),
            'email_log' => [
                [
                    'type' => 'admin_notification',
                    'recipient' => '<EMAIL>',
                    'sent_at' => now()->toISOString(),
                    'success' => true,
                    'error' => null,
                ],
                [
                    'type' => 'candidate_confirmation',
                    'recipient' => $user->email,
                    'sent_at' => now()->toISOString(),
                    'success' => true,
                    'error' => null,
                ],
            ],
            
            // Dados adicionais do candidato
            'candidate_name' => $user->name,
            'candidate_email' => $user->email,
            'candidate_phone' => $this->faker->phoneNumber(),
            'candidate_experience' => $this->faker->optional(0.7)->paragraph(),
            
            // Campos de status avançado
            'priority_level' => $this->faker->randomElement(['baixa', 'normal', 'alta', 'urgente']),
            'status_history' => [
                [
                    'from' => null,
                    'to' => 'Pendente',
                    'changed_by' => $user->id,
                    'changed_at' => now()->toISOString(),
                    'notes' => 'Candidatura inicial',
                ],
            ],
            'last_status_change' => now(),
            'status_changed_by' => $user->id,
        ];
    }

    /**
     * Indicate that the application is VIP.
     */
    public function vip(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_vip_priority' => true,
            'priority_level' => 'alta',
        ]);
    }

    /**
     * Indicate that the application is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Pendente',
            'reviewed_at' => null,
            'reviewed_by' => null,
        ]);
    }

    /**
     * Indicate that the application is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Aprovada',
            'reviewed_at' => now(),
            'reviewed_by' => User::factory()->create(['role' => 'administrador'])->id,
        ]);
    }

    /**
     * Indicate that the application is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Rejeitada',
            'reviewed_at' => now(),
            'reviewed_by' => User::factory()->create(['role' => 'administrador'])->id,
            'admin_notes' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the application has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority_level' => 'alta',
        ]);
    }

    /**
     * Indicate that the application has urgent priority.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority_level' => 'urgente',
        ]);
    }
}
