# Dependências
/vendor/
/node_modules/
npm-debug.log
yarn-error.log
package-lock.json
yarn.lock
composer.lock

# Laravel 12 specific
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json

# Storage e Cache
/storage/*.key
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/framework/testing/*

# Build e Assets
/public/build
/public/hot
/public/storage
/public/css
/public/js
/public/mix-manifest.json

# IDEs e Editores
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.DS_Store
Thumbs.db

# Arquivos de Teste
/coverage
.phpunit.cache/
.phpunit.result.cache

# Arquivos de Banco de Dados
*.sqlite
*.sql
*.sqlite-journal

# Arquivos de Build específicos
/livewire/build/
/flux-ui/build/
/tailwind/build/

# Configurações
tailwind.config.js
postcss.config.js
vite.config.js
.phpstorm.meta.php
_ide_helper.php
_ide_helper_models.php

# Manter migrations
!database/migrations/*.php

# Arquivos de Seeds
database/seeders/*.php

# Configurações do KingHost
kinghost.config.js

# Ignore arquivos de configuração do Git
.gitattributes

# Ignore arquivos de dependências do Composer
vendor/

# Ignore arquivos de node_modules
node_modules/

# Ignore arquivos de configuração do Tailwind
tailwind/build/

# Ignore arquivos de teste
tests/*.php