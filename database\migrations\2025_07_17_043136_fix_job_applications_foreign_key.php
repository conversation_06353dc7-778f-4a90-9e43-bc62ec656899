<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_applications', function (Blueprint $table) {
            // Remover a foreign key incorreta
            $table->dropForeign(['job_id']);

            // Adicionar a foreign key correta
            $table->foreign('job_id')->references('id')->on('job_vacancies')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_applications', function (Blueprint $table) {
            // Remover a foreign key correta
            $table->dropForeign(['job_id']);

            // Restaurar a foreign key incorreta (para rollback)
            $table->foreign('job_id')->references('id')->on('jobs')->onDelete('cascade');
        });
    }
};
