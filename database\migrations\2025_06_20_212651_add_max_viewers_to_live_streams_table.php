<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('live_streams', function (Blueprint $table) {
            if (!Schema::hasColumn('live_streams', 'max_viewers')) {
                $table->integer('max_viewers')->default(0)->after('viewers_count');
            }
            if (!Schema::hasColumn('live_streams', 'last_optimized_at')) {
                $table->timestamp('last_optimized_at')->nullable()->after('ended_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('live_streams', function (Blueprint $table) {
            $table->dropColumn(['max_viewers', 'last_optimized_at']);
        });
    }
};
