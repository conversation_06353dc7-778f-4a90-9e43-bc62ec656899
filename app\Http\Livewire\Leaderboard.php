<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\User;

class Leaderboard extends Component
{
    public $topUsers;

    public function mount()
    {
        // Fetch top 10 users with ranking points > 0, ordered by ranking points
        $this->topUsers = User::where('ranking_points', '>', 0)
            ->orderBy('ranking_points', 'desc')
            ->take(10)
            ->get();
    }

    public function render()
    {
        return view('livewire.leaderboard', [
            'topUsers' => $this->topUsers, // Pass the variable to the view
        ]);
    }
}
