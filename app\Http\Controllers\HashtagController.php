<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Hashtag;
use App\Models\Post;
use Livewire\WithPagination;

class HashtagController extends Controller
{
    use WithPagination;

    /**
     * Exibir posts de uma hashtag específica
     */
    public function show(Hashtag $hashtag)
    {
        // Carregar os posts relacionados à hashtag com paginação
        $posts = $hashtag->posts()->with(['user'])->latest()->paginate(15); // Usar paginate() em vez de get()

        return view('hashtag.show', compact('hashtag', 'posts'));
    }

    /**
     * API para buscar hashtags (autocomplete)
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 1) {
            return response()->json([]);
        }

        $hashtags = Hashtag::searchByName($query, 10);
        
        return response()->json($hashtags->map(function ($hashtag) {
            return [
                'name' => $hashtag->name,
                'posts_count' => $hashtag->posts_count,
                'exists' => true
            ];
        }));
    }

    /**
     * Listar todas as hashtags
     */
    public function index()
    {
        $hashtags = Hashtag::orderBy('posts_count', 'desc')
            ->paginate(20);

        return view('hashtag.index', compact('hashtags'));
    }
}
