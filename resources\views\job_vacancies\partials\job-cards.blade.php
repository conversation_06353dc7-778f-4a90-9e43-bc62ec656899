<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    @foreach($job_vacancies as $job)
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 group">
            <!-- Header with Category and Featured Badge -->
            <div class="p-6 pb-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-2">
                        <flux:icon icon="tag" class="w-4 h-4 text-gray-400" />
                        <span class="text-xs font-semibold uppercase text-primary bg-primary/10 px-2 py-1 rounded-full">
                            {{$job->category->name}}
                        </span>
                    </div>
                    @if($job->is_featured)
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                            <flux:icon icon="star" class="w-3 h-3 mr-1" />
                            Destaque
                        </span>
                    @endif
                </div>

                <!-- Job Title -->
                <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors">
                    <a href="{{route('job_vacancies.show', $job->slug)}}" class="hover:underline">
                        {{$job->title}}
                    </a>
                </h3>

                <!-- Job Description -->
                <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3 mb-4">
                    {{$job->description}}
                </p>

                <!-- Job Details Tags -->
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                        <flux:icon icon="document-text" class="w-3 h-3 mr-1" />
                        {{$job->contract_type}}
                    </span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                        <flux:icon icon="map-pin" class="w-3 h-3 mr-1" />
                        {{$job->work_mode}}
                    </span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                        <flux:icon icon="academic-cap" class="w-3 h-3 mr-1" />
                        {{$job->experience_level}}
                    </span>
                </div>

                <!-- Location and Deadline -->
                @if($job->location || $job->application_deadline)
                    <div class="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400 mb-4">
                        @if($job->location)
                            <div class="flex items-center gap-1">
                                <flux:icon icon="map-pin" class="w-3 h-3" />
                                <span>{{$job->location}}</span>
                            </div>
                        @endif
                        @if($job->application_deadline)
                            <div class="flex items-center gap-1">
                                <flux:icon icon="calendar" class="w-3 h-3" />
                                <span>Até {{$job->application_deadline->format('d/m/Y')}}</span>
                            </div>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Footer with Salary and Action -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-zinc-700 rounded-b-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-lg font-bold text-primary">{{$job->formatted_salary}}</div>
                        @if($job->salary_negotiable)
                            <div class="text-xs text-gray-500 dark:text-gray-400">Negociável</div>
                        @endif
                    </div>
                    <flux:button href="{{route('job_vacancies.show', $job->slug)}}" variant="primary" size="sm">
                        <flux:icon icon="eye" class="w-4 h-4 mr-1" />
                        Ver Detalhes
                    </flux:button>
                </div>
            </div>

            <!-- Stats Footer -->
            <div class="px-6 py-2 bg-gray-100 dark:bg-zinc-600 rounded-b-lg">
                <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <div class="flex items-center gap-4">
                        <span class="flex items-center gap-1">
                            <flux:icon icon="eye" class="w-3 h-3" />
                            {{$job->views_count}} visualizações
                        </span>
                        <span class="flex items-center gap-1">
                            <flux:icon icon="users" class="w-3 h-3" />
                            {{$job->applications_count}} candidaturas
                        </span>
                    </div>
                    <span>{{$job->created_at->diffForHumans()}}</span>
                </div>
            </div>
        </div>
    @endforeach
</div>