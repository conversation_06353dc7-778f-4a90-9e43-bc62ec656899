<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Hashtag;
use App\Models\Post;

class HashtagPosts extends Component
{
    use WithPagination;

    public $hashtag;
    public $limit = 10;

    public function mount(Hashtag $hashtag)
    {
        $this->hashtag = $hashtag;
    }

    public function loadMore()
    {
        $this->limit += 10;
    }

    public function render()
    {
        $posts = $this->hashtag->posts()
            ->with([
                'user.userPhotos' => function($query) {
                    $query->latest()->take(1);
                },
                'likedByUsers',
                'comments.user.userPhotos' => function($query) {
                    $query->latest()->take(1);
                },
                'hashtags'
            ])
            ->latest()
            ->take($this->limit)
            ->get();

        return view('livewire.hashtag-posts', compact('posts'));
    }
}
