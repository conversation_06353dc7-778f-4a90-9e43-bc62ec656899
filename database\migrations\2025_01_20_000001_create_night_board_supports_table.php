<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('night_board_supports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Quem patrocinou
            $table->foreignId('plan_id')->constrained('night_board_plans')->onDelete('cascade');
            $table->string('position'); // Ex: "B2", "A1"
            $table->decimal('amount', 10, 2); // Valor do patrocínio
            $table->text('comment')->nullable(); // Comentário do patrocinador
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('night_board_supports');
    }
};
