<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CleanExpiredSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sessions:clean {--days=7 : Número de dias para manter sessões}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Limpa sessões expiradas do banco de dados';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Limpando sessões mais antigas que {$days} dias...");

        // Conta quantas sessões serão removidas
        $count = DB::table('sessions')
            ->where('last_activity', '<', $cutoffDate->timestamp)
            ->count();

        if ($count === 0) {
            $this->info('Nenhuma sessão expirada encontrada.');
            return 0;
        }

        // Remove as sessões expiradas
        $deleted = DB::table('sessions')
            ->where('last_activity', '<', $cutoffDate->timestamp)
            ->delete();

        $this->info("Removidas {$deleted} sessões expiradas.");

        // Mostra estatísticas
        $totalSessions = DB::table('sessions')->count();
        $activeSessions = DB::table('sessions')
            ->where('last_activity', '>=', Carbon::now()->subDay()->timestamp)
            ->count();

        $this->info("Estatísticas:");
        $this->info("- Total de sessões: {$totalSessions}");
        $this->info("- Sessões ativas (últimas 24h): {$activeSessions}");

        return 0;
    }
} 