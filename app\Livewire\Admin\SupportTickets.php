<?php

namespace App\Livewire\Admin;

use App\Models\SupportTicket;
use App\Models\User;
use App\Notifications\SupportTicketStatusChanged;
use Livewire\Component;
use Livewire\WithPagination;

class SupportTickets extends Component
{
    use WithPagination;

    public $statusFilter = '';
    public $priorityFilter = '';
    public $categoryFilter = '';
    public $searchQuery = '';
    public $selectedTicket = null;
    public $showAssignModal = false;
    public $assignedTo = null;

    protected $queryString = [
        'statusFilter' => ['except' => ''],
        'priorityFilter' => ['except' => ''],
        'categoryFilter' => ['except' => ''],
        'searchQuery' => ['except' => ''],
    ];

    public function mount()
    {
        // Verificar se é admin
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Acesso negado.');
        }
    }

    public function updatedSearchQuery()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedPriorityFilter()
    {
        $this->resetPage();
    }

    public function updatedCategoryFilter()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->statusFilter = '';
        $this->priorityFilter = '';
        $this->categoryFilter = '';
        $this->searchQuery = '';
        $this->resetPage();
    }

    public function changeStatus($ticketId, $newStatus)
    {
        $ticket = SupportTicket::find($ticketId);
        if (!$ticket) return;

        $oldStatus = $ticket->status;
        
        $updateData = ['status' => $newStatus];
        
        if ($newStatus === 'resolvido') {
            $updateData['resolved_at'] = now();
        } elseif ($newStatus === 'fechado') {
            $updateData['closed_at'] = now();
        }

        $ticket->update($updateData);

        // Notificar usuário
        $ticket->user->notify(new SupportTicketStatusChanged($ticket, $oldStatus, $newStatus));

        $this->dispatch('notify', [
            'message' => "Status do ticket #{$ticket->ticket_number} alterado para {$ticket->status_label}",
            'type' => 'success'
        ]);
    }

    public function openAssignModal($ticketId)
    {
        $this->selectedTicket = SupportTicket::find($ticketId);
        $this->assignedTo = $this->selectedTicket->assigned_to;
        $this->showAssignModal = true;
    }

    public function closeAssignModal()
    {
        $this->showAssignModal = false;
        $this->selectedTicket = null;
        $this->assignedTo = null;
    }

    public function assignTicket()
    {
        if (!$this->selectedTicket) return;

        $this->selectedTicket->update([
            'assigned_to' => $this->assignedTo
        ]);

        $this->dispatch('notify', [
            'message' => "Ticket #{$this->selectedTicket->ticket_number} atribuído com sucesso",
            'type' => 'success'
        ]);

        $this->closeAssignModal();
    }

    public function getTicketsProperty()
    {
        $query = SupportTicket::with(['user', 'assignedTo'])
            ->orderBy('created_at', 'desc');

        if ($this->statusFilter) {
            $query->where('status', $this->statusFilter);
        }

        if ($this->priorityFilter) {
            $query->where('priority', $this->priorityFilter);
        }

        if ($this->categoryFilter) {
            $query->where('category', $this->categoryFilter);
        }

        if ($this->searchQuery) {
            $query->where(function ($q) {
                $q->where('title', 'like', "%{$this->searchQuery}%")
                  ->orWhere('description', 'like', "%{$this->searchQuery}%")
                  ->orWhere('ticket_number', 'like', "%{$this->searchQuery}%")
                  ->orWhereHas('user', function ($userQuery) {
                      $userQuery->where('name', 'like', "%{$this->searchQuery}%")
                               ->orWhere('email', 'like', "%{$this->searchQuery}%");
                  });
            });
        }

        return $query->paginate(15);
    }

    public function getStatsProperty()
    {
        return [
            'total' => SupportTicket::count(),
            'open' => SupportTicket::open()->count(),
            'closed' => SupportTicket::closed()->count(),
            'high_priority' => SupportTicket::where('priority', 'alta')->orWhere('priority', 'urgente')->count(),
            'unassigned' => SupportTicket::whereNull('assigned_to')->count(),
        ];
    }

    public function getAdminsProperty()
    {
        return User::where('role', 'admin')->get();
    }

    public function render()
    {
        return view('livewire.admin.support-tickets');
    }
}
