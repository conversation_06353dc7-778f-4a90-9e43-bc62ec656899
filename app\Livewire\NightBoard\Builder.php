<?php

namespace App\Livewire\NightBoard;

use Livewire\Component;
use App\Models\NightBoardPlan;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Auth;
use Livewire\WithFileUploads;


class Builder extends Component
{
    use WithFileUploads;

    #[Validate('required|string|max:255')]
    public $title = '';

    #[Validate('nullable|string|max:1000')]
    public $description = '';

    #[Validate('boolean')]
    public $is_public = true;

    public $board = [];
    public $selectedBlock = null;
    public $showSaveModal = false;
    public $showTextModal = false;
    public $showImageModal = false;
    public $currentPosition = null;
    public $customText = '';
    public $editingPosition = null;
    public $squareImage = null;
    public $modalImageUrl = '';

    // Tipos de blocos temáticos
    public function getBlockTypesProperty()
    {
        return [
            'ambiente' => [
                'name' => 'Ambiente',
                'icon' => 'cube-transparent',
                'color' => '#E60073', // Rosa neon
            ],
            'bebida' => [
                'name' => 'Bebida',
                'icon' => 'beaker',
                'color' => '#00FFF7', // Ciano neon
            ],
            'musica' => [
                'name' => 'Música',
                'icon' => 'musical-note',
                'color' => '#FFE600', // Amarelo neon
            ],
            'acao_fisica' => [
                'name' => 'Ação Física',
                'icon' => 'fire',
                'color' => '#FF6B35', // Laranja neon
            ],
            'acao_virtual' => [
                'name' => 'Ação Virtual',
                'icon' => 'device-phone-mobile',
                'color' => '#9D4EDD', // Roxo neon
            ],
            'desafio' => [
                'name' => 'Desafio',
                'icon' => 'puzzle-piece',
                'color' => '#06FFA5', // Verde neon
            ],
        ];
    }

    public function mount()
    {
        $this->initializeBoard();
    }

    public function initializeBoard()
    {
        // Inicializa tabuleiro 8x8 vazio
        $this->board = [];
        $rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

        foreach ($rows as $row) {
            for ($col = 1; $col <= 8; $col++) {
                $position = (string) ($row . $col);
                $this->board[$position] = [
                    'type' => null,
                    'custom_text' => '',
                    'is_black' => $this->isBlackSquare($row, $col),
                    'image' => null,
                ];
            }
        }
    }

    private function isBlackSquare($row, $col)
    {
        $rowIndex = ord($row) - ord('A');
        return ($rowIndex + $col) % 2 === 0;
    }

    public function selectBlock($type)
    {
        $this->selectedBlock = $type;
    }

    public function placeBlock($position)
    {
        if (!$this->selectedBlock) {
            // Se não há bloco selecionado, mas há um bloco na posição, abrir modal de edição
            if ($this->board[$position]['type'] && $this->board[$position]['is_black']) {
                $this->editBlockText($position);
            }
            return;
        }

        // Só permite colocar blocos em casas pretas
        if (!$this->board[$position]['is_black']) {
            $this->dispatch('toast', [
                'type' => 'error',
                'message' => 'Você só pode colocar blocos nas casas pretas!'
            ]);
            return;
        }

        $this->board[$position]['type'] = $this->selectedBlock;
        $this->currentPosition = $position;
        $this->customText = $this->board[$position]['custom_text'] ?? '';
        $this->selectedBlock = null;
        $this->showTextModal = true;
    }

    public function placeBlockDrop($position, $blockType)
    {
        // Só permite colocar blocos em casas pretas
        if (!$this->board[$position]['is_black']) {
            $this->dispatch('toast', [
                'type' => 'error',
                'message' => 'Você só pode colocar blocos nas casas pretas!'
            ]);
            return;
        }

        // Verifica se o tipo de bloco é válido
        if (!array_key_exists($blockType, $this->blockTypes)) {
            $this->dispatch('toast', [
                'type' => 'error',
                'message' => 'Tipo de bloco inválido!'
            ]);
            return;
        }

        $this->board[$position]['type'] = $blockType;
        $this->currentPosition = $position;
        $this->customText = $this->board[$position]['custom_text'] ?? '';
        $this->showTextModal = true;
    }

    public function removeBlock($position)
    {
        $this->board[$position]['type'] = null;
        $this->board[$position]['custom_text'] = '';

        $this->dispatch('toast', [
            'type' => 'info',
            'message' => 'Bloco removido!'
        ]);
    }

    public function updateCustomText($position, $text)
    {
        $this->board[$position]['custom_text'] = $text;
    }

    public function editBlockText($position)
    {
        $this->currentPosition = $position;
        $this->customText = $this->board[$position]['custom_text'] ?? '';
        $this->editingPosition = $position;
        $this->showTextModal = true;
    }

    public function saveCustomText()
    {
        // Validar o texto (máximo 100 caracteres)
        if (strlen($this->customText) > 100) {
            $this->dispatch('toast', [
                'type' => 'error',
                'message' => 'O texto deve ter no máximo 100 caracteres!'
            ]);
            return;
        }

        if ($this->currentPosition) {
            $this->board[$this->currentPosition]['custom_text'] = $this->customText;

            $this->dispatch('toast', [
                'type' => 'success',
                'message' => 'Texto personalizado salvo com sucesso!'
            ]);
        }

        $this->closeTextModal();
    }

    public function closeTextModal()
    {
        $this->showTextModal = false;
        $this->currentPosition = null;
        $this->customText = '';
        $this->editingPosition = null;
    }

    public function openImageUpload($position)
    {
        $this->currentPosition = $position;
        $this->squareImage = null;
        $this->showImageModal = true;
    }

    public function uploadSquareImage()
    {
        $this->validate([
            'squareImage' => 'required|image|max:2048'
        ]);

        if ($this->squareImage && $this->currentPosition) {
            $imagePath = $this->squareImage->store('night-board-squares', 'public');
            $this->board[$this->currentPosition]['image'] = $imagePath;

            $this->dispatch('toast', [
                'type' => 'success',
                'message' => 'Imagem adicionada com sucesso!'
            ]);

            $this->showImageModal = false;
            $this->currentPosition = null;
            $this->squareImage = null;
        }
    }

    public function removeSquareImage($position)
    {
        if (isset($this->board[$position]['image'])) {
            $this->board[$position]['image'] = null;

            $this->dispatch('toast', [
                'type' => 'success',
                'message' => 'Imagem removida com sucesso!'
            ]);
        }
    }

    public function viewSquareImage($imageUrl)
    {
        $this->modalImageUrl = $imageUrl;
        $this->dispatch('open-image-modal', $imageUrl);
    }

    public function openSaveModal()
    {
        // Verifica se há pelo menos um bloco no tabuleiro
        $hasBlocks = collect($this->board)->some(fn($square) => $square['type'] !== null);

        if (!$hasBlocks) {
            $this->dispatch('toast', [
                'type' => 'error',
                'message' => 'Adicione pelo menos um bloco ao tabuleiro antes de salvar!'
            ]);
            return;
        }

        $this->showSaveModal = true;
    }

    public function savePlan()
    {
        $this->validate();

        $plan = NightBoardPlan::create([
            'user_id' => auth()->id(),
            'title' => $this->title,
            'description' => $this->description,
            'board' => $this->board,
            'is_public' => $this->is_public,
        ]);

        $this->dispatch('toast', [
            'type' => 'success',
            'message' => 'Plano salvo com sucesso!'
        ]);

        $this->showSaveModal = false;

        return redirect()->route('night-board.show', $plan->id);
    }

    public function clearBoard()
    {
        $this->initializeBoard();
        $this->dispatch('toast', [
            'type' => 'info',
            'message' => 'Tabuleiro limpo!'
        ]);
    }

    public function render()
    {
        return view('livewire.night-board.builder');
    }
}
