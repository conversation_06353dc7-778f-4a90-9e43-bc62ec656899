<section id="progress-bar" class="max-w-lg mx-auto mt-6">
    <div x-data="{
            percent: 0,
            finalPercent: {{ $profileCompletion }},
            barWidth: 0,
            animate() {
                let step = 0;
                const increment = () => {
                    if (this.percent < this.finalPercent) {
                        this.percent += 1;
                        this.barWidth = this.percent;
                        setTimeout(increment, 12);
                    } else {
                        this.percent = this.finalPercent;
                        this.barWidth = this.finalPercent;
                    }
                };
                increment();
            }
        }"
        x-init="animate()"
    >
        <flux:text>
            Preenchimento de perfil: <span x-text="percent">0</span>%
        </flux:text>
        <div class="mb-2 mt-2 flex h-2.5 overflow-hidden rounded text-xs border border-gray-400 bg-gray-100">
            <div 
                :style="'width: ' + barWidth + '%'" 
                class="{{ $progressColor }} transition-all duration-700 ease-in-out"
            ></div>
        </div>
    </div>
</section>