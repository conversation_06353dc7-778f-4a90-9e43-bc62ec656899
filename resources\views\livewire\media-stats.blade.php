<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
        <flux:icon.chart-bar class="w-5 h-5 mr-2 text-blue-500" />
        Estatísticas da Galeria
    </h3>

    {{-- Cards de Estatísticas Principais --}}
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-600 dark:text-blue-400 text-sm font-medium">Total de Mídias</p>
                    <p class="text-2xl font-bold text-blue-700 dark:text-blue-300">{{ $stats['total_medias'] }}</p>
                </div>
                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                    <flux:icon.photo class="w-5 h-5 text-white" />
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-600 dark:text-green-400 text-sm font-medium">Fotos</p>
                    <p class="text-2xl font-bold text-green-700 dark:text-green-300">{{ $stats['total_photos'] }}</p>
                </div>
                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                    <flux:icon.camera class="w-5 h-5 text-white" />
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-600 dark:text-purple-400 text-sm font-medium">Vídeos</p>
                    <p class="text-2xl font-bold text-purple-700 dark:text-purple-300">{{ $stats['total_videos'] }}</p>
                </div>
                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                    <flux:icon.video-camera class="w-5 h-5 text-white" />
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-600 dark:text-orange-400 text-sm font-medium">Álbuns</p>
                    <p class="text-2xl font-bold text-orange-700 dark:text-orange-300">{{ $stats['total_albums'] }}</p>
                </div>
                <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                    <flux:icon.folder class="w-5 h-5 text-white" />
                </div>
            </div>
        </div>
    </div>

    {{-- Gráfico de Proporção --}}
    @if($stats['total_medias'] > 0)
        <div class="mb-6">
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Distribuição de Conteúdo</h4>
            <div class="flex items-center space-x-4">
                {{-- Barra de Progresso --}}
                <div class="flex-1">
                    <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                        <span>Fotos vs Vídeos</span>
                        <span>{{ $stats['photos_percentage'] }}% / {{ $stats['videos_percentage'] }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                        <div class="h-full flex">
                            <div class="bg-green-500 transition-all duration-500" style="width: {{ $stats['photos_percentage'] }}%"></div>
                            <div class="bg-purple-500 transition-all duration-500" style="width: {{ $stats['videos_percentage'] }}%"></div>
                        </div>
                    </div>
                </div>
                
                {{-- Legenda --}}
                <div class="flex space-x-4 text-sm">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-gray-600 dark:text-gray-400">Fotos</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                        <span class="text-gray-600 dark:text-gray-400">Vídeos</span>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Informações Adicionais --}}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {{-- Estatísticas Detalhadas --}}
        <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Detalhes</h4>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-zinc-700 rounded-lg">
                    <span class="text-gray-600 dark:text-gray-400 flex items-center">
                        <flux:icon.server class="w-4 h-4 mr-2" />
                        Espaço Utilizado
                    </span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ $stats['total_size'] }}</span>
                </div>

                @if($stats['latest_media'])
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-zinc-700 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400 flex items-center">
                            <flux:icon.clock class="w-4 h-4 mr-2" />
                            Último Upload
                        </span>
                        <span class="font-medium text-gray-900 dark:text-white">
                            {{ $stats['latest_media']->created_at->diffForHumans() }}
                        </span>
                    </div>
                @endif

                @if($stats['top_album'])
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-zinc-700 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400 flex items-center">
                            <flux:icon.star class="w-4 h-4 mr-2" />
                            Álbum Principal
                        </span>
                        <span class="font-medium text-gray-900 dark:text-white">
                            {{ $stats['top_album']->name }} ({{ $stats['top_album']->medias_count }})
                        </span>
                    </div>
                @endif
            </div>
        </div>

        {{-- Atividade Recente --}}
        @if($recentActivity->count() > 0)
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Atividade Recente</h4>
                <div class="space-y-2">
                    @foreach($recentActivity as $activity)
                        <div class="flex items-center p-2 bg-gray-50 dark:bg-zinc-700 rounded-lg">
                            <div class="w-10 h-10 rounded-lg overflow-hidden mr-3 flex-shrink-0">
                                @if($activity['type'] === 'photo')
                                    <img src="{{ $activity['thumbnail'] }}" 
                                         alt="{{ $activity['title'] }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-gray-800 flex items-center justify-center">
                                        <flux:icon.play class="w-4 h-4 text-white" />
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ $activity['title'] }}
                                </p>
                                <p class="text-xs text-gray-600 dark:text-gray-400">
                                    {{ $activity['album'] }} • {{ $activity['date'] }}
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                @if($activity['type'] === 'photo')
                                    <flux:icon.photo class="w-4 h-4 text-green-500" />
                                @else
                                    <flux:icon.video-camera class="w-4 h-4 text-purple-500" />
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>

    {{-- Gráfico de Uploads por Mês (se houver dados) --}}
    @if($chartData['monthly_uploads']->count() > 0)
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-zinc-700">
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Uploads por Mês</h4>
            <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4">
                <div class="flex items-end justify-between h-32 space-x-2">
                    @foreach($chartData['monthly_uploads'] as $index => $month)
                        <div class="flex-1 flex flex-col items-center">
                            <div class="w-full flex flex-col justify-end h-24 space-y-1">
                                @if($month->photos > 0)
                                    <div class="bg-green-500 rounded-t" 
                                         style="height: {{ ($month->photos / max($chartData['monthly_uploads']->max('count'), 1)) * 100 }}%"
                                         title="{{ $month->photos }} fotos"></div>
                                @endif
                                @if($month->videos > 0)
                                    <div class="bg-purple-500 {{ $month->photos > 0 ? '' : 'rounded-t' }}" 
                                         style="height: {{ ($month->videos / max($chartData['monthly_uploads']->max('count'), 1)) * 100 }}%"
                                         title="{{ $month->videos }} vídeos"></div>
                                @endif
                            </div>
                            <span class="text-xs text-gray-600 dark:text-gray-400 mt-2">
                                {{ $chartData['labels'][$index] }}
                            </span>
                        </div>
                    @endforeach
                </div>
                <div class="flex justify-center space-x-4 mt-3 text-sm">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                        <span class="text-gray-600 dark:text-gray-400">Fotos</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded mr-2"></div>
                        <span class="text-gray-600 dark:text-gray-400">Vídeos</span>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
