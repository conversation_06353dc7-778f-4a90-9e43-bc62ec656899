<x-layouts.app :title="'Categorias de Vagas'">
    <div class="p-6">
        <!-- Header -->
        <div class="mb-6 flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Categorias de Vagas</h1>
                <p class="text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON><PERSON> as categorias das vagas de emprego</p>
            </div>
            <flux:button wire:click="create" variant="primary">
                <flux:icon icon="plus" class="w-4 h-4 mr-2" />
                Nova Categoria
            </flux:button>
        </div>

        <!-- Flash Messages -->
        @if (session()->has('message'))
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
                {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
                {{ session('error') }}
            </div>
        @endif

        <!-- Search and Filters -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
            <div class="p-6">
                <div class="flex items-center gap-4">
                    <div class="flex-1">
                        <flux:input
                            wire:model.live.debounce.300ms="search"
                            placeholder="Buscar categorias..."
                            icon="magnifying-glass"
                        />
                    </div>
                    <flux:button wire:click="$set('search', '')" variant="ghost" size="sm">
                        <flux:icon icon="x-mark" class="w-4 h-4 mr-1" />
                        Limpar
                    </flux:button>
                </div>
            </div>
        </div>

        <!-- Categories Table -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-zinc-700">
                        <tr>
                            <th wire:click="sortBy('name')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                                <div class="flex items-center space-x-1">
                                    <span>Nome</span>
                                    @if($sortBy === 'name')
                                        <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                    @endif
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Descrição
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Vagas
                            </th>
                            <th wire:click="sortBy('created_at')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                                <div class="flex items-center space-x-1">
                                    <span>Criada em</span>
                                    @if($sortBy === 'created_at')
                                        <flux:icon icon="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="w-4 h-4" />
                                    @endif
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Ações
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($categories as $category)
                            <tr class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $category->name }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $category->slug }}
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 dark:text-white">
                                        {{ $category->description ? Str::limit($category->description, 100) : 'Sem descrição' }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                        {{ $category->job_vacancies_count }} {{ $category->job_vacancies_count === 1 ? 'vaga' : 'vagas' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ $category->created_at->format('d/m/Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <flux:button wire:click="edit({{ $category->id }})" size="sm" variant="ghost">
                                            <flux:icon icon="pencil" class="w-4 h-4" />
                                        </flux:button>

                                        @if($category->job_vacancies_count === 0)
                                            <flux:button
                                                wire:click="delete({{ $category->id }})"
                                                size="sm"
                                                variant="ghost"
                                                class="text-red-600 hover:text-red-800"
                                                onclick="return confirm('Tem certeza que deseja excluir esta categoria?')">
                                                <flux:icon icon="trash" class="w-4 h-4" />
                                            </flux:button>
                                        @else
                                            <span class="text-xs text-gray-400" title="Não é possível excluir uma categoria com vagas">
                                                <flux:icon icon="lock-closed" class="w-4 h-4" />
                                            </span>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <flux:icon icon="tag" class="w-12 h-12 text-gray-400 dark:text-gray-600 mb-4" />
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhuma categoria encontrada</h3>
                                        <p class="text-gray-500 dark:text-gray-400">Não há categorias que correspondam aos filtros aplicados.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($categories->hasPages())
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    {{ $categories->links() }}
                </div>
            @endif
        </div>

        <!-- Create/Edit Modal -->
        @if($showModal)
            <flux:modal wire:model="showModal" class="max-w-md">
                <flux:modal.header>
                    <flux:heading size="lg">{{ $editingCategory ? 'Editar Categoria' : 'Nova Categoria' }}</flux:heading>
                </flux:modal.header>

                <form wire:submit.prevent="save">
                    <div class="space-y-4">
                        <div>
                            <flux:field>
                                <flux:label>Nome da Categoria</flux:label>
                                <flux:input wire:model="name" placeholder="Ex: Tecnologia" />
                                <flux:error name="name" />
                            </flux:field>
                        </div>

                        <div>
                            <flux:field>
                                <flux:label>Descrição (opcional)</flux:label>
                                <flux:textarea wire:model="description" placeholder="Descrição da categoria..." rows="3" />
                                <flux:error name="description" />
                            </flux:field>
                        </div>
                    </div>

                    <flux:modal.footer>
                        <div class="flex justify-between w-full">
                            <flux:button wire:click="closeModal" variant="ghost">
                                Cancelar
                            </flux:button>
                            <flux:button type="submit" variant="primary">
                                <flux:icon icon="check" class="w-4 h-4 mr-1" />
                                {{ $editingCategory ? 'Atualizar' : 'Criar' }}
                            </flux:button>
                        </div>
                    </flux:modal.footer>
                </form>
            </flux:modal>
        @endif
    </div>
</x-layouts.app>
