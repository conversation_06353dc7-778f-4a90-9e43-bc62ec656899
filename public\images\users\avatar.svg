<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradiente de fundo -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradiente para o ícone -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9ca3af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6b7280;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Fundo circular -->
  <circle cx="100" cy="100" r="100" fill="url(#bgGradient)" />
  
  <!-- <PERSON>cone de usuário -->
  <g transform="translate(100, 100)">
    <!-- Cabeça -->
    <circle cx="0" cy="-20" r="25" fill="url(#iconGradient)" />
    
    <!-- Corpo -->
    <path d="M -35 20 Q -35 -5 -25 -15 Q 0 -25 25 -15 Q 35 -5 35 20 L 35 40 Q 35 50 25 50 L -25 50 Q -35 50 -35 40 Z" 
          fill="url(#iconGradient)" />
  </g>
  
  <!-- Borda sutil -->
  <circle cx="100" cy="100" r="99" fill="none" stroke="#d1d5db" stroke-width="2" />
</svg>
