<?php

namespace App\Livewire;

use App\Models\LiveStream;
use Livewire\Component;
use Livewire\WithPagination;

class LiveStreamsList extends Component
{
    use WithPagination;

    public $filter = 'live'; // live, waiting, ended, all
    public $search = '';

    protected $queryString = [
        'filter' => ['except' => 'live'],
        'search' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function setFilter($filter)
    {
        $this->filter = $filter;
        $this->resetPage();
    }

    public function render()
    {
        $query = LiveStream::with(['user', 'user.userPhotos'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhereHas('user', function ($userQuery) {
                          $userQuery->where('name', 'like', '%' . $this->search . '%');
                      });
                });
            });

        switch ($this->filter) {
            case 'live':
                $query->live();
                break;
            case 'waiting':
                $query->waiting();
                break;
            case 'ended':
                $query->ended();
                break;
            case 'all':
                // Não aplica filtro
                break;
        }

        $liveStreams = $query->latest()->paginate(12);

        return view('livewire.live-streams-list', [
            'liveStreams' => $liveStreams,
        ]);
    }
}
