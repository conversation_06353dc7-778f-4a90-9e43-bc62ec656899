<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Group;
use App\Models\GroupInvitation;
use App\Notifications\GroupInvitationReceived;
use App\Notifications\AdminGroupInvitationCopy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class GroupInvitationNotificationTest extends TestCase
{
    public function test_group_invitation_notification_contains_correct_data()
    {
        // Create users and group manually
        $inviter = new User([
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'username' => 'johndoe',
            'role' => 'vip'
        ]);
        $inviter->id = 1;

        $invitedUser = new User([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'username' => 'janedoe',
            'role' => 'vip'
        ]);
        $invitedUser->id = 2;

        $group = new Group([
            'name' => 'Test Group',
            'slug' => 'test-group',
            'description' => 'A test group',
            'privacy' => 'private',
            'creator_id' => 1,
            'members_count' => 1
        ]);
        $group->id = 1;

        // Create invitation manually
        $invitation = new GroupInvitation([
            'group_id' => 1,
            'user_id' => 2,
            'invited_by' => 1,
            'status' => 'pending',
        ]);
        $invitation->id = 1;
        $invitation->group = $group;
        $invitation->user = $invitedUser;
        $invitation->inviter = $inviter;

        // Test user notification
        $notification = new GroupInvitationReceived($invitation);
        $mailData = $notification->toMail($invitedUser);

        $this->assertEquals('Convite para Grupo - ' . $group->name, $mailData->subject);

        // Test array data
        $arrayData = $notification->toArray($invitedUser);
        $this->assertEquals($invitation->id, $arrayData['invitation_id']);
        $this->assertEquals($group->id, $arrayData['group_id']);
        $this->assertEquals($group->name, $arrayData['group_name']);
        $this->assertEquals($inviter->id, $arrayData['inviter_id']);
        $this->assertEquals($inviter->name, $arrayData['inviter_name']);
    }

    public function test_admin_notification_contains_correct_data()
    {
        // Create users and group manually
        $inviter = new User([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'username' => 'johndoe',
            'role' => 'vip'
        ]);
        $inviter->id = 1;

        $invitedUser = new User([
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'username' => 'janedoe',
            'role' => 'vip'
        ]);
        $invitedUser->id = 2;

        $admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'username' => 'admin',
            'role' => 'admin'
        ]);
        $admin->id = 3;

        $group = new Group([
            'name' => 'Test Group',
            'slug' => 'test-group',
            'description' => 'A test group',
            'privacy' => 'private',
            'creator_id' => 1,
            'members_count' => 1
        ]);
        $group->id = 1;

        // Create invitation manually
        $invitation = new GroupInvitation([
            'group_id' => 1,
            'user_id' => 2,
            'invited_by' => 1,
            'status' => 'pending',
        ]);
        $invitation->id = 1;
        $invitation->group = $group;
        $invitation->user = $invitedUser;
        $invitation->inviter = $inviter;

        // Test admin notification
        $notification = new AdminGroupInvitationCopy($invitation);
        $mailData = $notification->toMail($admin);

        $this->assertEquals('[ADMIN COPY] Convite para Grupo - ' . $group->name, $mailData->subject);
    }
}
