<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\JobApplication;
use App\Models\JobVacancy;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

class JobApplicationManager extends Component
{
    use WithPagination;

    public $search = '';
    public $statusFilter = '';
    public $jobFilter = '';
    public $priorityFilter = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';

    // Modal properties
    public $showModal = false;
    public $selectedApplication = null;
    public $adminNotes = '';
    public $newStatus = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'jobFilter' => ['except' => ''],
        'priorityFilter' => ['except' => ''],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount()
    {
        //
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingJobFilter()
    {
        $this->resetPage();
    }

    public function updatingPriorityFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function viewApplication($applicationId)
    {
        $this->selectedApplication = JobApplication::with(['job', 'user', 'reviewer'])->find($applicationId);
        $this->adminNotes = $this->selectedApplication->admin_notes ?? '';
        $this->newStatus = $this->selectedApplication->status;
        $this->showModal = true;
    }

    public function updateApplication()
    {
        $this->validate([
            'newStatus' => 'required|in:Pendente,Em Análise,Aprovada,Rejeitada,Contratada',
            'adminNotes' => 'nullable|string|max:1000',
        ]);

        if ($this->selectedApplication) {
            $this->selectedApplication->update([
                'status' => $this->newStatus,
                'admin_notes' => $this->adminNotes,
                'reviewed_at' => now(),
                'reviewed_by' => auth()->id(),
            ]);

            session()->flash('message', 'Candidatura atualizada com sucesso!');
            $this->closeModal();
        }
    }

    public function downloadResume($applicationId)
    {
        $application = JobApplication::findOrFail($applicationId);

        if ($application->resume_path && Storage::disk('private')->exists($application->resume_path)) {
            return Storage::disk('private')->download($application->resume_path, $application->resume_filename);
        }

        session()->flash('error', 'Currículo não encontrado.');
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedApplication = null;
        $this->adminNotes = '';
        $this->newStatus = '';
    }

    public function getApplicationsProperty()
    {
        $query = JobApplication::with(['job', 'user', 'reviewer'])
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%');
                })->orWhereHas('job', function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->jobFilter, function ($query) {
                $query->where('job_id', $this->jobFilter);
            })
            ->when($this->priorityFilter === 'vip', function ($query) {
                $query->where('is_vip_priority', true);
            })
            ->when($this->priorityFilter === 'regular', function ($query) {
                $query->where('is_vip_priority', false);
            })
            ->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(15);
    }

    public function getJobsProperty()
    {
        return JobVacancy::select('id', 'title')->orderBy('title')->get();
    }

    public function getStatusStatsProperty()
    {
        return [
            'total' => JobApplication::count(),
            'pending' => JobApplication::where('status', 'Pendente')->count(),
            'in_review' => JobApplication::where('status', 'Em Análise')->count(),
            'approved' => JobApplication::where('status', 'Aprovada')->count(),
            'rejected' => JobApplication::where('status', 'Rejeitada')->count(),
            'hired' => JobApplication::where('status', 'Contratada')->count(),
            'vip' => JobApplication::where('is_vip_priority', true)->count(),
        ];
    }

    public function render()
    {
        return view('livewire.admin.job-application-manager', [
            'applications' => $this->applications,
            'jobs' => $this->jobs,
            'statusStats' => $this->statusStats,
        ])->layout('components.layouts.app');
    }
}
