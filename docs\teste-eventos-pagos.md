# Teste de Eventos Pagos - Guia Passo a Passo

## ✅ Correções Implementadas

1. **Componente Livewire corrigido** para distinguir eventos gratuitos de pagos
2. **Logs detalhados** adicionados para debugging
3. **Botões atualizados** para mostrar preço correto
4. **Redirecionamento para Stripe** implementado

## 🧪 Como Testar

### 1. Verificar Evento Pago Existente

Baseado no teste, você tem um evento pago:
- **Nome**: "kezia rodrigues"
- **Preço**: R$ 125,00
- **URL**: `/eventos/kezia-rodrigues`

### 2. Teste no Browser

1. **Acesse o evento**:
   ```
   http://localhost/eventos/kezia-rodrigues
   ```

2. **Verifique o botão**:
   - Deve mostrar: "Inscrever-se por R$ 125,00"
   - NÃO deve mostrar: "Inscrever-se Gratuitamente"

3. **Clique no botão de inscrição**

4. **Confirme no modal**:
   - Modal deve mostrar o preço: R$ 125,00
   - Deve ter aviso sobre redirecionamento para Stripe

5. **Clique em "Confirmar"**:
   - Deve redirecionar para o Stripe
   - URL deve começar com `https://checkout.stripe.com/`

### 3. Verificar Logs

Abra o arquivo `storage/logs/laravel.log` e procure por:

```
[timestamp] local.INFO: EventRegistration: register() called {"event_id":2,"event_name":"kezia rodrigues","event_price":125,"is_free":false,"user_id":1}
[timestamp] local.INFO: EventRegistration: Creating paid registration {"event_id":2,"event_price":125,"user_id":1}
[timestamp] local.INFO: EventRegistration: Pending attendee created {"attendee_id":X}
[timestamp] local.INFO: EventRegistration: Creating Stripe session {"amount":12500,"customer_email":"<EMAIL>"}
[timestamp] local.INFO: EventRegistration: Stripe session created {"session_id":"cs_...","session_url":"https://checkout.stripe.com/..."}
[timestamp] local.INFO: EventRegistration: Redirecting to Stripe {"url":"https://checkout.stripe.com/..."}
```

### 4. Verificar no Banco de Dados

Execute esta query no phpMyAdmin:

```sql
SELECT 
    ea.id,
    ea.event_id,
    ea.user_id,
    ea.status,
    ea.payment_status,
    ea.payment_id,
    ea.amount_paid,
    e.name as evento_nome,
    e.price as evento_preco,
    u.name as usuario_nome
FROM event_attendees ea
JOIN events e ON ea.event_id = e.id
JOIN users u ON ea.user_id = u.id
WHERE ea.created_at > NOW() - INTERVAL 1 HOUR
ORDER BY ea.created_at DESC;
```

Deve mostrar:
- `status`: 'registered' (não 'confirmed')
- `payment_status`: 'pending' (não 'completed')
- `payment_id`: ID da sessão Stripe (cs_...)
- `amount_paid`: 0 (será atualizado após pagamento)

## 🔍 Troubleshooting

### Problema: Botão ainda mostra "Gratuito"

**Verificar**:
1. Se o evento tem `price > 0`
2. Se o método `is_free` está funcionando

**Query de verificação**:
```sql
SELECT id, name, price, (price IS NULL OR price = 0) as is_free 
FROM events 
WHERE slug = 'kezia-rodrigues';
```

### Problema: Não redireciona para Stripe

**Verificar logs** para:
- Erros de criação de sessão Stripe
- Problemas de configuração
- Erros de redirecionamento

**Possíveis causas**:
1. Configuração Stripe incorreta
2. Erro na criação da sessão
3. Problema de redirecionamento do Livewire

### Problema: Erro de configuração Stripe

**Verificar variáveis**:
```bash
php artisan config:show cashier
```

Deve mostrar:
- `key`: pk_live_... ou pk_test_...
- `secret`: sk_live_... ou sk_test_...

## 📊 Monitoramento

### Console do Browser

1. Abra DevTools (F12)
2. Vá para aba Console
3. Procure por mensagens de debug
4. Verifique erros JavaScript

### Rede (Network)

1. Aba Network no DevTools
2. Procure por requisições Livewire
3. Verifique se há erros 500/400
4. Veja se há redirecionamentos

## 🎯 Resultados Esperados

### ✅ Funcionando Corretamente

1. **Botão**: "Inscrever-se por R$ 125,00"
2. **Modal**: Mostra preço e aviso sobre Stripe
3. **Logs**: Sequência completa de logs
4. **Banco**: Registro com status 'registered' e 'pending'
5. **Redirecionamento**: Para checkout.stripe.com

### ❌ Problemas Comuns

1. **Botão gratuito**: Evento não tem preço configurado
2. **Sem redirecionamento**: Erro na configuração Stripe
3. **Erro 500**: Problema no código/configuração
4. **Modal não abre**: Problema JavaScript/Livewire

## 🔧 Comandos Úteis

### Limpar Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

### Verificar Configuração
```bash
php artisan config:show app.debug
php artisan config:show cashier
```

### Testar Stripe
```bash
php test-paid-event.php
```

## 📝 Relatório de Teste

Após testar, anote:

1. **URL testada**: _______________
2. **Botão mostrou preço correto**: Sim/Não
3. **Modal abriu**: Sim/Não
4. **Redirecionou para Stripe**: Sim/Não
5. **Logs apareceram**: Sim/Não
6. **Registro criado no banco**: Sim/Não
7. **Erros encontrados**: _______________

## 🆘 Se Não Funcionar

1. **Copie os logs** de `storage/logs/laravel.log`
2. **Tire screenshot** do botão e modal
3. **Execute** `php test-paid-event.php`
4. **Verifique** configuração Stripe
5. **Teste** com evento gratuito primeiro

## 📞 Próximos Passos

Se tudo funcionar:
1. Teste o fluxo completo de pagamento
2. Verifique webhook do Stripe
3. Teste cancelamento de pagamento
4. Verifique emails de confirmação

Se não funcionar:
1. Forneça logs específicos
2. Descreva comportamento observado
3. Informe se eventos gratuitos funcionam
4. Compartilhe resultado do teste PHP
