<?php

namespace App\Livewire\Support;

use App\Models\SupportTicket;
use Livewire\Component;
use Livewire\WithFileUploads;

class EditTicket extends Component
{
    use WithFileUploads;

    public SupportTicket $ticket;
    public $showModal = false;
    public $title;
    public $description;
    public $category;
    public $priority;
    public $newAttachments = [];

    protected $listeners = [
        'openEditTicketModal' => 'openModal',
        'closeEditTicketModal' => 'closeModal',
    ];

    protected $rules = [
        'title' => 'required|string|min:5|max:255',
        'description' => 'required|string|min:10',
        'category' => 'required|string',
        'priority' => 'required|in:baixa,media,alta,urgente',
        'newAttachments.*' => 'nullable|file|max:10240|mimes:jpg,jpeg,png,pdf,doc,docx,txt',
    ];

    protected $messages = [
        'title.required' => 'O título é obrigatório.',
        'title.min' => 'O título deve ter pelo menos 5 caracteres.',
        'description.required' => 'A descrição é obrigatória.',
        'description.min' => 'A descrição deve ter pelo menos 10 caracteres.',
        'category.required' => 'Selecione uma categoria.',
        'newAttachments.*.max' => 'Cada arquivo deve ter no máximo 10MB.',
        'newAttachments.*.mimes' => 'Tipos de arquivo permitidos: JPG, PNG, PDF, DOC, DOCX, TXT.',
    ];

    public function mount(SupportTicket $ticket = null)
    {
        if ($ticket) {
            $this->ticket = $ticket;
            $this->title = $ticket->title;
            $this->description = $ticket->description;
            $this->category = $ticket->category;
            $this->priority = $ticket->priority;
        }
    }

    public function openModal($ticketId)
    {
        $this->ticket = SupportTicket::findOrFail($ticketId);
        
        // Verificar se o usuário pode editar este ticket
        if ($this->ticket->user_id !== auth()->id() && auth()->user()->role !== 'admin') {
            $this->dispatch('notify', [
                'message' => 'Você não tem permissão para editar este ticket.',
                'type' => 'error'
            ]);
            return;
        }

        // Só permite editar tickets abertos
        if (!$this->ticket->isOpen()) {
            $this->dispatch('notify', [
                'message' => 'Apenas tickets abertos podem ser editados.',
                'type' => 'warning'
            ]);
            return;
        }

        $this->title = $this->ticket->title;
        $this->description = $this->ticket->description;
        $this->category = $this->ticket->category;
        $this->priority = $this->ticket->priority;
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->reset(['newAttachments']);
        $this->resetValidation();
    }

    public function removeAttachment($index)
    {
        unset($this->newAttachments[$index]);
        $this->newAttachments = array_values($this->newAttachments);
    }

    public function removeExistingAttachment($index)
    {
        $attachments = $this->ticket->attachments ?? [];
        unset($attachments[$index]);
        $this->ticket->update(['attachments' => array_values($attachments)]);
        
        $this->dispatch('notify', [
            'message' => 'Anexo removido com sucesso.',
            'type' => 'success'
        ]);
    }

    public function updateTicket()
    {
        $this->validate();

        try {
            // Upload new attachments
            $uploadedFiles = [];
            if (!empty($this->newAttachments)) {
                foreach ($this->newAttachments as $file) {
                    $path = $file->store('support-tickets', 'public');
                    $uploadedFiles[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getMimeType(),
                    ];
                }
            }

            // Merge with existing attachments
            $existingAttachments = $this->ticket->attachments ?? [];
            $allAttachments = array_merge($existingAttachments, $uploadedFiles);

            // Update ticket
            $this->ticket->update([
                'title' => $this->title,
                'description' => $this->description,
                'category' => $this->category,
                'priority' => $this->priority,
                'attachments' => $allAttachments,
            ]);

            $this->dispatch('notify', [
                'message' => "Ticket #{$this->ticket->ticket_number} atualizado com sucesso!",
                'type' => 'success'
            ]);

            $this->closeModal();

            // Refresh parent component
            $this->dispatch('ticketUpdated');

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao atualizar ticket. Tente novamente.',
                'type' => 'error'
            ]);
        }
    }

    public function getCategoriesProperty()
    {
        return [
            'conta' => 'Problemas com Conta',
            'pagamento' => 'Pagamentos e Cobrança',
            'tecnico' => 'Problemas Técnicos',
            'funcionalidade' => 'Dúvidas sobre Funcionalidades',
            'loja' => 'Loja Virtual',
            'grupos' => 'Grupos e Comunidades',
            'mensagens' => 'Sistema de Mensagens',
            'perfil' => 'Perfil e Configurações',
            'outros' => 'Outros Assuntos',
        ];
    }

    public function getPrioritiesProperty()
    {
        return [
            'baixa' => 'Baixa - Dúvida geral',
            'media' => 'Média - Problema que não impede o uso',
            'alta' => 'Alta - Problema que impede funcionalidades',
            'urgente' => 'Urgente - Sistema inacessível',
        ];
    }

    public function render()
    {
        return view('livewire.support.edit-ticket');
    }
}
