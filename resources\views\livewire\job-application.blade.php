<div class="bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    @if($showSuccess)
        <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div class="flex items-center gap-3">
                <flux:icon icon="check-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
                <div>
                    <h3 class="font-semibold text-green-800 dark:text-green-200">Candidatura Enviada!</h3>
                    <p class="text-sm text-green-700 dark:text-green-300 mt-1">
                        Sua candidatura foi enviada com sucesso. Você receberá um email de confirmação em breve.
                        @if(auth()->user()->role === 'vip')
                            <br><strong>Sua candidatura VIP terá prioridade na análise!</strong>
                        @endif
                    </p>
                </div>
            </div>
        </div>
    @else
        <form wire:submit.prevent="apply" class="space-y-6">
            <!-- <PERSON><PERSON> -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <flux:icon icon="user" class="w-5 h-5 text-primary" />
                    Dados Pessoais
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <flux:field>
                            <flux:label>Nome Completo <span class="text-red-500">*</span></flux:label>
                            <flux:input wire:model.live="name" placeholder="Digite seu nome completo" />
                            <flux:error name="name" />
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Email <span class="text-red-500">*</span></flux:label>
                            <flux:input type="email" wire:model.live="email" placeholder="<EMAIL>" />
                            <flux:error name="email" />
                        </flux:field>
                    </div>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Telefone <span class="text-red-500">*</span></flux:label>
                        <flux:input wire:model.live="phone" placeholder="(11) 99999-9999" />
                        <flux:error name="phone" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Experiência Profissional</flux:label>
                        <flux:textarea wire:model.live="experience" rows="3"
                            placeholder="Descreva brevemente sua experiência relevante para esta vaga..." />
                        <flux:error name="experience" />
                        <flux:description>Máximo 1000 caracteres</flux:description>
                    </flux:field>
                </div>
            </div>

            <!-- Upload de Currículo -->
            @if($job->requires_resume)
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <flux:icon icon="document-text" class="w-5 h-5 text-primary" />
                    Currículo <span class="text-red-500">*</span>
                </h3>

                <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                    @if($resume)
                        <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center gap-3">
                                <flux:icon icon="document" class="w-8 h-8 text-primary" />
                                <div class="text-left">
                                    <p class="font-medium text-gray-900 dark:text-white">{{ $resume->getClientOriginalName() }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $this->getFileSize() }}</p>
                                </div>
                            </div>
                            <flux:button wire:click="removeFile" variant="ghost" size="sm" class="text-red-600 hover:text-red-700">
                                <flux:icon icon="x-mark" class="w-4 h-4" />
                            </flux:button>
                        </div>

                        @if($uploadProgress > 0 && $uploadProgress < 100)
                            <div class="mt-3">
                                <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full transition-all duration-300"
                                         style="width: {{ $uploadProgress }}%"></div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Enviando... {{ $uploadProgress }}%</p>
                            </div>
                        @endif
                    @else
                        <div class="space-y-3">
                            <flux:icon icon="cloud-arrow-up" class="w-12 h-12 text-gray-400 mx-auto" />
                            <div>
                                <label for="resume-upload" class="cursor-pointer">
                                    <span class="text-primary font-medium hover:text-primary/80">Clique para enviar</span>
                                    <span class="text-gray-500 dark:text-gray-400"> ou arraste o arquivo aqui</span>
                                </label>
                                <input id="resume-upload" type="file" wire:model="resume"
                                       accept=".pdf,.doc,.docx" class="hidden" />
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                PDF, DOC ou DOCX até 2MB
                            </p>
                        </div>

                        <div wire:loading wire:target="resume" class="mt-3">
                            <div class="flex items-center justify-center gap-2 text-primary">
                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                                <span class="text-sm">Processando arquivo...</span>
                            </div>
                        </div>
                    @endif
                </div>

                @error('resume')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
            @endif

            <!-- Carta de Apresentação -->
            @if($job->requires_cover_letter)
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <flux:icon icon="document-text" class="w-5 h-5 text-primary" />
                    Carta de Apresentação <span class="text-red-500">*</span>
                </h3>

                <flux:field>
                    <flux:textarea wire:model.live="cover_letter" rows="6"
                        placeholder="Conte-nos por que você é o candidato ideal para esta vaga. Destaque suas qualificações, experiências relevantes e motivação..." />
                    <flux:error name="cover_letter" />
                    <flux:description>
                        Mínimo 100 caracteres, máximo 2000 caracteres
                        @if(strlen($cover_letter) > 0)
                            ({{ strlen($cover_letter) }}/2000)
                        @endif
                    </flux:description>
                </flux:field>
            </div>
            @else
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <flux:icon icon="document-text" class="w-5 h-5 text-primary" />
                    Carta de Apresentação <span class="text-gray-500">(Opcional)</span>
                </h3>

                <flux:field>
                    <flux:textarea wire:model.live="cover_letter" rows="4"
                        placeholder="Adicione uma carta de apresentação para destacar sua candidatura..." />
                    <flux:error name="cover_letter" />
                    <flux:description>
                        Máximo 2000 caracteres
                        @if(strlen($cover_letter) > 0)
                            ({{ strlen($cover_letter) }}/2000)
                        @endif
                    </flux:description>
                </flux:field>
            </div>
            @endif

            <!-- Informações VIP -->
            @if(auth()->user()->role === 'vip')
            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex items-center gap-3">
                    <flux:icon icon="star" class="w-6 h-6 text-yellow-500" />
                    <div>
                        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200">Candidatura VIP</h4>
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">
                            Sua candidatura terá prioridade na análise dos recrutadores!
                        </p>
                    </div>
                </div>
            </div>
            @endif

            <!-- Mensagens de Erro -->
            @if($errorMessage)
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex items-center gap-3">
                    <flux:icon icon="exclamation-triangle" class="w-5 h-5 text-red-600 dark:text-red-400" />
                    <p class="text-red-700 dark:text-red-300">{{ $errorMessage }}</p>
                </div>
            </div>
            @endif

            <!-- Botões de Ação -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    <flux:icon icon="shield-check" class="w-4 h-4 inline mr-1" />
                    Seus dados estão seguros e protegidos
                </div>

                <div class="flex gap-3">
                    <flux:button type="button" variant="ghost"
                        onclick="window.history.back()"
                        :disabled="$isSubmitting">
                        Voltar
                    </flux:button>

                    <flux:button type="submit" variant="primary"
                        :disabled="$isSubmitting"
                        class="min-w-[140px]">
                        <span wire:loading.remove wire:target="apply">
                            <flux:icon icon="paper-airplane" class="w-4 h-4 mr-2" />
                            Enviar Candidatura
                        </span>
                        <span wire:loading wire:target="apply" class="flex items-center">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Enviando...
                        </span>
                    </flux:button>
                </div>
            </div>
        </form>
    @endif
</div>

<script>
    // Máscara para telefone
    document.addEventListener('livewire:init', () => {
        Livewire.on('show-toast', (event) => {
            // Implementar toast notification se necessário
            console.log('Toast:', event);
        });
    });
</script>
