<x-layouts.app :title="'Minhas Candidaturas'">
    <div class="p-6">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                <flux:icon icon="clipboard-document-list" class="w-6 h-6" />
                Minhas Candidaturas
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Acompanhe o status das suas candidaturas às vagas de emprego</p>
        </div>

        <!-- Quick Actions -->
        <div class="mb-6 flex flex-wrap gap-3">
            <flux:button :href="route('job_vacancies.index')" wire:navigate variant="primary">
                <flux:icon icon="briefcase" class="w-4 h-4 mr-2" />
                Ver Vagas Disponíveis
            </flux:button>
        </div>

        @if($applications->count() > 0)
            <!-- Applications List -->
            <div class="space-y-4">
                @foreach($applications as $application)
                    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
                        <div class="p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <!-- Job Title and Company -->
                                    <div class="mb-3">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                            <a href="{{ route('job_vacancies.show', $application->job->slug) }}" 
                                               class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                                               wire:navigate>
                                                {{ $application->job->title }}
                                            </a>
                                        </h3>
                                        <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                                            <flux:icon icon="building-office" class="w-4 h-4" />
                                            <span>{{ $application->job->category->name ?? 'Categoria não definida' }}</span>
                                            @if($application->job->location)
                                                <span>•</span>
                                                <flux:icon icon="map-pin" class="w-4 h-4" />
                                                <span>{{ $application->job->location }}</span>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Application Info -->
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                        <div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">Data da Candidatura</div>
                                            <div class="font-medium text-gray-900 dark:text-white">
                                                {{ $application->created_at->format('d/m/Y H:i') }}
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">Status</div>
                                            <div class="mt-1">
                                                @php
                                                    $statusColors = [
                                                        'Pendente' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                                        'Em Análise' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                                        'Aprovada' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                                        'Rejeitada' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                                        'Contratada' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                                                    ];
                                                @endphp
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $statusColors[$application->status] ?? 'bg-gray-100 text-gray-800' }}">
                                                    {{ $application->status }}
                                                </span>
                                            </div>
                                        </div>

                                        <div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">Prioridade</div>
                                            <div class="mt-1">
                                                @if($application->is_vip_priority)
                                                    <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300">
                                                        <flux:icon icon="star" class="w-3 h-3 mr-1" />
                                                        VIP
                                                    </span>
                                                @else
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                        Regular
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Cover Letter Preview -->
                                    @if($application->cover_letter)
                                        <div class="mb-4">
                                            <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Carta de Apresentação</div>
                                            <div class="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-zinc-700 rounded-lg p-3">
                                                {{ Str::limit($application->cover_letter, 200) }}
                                            </div>
                                        </div>
                                    @endif

                                    <!-- Admin Notes -->
                                    @if($application->admin_notes)
                                        <div class="mb-4">
                                            <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Observações da Empresa</div>
                                            <div class="text-sm text-gray-700 dark:text-gray-300 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                                                {{ $application->admin_notes }}
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Actions -->
                                <div class="flex flex-col gap-2 ml-4">
                                    <flux:button 
                                        :href="route('job_vacancies.application.show', $application)" 
                                        size="sm" 
                                        variant="ghost"
                                        wire:navigate>
                                        <flux:icon icon="eye" class="w-4 h-4 mr-1" />
                                        Ver Detalhes
                                    </flux:button>

                                    @if($application->resume_path)
                                        <flux:button 
                                            :href="route('job_vacancies.application.resume', $application)" 
                                            size="sm" 
                                            variant="ghost"
                                            class="text-blue-600 hover:text-blue-800">
                                            <flux:icon icon="document-arrow-down" class="w-4 h-4 mr-1" />
                                            Baixar Currículo
                                        </flux:button>
                                    @endif

                                    @if($application->status === 'Pendente')
                                        <flux:button 
                                            wire:click="cancelApplication({{ $application->id }})"
                                            size="sm" 
                                            variant="ghost"
                                            class="text-red-600 hover:text-red-800"
                                            onclick="return confirm('Tem certeza que deseja cancelar esta candidatura?')">
                                            <flux:icon icon="x-mark" class="w-4 h-4 mr-1" />
                                            Cancelar
                                        </flux:button>
                                    @endif
                                </div>
                            </div>

                            <!-- Timeline/Progress -->
                            @if($application->reviewed_at)
                                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        <flux:icon icon="clock" class="w-4 h-4 inline mr-1" />
                                        Última atualização: {{ $application->reviewed_at->format('d/m/Y H:i') }}
                                        @if($application->reviewer)
                                            por {{ $application->reviewer->name }}
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $applications->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="flex flex-col items-center justify-center">
                    <flux:icon icon="clipboard-document-list" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhuma candidatura encontrada</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-6">Você ainda não se candidatou a nenhuma vaga.</p>
                    <flux:button :href="route('job_vacancies.index')" wire:navigate variant="primary">
                        <flux:icon icon="briefcase" class="w-4 h-4 mr-2" />
                        Explorar Vagas Disponíveis
                    </flux:button>
                </div>
            </div>
        @endif
    </div>
</x-layouts.app>
