<?php

namespace App\Livewire\Support;

use App\Models\HelpArticle;
use App\Models\FaqItem;
use App\Models\SupportTicket;
use Livewire\Component;
use Livewire\WithPagination;

class HelpCenter extends Component
{
    use WithPagination;

    public $activeTab = 'tutorials';
    public $searchQuery = '';
    public $selectedCategory = '';
    public $showCreateTicket = false;

    protected $queryString = [
        'activeTab' => ['except' => 'tutorials'],
        'searchQuery' => ['except' => ''],
        'selectedCategory' => ['except' => ''],
    ];

    public function mount()
    {
        // Se o usuário tem tickets abertos, mostrar aba de tickets por padrão
        if (auth()->user()->supportTickets()->open()->exists()) {
            $this->activeTab = 'tickets';
        }
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function updatedSearchQuery()
    {
        $this->resetPage();
    }

    public function updatedSelectedCategory()
    {
        $this->resetPage();
    }

    public function clearSearch()
    {
        $this->searchQuery = '';
        $this->selectedCategory = '';
        $this->resetPage();
    }

    protected $listeners = [
        'ticketCreated' => '$refresh',
        'ticketUpdated' => '$refresh',
    ];

    public function openCreateTicket()
    {
        $this->showCreateTicket = true;
    }

    public function getUserTicketsProperty()
    {
        if (!auth()->check()) {
            return collect();
        }

        return auth()->user()->supportTickets()
            ->with(['assignedTo', 'messages'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getHelpArticlesProperty()
    {
        $query = HelpArticle::published()->orderBy('sort_order')->orderBy('created_at', 'desc');

        if ($this->searchQuery) {
            $query->search($this->searchQuery);
        }

        if ($this->selectedCategory) {
            $query->byCategory($this->selectedCategory);
        }

        return $query->paginate(10);
    }

    public function getFaqItemsProperty()
    {
        $query = FaqItem::published()->orderBy('sort_order')->orderBy('created_at', 'desc');

        if ($this->searchQuery) {
            $query->search($this->searchQuery);
        }

        if ($this->selectedCategory) {
            $query->byCategory($this->selectedCategory);
        }

        return $query->paginate(10);
    }

    public function getFeaturedArticlesProperty()
    {
        return HelpArticle::published()
            ->featured()
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();
    }

    public function getFeaturedFaqsProperty()
    {
        return FaqItem::published()
            ->featured()
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();
    }

    public function getArticleCategoriesProperty()
    {
        return HelpArticle::published()
            ->select('category')
            ->distinct()
            ->orderBy('category')
            ->pluck('category');
    }

    public function getFaqCategoriesProperty()
    {
        return FaqItem::published()
            ->select('category')
            ->distinct()
            ->orderBy('category')
            ->pluck('category');
    }

    public function markArticleAsHelpful($articleId)
    {
        $article = HelpArticle::find($articleId);
        if ($article) {
            $article->markAsHelpful();
            $this->dispatch('notify', [
                'message' => 'Obrigado pelo seu feedback!',
                'type' => 'success'
            ]);
        }
    }

    public function markArticleAsNotHelpful($articleId)
    {
        $article = HelpArticle::find($articleId);
        if ($article) {
            $article->markAsNotHelpful();
            $this->dispatch('notify', [
                'message' => 'Obrigado pelo seu feedback! Vamos melhorar este conteúdo.',
                'type' => 'info'
            ]);
        }
    }

    public function markFaqAsHelpful($faqId)
    {
        $faq = FaqItem::find($faqId);
        if ($faq) {
            $faq->markAsHelpful();
            $this->dispatch('notify', [
                'message' => 'Obrigado pelo seu feedback!',
                'type' => 'success'
            ]);
        }
    }

    public function markFaqAsNotHelpful($faqId)
    {
        $faq = FaqItem::find($faqId);
        if ($faq) {
            $faq->markAsNotHelpful();
            $this->dispatch('notify', [
                'message' => 'Obrigado pelo seu feedback! Vamos melhorar esta resposta.',
                'type' => 'info'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.support.help-center-simple');
           
    }
}
