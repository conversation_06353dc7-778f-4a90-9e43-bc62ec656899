<?php

require_once 'vendor/autoload.php';

use App\Livewire\SearchForm;

try {
    echo "🧪 Testando componente SearchForm...\n";
    
    // Simular ambiente Laravel
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel inicializado\n";
    
    // Criar instância do componente
    $component = new SearchForm();
    echo "✅ Componente criado\n";
    
    // Testar mount
    $component->mount();
    echo "✅ Mount executado\n";
    
    // Verificar propriedades
    echo "📊 Propriedades:\n";
    echo "   - searchType: " . ($component->searchType ?? 'null') . "\n";
    echo "   - searchQuery: " . ($component->searchQuery ?? 'null') . "\n";
    echo "   - hasSearched: " . ($component->hasSearched ? 'true' : 'false') . "\n";
    echo "   - states count: " . count($component->states) . "\n";
    echo "   - procuras count: " . count($component->procuras) . "\n";
    
    echo "\n🎉 Teste concluído com sucesso!\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
