<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_applications', function (Blueprint $table) {
            // Campos de auditoria
            $table->string('ip_address')->nullable()->after('is_vip_priority');
            $table->text('user_agent')->nullable()->after('ip_address');
            $table->json('form_data')->nullable()->after('user_agent');
            $table->timestamp('email_sent_at')->nullable()->after('form_data');
            $table->timestamp('email_confirmed_at')->nullable()->after('email_sent_at');
            $table->json('email_log')->nullable()->after('email_confirmed_at');
            
            // Campos adicionais do candidato
            $table->string('candidate_name')->nullable()->after('email_log');
            $table->string('candidate_email')->nullable()->after('candidate_name');
            $table->string('candidate_phone')->nullable()->after('candidate_email');
            $table->text('candidate_experience')->nullable()->after('candidate_phone');
            
            // Campos de status avançado
            $table->enum('priority_level', ['baixa', 'normal', 'alta', 'urgente'])->default('normal')->after('candidate_experience');
            $table->json('status_history')->nullable()->after('priority_level');
            $table->timestamp('last_status_change')->nullable()->after('status_history');
            $table->unsignedBigInteger('status_changed_by')->nullable()->after('last_status_change');
            
            // Índices para performance
            $table->index(['status', 'is_vip_priority']);
            $table->index(['job_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index('priority_level');
            
            // Foreign key para quem mudou o status
            $table->foreign('status_changed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_applications', function (Blueprint $table) {
            // Remover foreign key primeiro
            $table->dropForeign(['status_changed_by']);
            
            // Remover índices
            $table->dropIndex(['status', 'is_vip_priority']);
            $table->dropIndex(['job_id', 'status']);
            $table->dropIndex(['user_id', 'created_at']);
            $table->dropIndex(['priority_level']);
            
            // Remover colunas
            $table->dropColumn([
                'ip_address',
                'user_agent',
                'form_data',
                'email_sent_at',
                'email_confirmed_at',
                'email_log',
                'candidate_name',
                'candidate_email',
                'candidate_phone',
                'candidate_experience',
                'priority_level',
                'status_history',
                'last_status_change',
                'status_changed_by',
            ]);
        });
    }
};
