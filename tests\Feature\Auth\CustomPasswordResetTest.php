<?php

use App\Models\User;
use App\Notifications\CustomResetPassword;
use Illuminate\Support\Facades\Notification;
use Livewire\Volt\Volt;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('custom reset password notification is sent', function () {
    Notification::fake();

    $user = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    Volt::test('auth.forgot-password')
        ->set('email', $user->email)
        ->call('sendPasswordResetLink');

    Notification::assertSentTo($user, CustomResetPassword::class);
});

test('forgot password page renders correctly', function () {
    $response = $this->get('/forgot-password');

    $response->assertStatus(200)
        ->assertSee('Esqueceu a Senha?')
        ->assertSee('Digite seu email para receber um link de redefinição de senha');
});

test('reset password page renders correctly', function () {
    $response = $this->get('/reset-password/fake-token?email=<EMAIL>');

    $response->assertStatus(200)
        ->assertSee('Redefinir Senha')
        ->assertSee('Digite sua nova senha para acessar sua conta');
});

test('password reset form validates email', function () {
    Volt::test('auth.forgot-password')
        ->set('email', 'invalid-email')
        ->call('sendPasswordResetLink')
        ->assertHasErrors(['email']);
});

test('password reset form validates required email', function () {
    Volt::test('auth.forgot-password')
        ->set('email', '')
        ->call('sendPasswordResetLink')
        ->assertHasErrors(['email']);
});
