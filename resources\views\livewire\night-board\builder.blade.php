<div class="min-h-screen bg-zinc-50 dark:bg-zinc-900 p-4">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-title mb-2">Monte Sua Noite</h1>
            <p class="text-body">Crie um tabuleiro interativo com suas experiências da noite e permita que outros patrocinem cada etapa!</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Blocos Temáticos -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-4">
                    <h3 class="text-lg font-semibold text-title mb-4">Blocos Temáticos</h3>
                    <div class="space-y-3">
                        @foreach($this->blockTypes as $type => $block)
                            @if(is_array($block) && isset($block['name'], $block['icon'], $block['color']))
                            <div
                                draggable="true"
                                data-block-type="{{ $type }}"
                                data-block-color="{{ $block['color'] }}"
                                wire:click="selectBlock('{{ $type }}')"
                                class="night-board-block w-full p-3 rounded-lg border-2 cursor-grab active:cursor-grabbing {{ $selectedBlock === $type ? 'selected border-zinc-400 dark:border-zinc-500 bg-zinc-100 dark:bg-zinc-700' : 'border-zinc-200 dark:border-zinc-600 hover:border-zinc-300 dark:hover:border-zinc-500' }}"
                                style="box-shadow: {{ $selectedBlock === $type ? '0 0 15px ' . $block['color'] . '50' : 'none' }}"
                                x-data
                                x-on:dragstart="
                                    $event.dataTransfer.setData('text/plain', '{{ $type }}');
                                    $event.dataTransfer.effectAllowed = 'copy';
                                    $el.classList.add('dragging');
                                "
                                x-on:dragend="
                                    $el.classList.remove('dragging');
                                "
                            >
                                <div class="flex items-center space-x-3">
                                    <flux:icon
                                        name="{{ $block['icon'] }}"
                                        class="block-icon w-6 h-6"
                                        style="color: {{ $block['color'] }};"
                                    />
                                    <span class="text-sm font-medium text-title">{{ $block['name'] }}</span>
                                </div>
                            </div>
                            @endif
                        @endforeach
                    </div>

                    <!-- Instruções -->
                    <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <h4 class="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">Como usar:</h4>
                        <ul class="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• Arraste blocos para as casas pretas</li>
                            <li>• Clique em uma casa com bloco para adicionar texto</li>
                            <li>• Clique no ícone de imagem para adicionar foto</li>
                            <li>• Casas brancas são para patrocínio</li>
                        </ul>
                    </div>

                    <!-- Controles -->
                    <div class="mt-6 space-y-3">
                        <flux:button
                            wire:click="clearBoard"
                            variant="ghost"
                            size="sm"
                            class="w-full"
                        >
                            <flux:icon name="trash" class="w-4 h-4 mr-2" />
                            Limpar Tabuleiro
                        </flux:button>

                        <flux:button
                            wire:click="openSaveModal"
                            variant="primary"
                            size="sm"
                            class="night-board-btn w-full neon-box-red"
                        >
                            <flux:icon name="bookmark" class="w-4 h-4 mr-2" />
                            Salvar Plano
                        </flux:button>
                    </div>
                </div>
            </div>

            <!-- Tabuleiro 8x8 -->
            <div class="lg:col-span-3">
                <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                    <h3 class="text-lg font-semibold text-title mb-4">Tabuleiro 8x8</h3>
                    <p class="text-sm text-body mb-4">
                        <span class="font-medium">Casas Pretas:</span> Arraste seus blocos aqui |
                        <span class="font-medium">Casas Brancas:</span> Espaços para patrocínio
                    </p>

                    <div class="grid grid-cols-8 gap-0 max-w-2xl mx-auto border-2 border-zinc-800 dark:border-zinc-400">
                        @if(is_array($board))
                        @foreach(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'] as $rowIndex => $row)
                            @for($col = 1; $col <= 8; $col++)
                                @php
                                    $position = (string) ($row . $col);
                                    $square = $board[$position] ?? ['type' => null, 'is_black' => false, 'custom_text' => '', 'image' => null];
                                    $isBlack = $square['is_black'] ?? false;
                                    $hasImage = !empty($square['image']);

                                    // Lógica correta para xadrez: soma da linha + coluna
                                    $isChessBlack = ($rowIndex + $col) % 2 === 0;
                                @endphp

                                <div
                                    wire:click="placeBlock('{{ $position }}')"
                                    data-position="{{ $position }}"
                                    data-is-black="{{ $isBlack ? 'true' : 'false' }}"
                                    class="chess-square aspect-square cursor-pointer relative {{ $isChessBlack ? 'bg-zinc-800 dark:bg-zinc-700' : 'bg-zinc-200 dark:bg-zinc-300' }} {{ $selectedBlock && $isBlack ? 'drag-over' : '' }}"
                                    x-data
                                    x-on:dragover.prevent="
                                        if ($el.dataset.isBlack === 'true') {
                                            $event.dataTransfer.dropEffect = 'copy';
                                            $el.classList.add('drag-over');
                                        } else {
                                            $event.dataTransfer.dropEffect = 'none';
                                            $el.classList.add('drag-invalid');
                                        }
                                    "
                                    x-on:dragleave="
                                        $el.classList.remove('drag-over', 'drag-invalid');
                                    "
                                    x-on:drop.prevent="
                                        $el.classList.remove('drag-over', 'drag-invalid');
                                        if ($el.dataset.isBlack === 'true') {
                                            const blockType = $event.dataTransfer.getData('text/plain');
                                            const position = $el.dataset.position;
                                            if (blockType && position) {
                                                $el.style.animation = 'bounce 0.5s ease-in-out';
                                                setTimeout(() => $el.style.animation = '', 500);
                                                $wire.placeBlockDrop(position, blockType);
                                            }
                                        }
                                    "
                                >
                                    <!-- Posição -->
                                    <div class="absolute top-1 left-1 text-xs font-mono {{ $isBlack ? 'text-zinc-400' : 'text-zinc-600' }}">
                                        {{ $position }}
                                    </div>

                                    <!-- Conteúdo da casa -->
                                    <div class="flex items-center justify-center h-full p-1">
                                        @if(isset($square['type']) && $square['type'] && isset($this->blockTypes[$square['type']]))
                                            <div class="text-center w-full">
                                                <flux:icon
                                                    name="{{ $this->blockTypes[$square['type']]['icon'] }}"
                                                    class="block-icon w-5 h-5 mx-auto mb-1"
                                                    style="color: {{ $this->blockTypes[$square['type']]['color'] }};"
                                                />
                                                <div class="text-xs text-zinc-300 font-medium mb-1">
                                                    {{ $this->blockTypes[$square['type']]['name'] }}
                                                </div>

                                                <!-- Controles -->
                                                <div class="flex items-center justify-center gap-2 mt-1">
                                                    <!-- Texto personalizado -->
                                                    @if(!empty($square['custom_text']))
                                                        <flux:tooltip content="{{ $square['custom_text'] }}" position="top">
                                                            <div class="custom-text-indicator text-zinc-200 cursor-help">
                                                                <flux:icon name="document-text" class="w-3 h-3 text-cyan-400" />
                                                            </div>
                                                        </flux:tooltip>
                                                    @endif

                                                    <!-- Imagem -->
                                                    @if($hasImage)
                                                        <button
                                                            wire:click.stop="viewSquareImage('{{ asset('storage/' . $square['image']) }}')"
                                                            class="text-green-400 hover:text-green-300"
                                                            title="Ver imagem"
                                                        >
                                                            <flux:icon name="photo" class="w-3 h-3" />
                                                        </button>
                                                    @else
                                                        <button
                                                            wire:click.stop="openImageUpload('{{ $position }}')"
                                                            class="text-zinc-400 hover:text-zinc-300"
                                                            title="Adicionar imagem"
                                                        >
                                                            <flux:icon name="camera" class="w-3 h-3" />
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>

                                            <!-- Botão de remover -->
                                            <button
                                                wire:click.stop="removeBlock('{{ $position }}')"
                                                class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs transition-all duration-200"
                                            >
                                                ×
                                            </button>
                                        @elseif($isBlack && $selectedBlock && isset($this->blockTypes[$selectedBlock]))
                                            <div class="text-center opacity-50">
                                                <flux:icon
                                                    name="{{ $this->blockTypes[$selectedBlock]['icon'] }}"
                                                    class="w-6 h-6 mx-auto"
                                                    style="color: {{ $this->blockTypes[$selectedBlock]['color'] }};"
                                                />
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endfor
                        @endforeach
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Salvar -->
    <flux:modal wire:model="showSaveModal" class="max-w-md">
        <flux:modal.header>
            <flux:heading>Salvar Plano da Noite</flux:heading>
        </flux:modal.header>

        <flux:modal.body>
            <div class="space-y-4">
                <flux:field>
                    <flux:label>Título do Plano</flux:label>
                    <flux:input wire:model="title" placeholder="Ex: Noite Romântica no Centro" />
                    <flux:error name="title" />
                </flux:field>

                <flux:field>
                    <flux:label>Descrição (opcional)</flux:label>
                    <flux:textarea wire:model="description" placeholder="Descreva seu plano da noite..." rows="3" />
                    <flux:error name="description" />
                </flux:field>

                <flux:field>
                    <flux:checkbox wire:model="is_public">
                        Tornar público (outros usuários poderão ver e patrocinar)
                    </flux:checkbox>
                </flux:field>
            </div>
        </flux:modal.body>

        <flux:modal.footer>
            <flux:button wire:click="$set('showSaveModal', false)" variant="ghost">
                Cancelar
            </flux:button>
            <flux:button wire:click="savePlan" variant="primary">
                Salvar Plano
            </flux:button>
        </flux:modal.footer>
    </flux:modal>

    <!-- Modal de Texto Personalizado -->
    <flux:modal wire:model="showTextModal" class="max-w-md">
        <flux:modal.header>
            <flux:heading>
                @if($editingPosition)
                    Editar Texto do Bloco
                @else
                    Adicionar Texto Personalizado
                @endif
            </flux:heading>
        </flux:modal.header>

        <flux:modal.body>
            <div class="space-y-4">
                @if($currentPosition)
                    <div class="text-sm text-zinc-600 dark:text-zinc-400">
                        <strong>Posição:</strong> {{ $currentPosition }}
                        @if(isset($board[$currentPosition]['type']) && isset($this->blockTypes[$board[$currentPosition]['type']]))
                            <br><strong>Bloco:</strong> {{ $this->blockTypes[$board[$currentPosition]['type']]['name'] }}
                        @endif
                    </div>
                @endif

                <flux:field>
                    <flux:label>Texto Personalizado</flux:label>
                    <flux:textarea
                        wire:model="customText"
                        placeholder="Descreva esta etapa da sua noite..."
                        rows="3"
                        maxlength="100"
                    />
                    <flux:description>
                        Máximo 100 caracteres. Atual: {{ strlen($customText) }}/100
                    </flux:description>
                </flux:field>

                <div class="text-xs text-zinc-500 dark:text-zinc-400">
                    💡 <strong>Dica:</strong> Use este espaço para detalhar o que acontece nesta etapa da sua noite.
                    Seja criativo e específico!
                </div>
            </div>
        </flux:modal.body>

        <flux:modal.footer>
            <flux:button wire:click="closeTextModal" variant="ghost">
                @if($editingPosition)
                    Cancelar
                @else
                    Pular
                @endif
            </flux:button>
            <flux:button wire:click="saveCustomText" variant="primary">
                Salvar Texto
            </flux:button>
        </flux:modal.footer>
    </flux:modal>

    <!-- Modal de Upload de Imagem -->
    <flux:modal wire:model="showImageModal" size="md">
        <flux:modal.header>
            <flux:heading size="lg">Adicionar Imagem</flux:heading>
        </flux:modal.header>

        <flux:modal.body>
            <div class="space-y-4">
                <flux:field>
                    <flux:label>Selecione uma imagem</flux:label>
                    <input
                        type="file"
                        wire:model="squareImage"
                        accept="image/*"
                        class="w-full text-sm text-zinc-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    >
                    @error('squareImage')
                        <flux:description variant="danger">{{ $message }}</flux:description>
                    @enderror
                </flux:field>

                @if($squareImage)
                    <div class="mt-4">
                        <p class="text-sm text-green-600 mb-2">Preview:</p>
                        <img src="{{ $squareImage->temporaryUrl() }}" alt="Preview" class="w-32 h-32 object-cover rounded-lg mx-auto">
                    </div>
                @endif

                <div class="text-xs text-zinc-500 dark:text-zinc-400">
                    💡 <strong>Dica:</strong> A imagem será redimensionada para caber no quadrado. Use imagens quadradas para melhor resultado.
                </div>
            </div>
        </flux:modal.body>

        <flux:modal.footer>
            <flux:button wire:click="$set('showImageModal', false)" variant="ghost">
                Cancelar
            </flux:button>
            <flux:button wire:click="uploadSquareImage" variant="primary" :disabled="!$squareImage">
                Adicionar Imagem
            </flux:button>
        </flux:modal.footer>
    </flux:modal>

    <!-- Modal de Visualização de Imagem (usando o mesmo do feed) -->
    <div x-data="{
        showImageModal: false,
        modalImageUrl: '',
        zoom: 1,
        offsetX: 0,
        offsetY: 0,
        openModal(url) {
            this.modalImageUrl = url;
            this.zoom = 1;
            this.offsetX = 0;
            this.offsetY = 0;
            this.showImageModal = true;
            document.body.style.overflow = 'hidden';
        },
        closeModal() {
            this.showImageModal = false;
            document.body.style.overflow = '';
        },
        zoomIn() { this.zoom += 0.2; },
        zoomOut() { if(this.zoom > 0.4) this.zoom -= 0.2; }
    }"
    @open-image-modal.window="openModal($event.detail)"
    >
        <!-- Modal de imagem ampliada -->
        <div x-show="showImageModal" x-transition class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80" style="display: none;">
            <div class="relative flex items-center justify-center w-full h-full select-none">
                <!-- Botão fechar -->
                <button @click="closeModal" class="absolute top-4 right-4 bg-gray-800 bg-opacity-70 rounded-full p-2 text-white hover:bg-opacity-100 z-20">
                    <svg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'/></svg>
                </button>

                <!-- Imagem -->
                <div class="flex flex-col items-center w-full">
                    <img :src="modalImageUrl"
                         :style="'transform: translate(' + offsetX + 'px,' + offsetY + 'px) scale(' + zoom + '); max-width:90vw; max-height:80vh; cursor: grab; user-select: none;'"
                         class="rounded-lg shadow-lg transition-transform duration-200 z-10 select-none"
                         alt="Imagem ampliada"
                    >

                    <!-- Controles de zoom -->
                    <div class="flex items-center gap-4 mt-4 bg-gray-800 bg-opacity-70 rounded-full px-4 py-2">
                        <button @click="zoomOut" class="text-white hover:text-gray-300">
                            <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M20 12H4'/></svg>
                        </button>
                        <span class="text-white text-sm" x-text="Math.round(zoom * 100) + '%'"></span>
                        <button @click="zoomIn" class="text-white hover:text-gray-300">
                            <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 4v16m8-8H4'/></svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>