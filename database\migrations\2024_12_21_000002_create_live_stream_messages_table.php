<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('live_stream_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('live_stream_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('message');
            $table->string('type')->default('message'); // message, donation, system
            $table->json('data')->nullable(); // Dados extras (valor da doação, etc)
            $table->timestamps();
            
            $table->index(['live_stream_id', 'created_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('live_stream_messages');
    }
};
