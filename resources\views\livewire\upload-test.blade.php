<div class="max-w-4xl mx-auto p-6">
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            🧪 Teste de Upload - Limite 200MB
        </h2>

        <!-- Status e Mensagens -->
        @if($uploadStatus)
            <div class="bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-4">
                <pre class="whitespace-pre-wrap">{{ $uploadStatus }}</pre>
            </div>
        @endif

        @if($errorMessage)
            <div class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
                {{ $errorMessage }}
            </div>
        @endif

        <div class="grid md:grid-cols-2 gap-6">
            <!-- Teste de Upload de Imagem -->
            <div class="border border-gray-300 dark:border-gray-600 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    📸 Teste de Imagem
                </h3>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Selecionar Imagem (até 200MB)
                    </label>
                    <input 
                        type="file" 
                        wire:model="testImage" 
                        accept="image/*"
                        class="block w-full text-sm text-gray-500 dark:text-gray-400
                               file:mr-4 file:py-2 file:px-4
                               file:rounded-full file:border-0
                               file:text-sm file:font-semibold
                               file:bg-purple-50 file:text-purple-700
                               hover:file:bg-purple-100
                               dark:file:bg-purple-900 dark:file:text-purple-300"
                    >
                    @error('testImage') 
                        <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> 
                    @enderror
                </div>

                @if($testImage)
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            📁 Arquivo: {{ $testImage->getClientOriginalName() }}<br>
                            📏 Tamanho: {{ number_format($testImage->getSize() / 1048576, 2) }} MB
                        </p>
                        @if(method_exists($testImage, 'temporaryUrl'))
                            <img src="{{ $testImage->temporaryUrl() }}" 
                                 class="mt-2 max-w-xs h-auto rounded-lg shadow-sm" 
                                 alt="Preview">
                        @endif
                    </div>
                @endif

                <button 
                    wire:click="testImageUpload" 
                    wire:loading.attr="disabled"
                    wire:target="testImageUpload"
                    class="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white font-bold py-2 px-4 rounded"
                    @if(!$testImage) disabled @endif
                >
                    <span wire:loading.remove wire:target="testImageUpload">Testar Upload de Imagem</span>
                    <span wire:loading wire:target="testImageUpload">Enviando...</span>
                </button>
            </div>

            <!-- Teste de Upload de Vídeo -->
            <div class="border border-gray-300 dark:border-gray-600 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    🎥 Teste de Vídeo
                </h3>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Selecionar Vídeo (até 200MB)
                    </label>
                    <input 
                        type="file" 
                        wire:model="testVideo" 
                        accept="video/*"
                        class="block w-full text-sm text-gray-500 dark:text-gray-400
                               file:mr-4 file:py-2 file:px-4
                               file:rounded-full file:border-0
                               file:text-sm file:font-semibold
                               file:bg-blue-50 file:text-blue-700
                               hover:file:bg-blue-100
                               dark:file:bg-blue-900 dark:file:text-blue-300"
                    >
                    @error('testVideo') 
                        <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> 
                    @enderror
                </div>

                @if($testVideo)
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            📁 Arquivo: {{ $testVideo->getClientOriginalName() }}<br>
                            📏 Tamanho: {{ number_format($testVideo->getSize() / 1048576, 2) }} MB
                        </p>
                        @if(method_exists($testVideo, 'temporaryUrl'))
                            <video controls class="mt-2 max-w-xs h-auto rounded-lg shadow-sm">
                                <source src="{{ $testVideo->temporaryUrl() }}" type="video/mp4">
                                Seu navegador não suporta o elemento de vídeo.
                            </video>
                        @endif
                    </div>
                @endif

                <button 
                    wire:click="testVideoUpload" 
                    wire:loading.attr="disabled"
                    wire:target="testVideoUpload"
                    class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-bold py-2 px-4 rounded"
                    @if(!$testVideo) disabled @endif
                >
                    <span wire:loading.remove wire:target="testVideoUpload">Testar Upload de Vídeo</span>
                    <span wire:loading wire:target="testVideoUpload">Enviando...</span>
                </button>
            </div>
        </div>

        <!-- Botão para limpar resultados -->
        @if($uploadStatus || $errorMessage)
            <div class="mt-6 text-center">
                <button 
                    wire:click="clearResults" 
                    class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded"
                >
                    Limpar Resultados
                </button>
            </div>
        @endif

        <!-- Informações do Sistema -->
        <div class="mt-8 bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                ℹ️ Informações do Sistema
            </h4>
            <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <p><strong>Upload Max Filesize:</strong> {{ ini_get('upload_max_filesize') }}</p>
                <p><strong>Post Max Size:</strong> {{ ini_get('post_max_size') }}</p>
                <p><strong>Max Execution Time:</strong> {{ ini_get('max_execution_time') }}s</p>
                <p><strong>Memory Limit:</strong> {{ ini_get('memory_limit') }}</p>
            </div>
        </div>

        <!-- Instruções -->
        <div class="mt-6 bg-blue-100 dark:bg-blue-900 border border-blue-400 text-blue-700 dark:text-blue-300 px-4 py-3 rounded">
            <h4 class="font-semibold mb-2">📋 Como usar este teste:</h4>
            <ul class="list-disc list-inside space-y-1 text-sm">
                <li>Selecione uma imagem ou vídeo de até 200MB</li>
                <li>Clique no botão de teste correspondente</li>
                <li>Aguarde o upload completar</li>
                <li>Verifique se há mensagens de erro ou sucesso</li>
                <li>Os arquivos de teste são salvos em storage/app/public/test-uploads/</li>
            </ul>
        </div>
    </div>
</div>
