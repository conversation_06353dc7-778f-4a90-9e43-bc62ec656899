<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use App\Models\Level; // Importar o modelo Level
use Livewire\WithPagination; // Para paginação

class ManageLevels extends Component
{
    use WithPagination;

    public $level, $min_points, $permissions;
    public $level_id = null; // Para armazenar o ID do nível em edição
    public $is_editing = false; // Flag para saber se está editando

    protected $rules = [
        'level' => 'required|integer|min:1|unique:levels,level',
        'min_points' => 'required|integer|min:0',
        'permissions' => 'nullable|string', // Ou validar como JSON se usar text area
    ];

    public function render()
    {
        $levels = Level::orderBy('min_points', 'asc')->paginate(10); // Ordenar por pontos
        return view('livewire.admin.manage-levels', [
            'levels' => $levels,
        ]);
    }

    public function createLevel()
    {
        $this->validate();

        Level::create([
            'level' => $this->level,
            'min_points' => $this->min_points,
            'permissions' => json_decode($this->permissions, true) ?? [], // Decodificar JSON ou array vazio
        ]);

        session()->flash('message', 'Nível criado com sucesso!');

        $this->resetForm();
    }

    public function editLevel($id)
    {
        $level = Level::findOrFail($id);

        $this->level_id = $level->id;
        $this->level = $level->level;
        $this->min_points = $level->min_points;
        $this->permissions = json_encode($level->permissions); // Codificar para JSON para exibir no campo
        $this->is_editing = true;

        // Atualiza as regras de validação para edição (ignorar unique para o próprio registro)
        $this->rules['level'] = 'required|integer|min:1|unique:levels,level,' . $this->level_id;
    }

    public function updateLevel()
    {
        $this->validate();

        $level = Level::findOrFail($this->level_id);

        $level->update([
            'level' => $this->level,
            'min_points' => $this->min_points,
            'permissions' => json_decode($this->permissions, true) ?? [],
        ]);

        session()->flash('message', 'Nível atualizado com sucesso!');

        $this->resetForm();
    }

    public function deleteLevel($id)
    {
        Level::destroy($id);
        session()->flash('message', 'Nível deletado com sucesso!');
    }

    public function resetForm()
    {
        $this->level = '';
        $this->min_points = '';
        $this->permissions = '';
        $this->level_id = null;
        $this->is_editing = false;

        // Restaura as regras de validação para criação
        $this->rules['level'] = 'required|integer|min:1|unique:levels,level';
    }
}
