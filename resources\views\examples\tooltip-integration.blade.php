{{-- Exemplo de Integração de Tooltips no Projeto --}}
<x-layouts.app>
    <div class="p-6 space-y-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Integração de Tooltips no Projeto
        </h1>

        {{-- Simulação da Sidebar com Tooltips --}}
        <div class="space-y-4">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Sidebar com Tooltips Customizados
            </h2>
            
            <div class="bg-zinc-900 p-4 rounded-lg w-64">
                <nav class="space-y-2">
                    <flux:tooltip content="Página principal do sistema" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="home" class="w-5 h-5" />
                            <span class="lg:block hidden">Principal</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Buscar usuários e conteúdo no sistema" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="magnifying-glass" class="w-5 h-5" />
                            <span class="lg:block hidden">Busca</span>
                            <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">12</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Encontrar usuários próximos à sua localização" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="map-pin" class="w-5 h-5" />
                            <span class="lg:block hidden">Radar</span>
                            <span class="ml-auto bg-green-500 text-white text-xs rounded-full px-2 py-1">5</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Suas conversas e mensagens privadas" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="chat-bubble-left-right" class="w-5 h-5" />
                            <span class="lg:block hidden">Mensagens</span>
                            <span class="ml-auto bg-blue-500 text-white text-xs rounded-full px-2 py-1">3</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Ranking de usuários por pontuação" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="trophy" class="w-5 h-5" />
                            <span class="lg:block hidden">Ranking</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Grupos e comunidades disponíveis" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="user-group" class="w-5 h-5" />
                            <span class="lg:block hidden">Grupos</span>
                            <span class="ml-auto bg-purple-500 text-white text-xs rounded-full px-2 py-1">8</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Histórias e contos dos usuários" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="book-open" class="w-5 h-5" />
                            <span class="lg:block hidden">Contos</span>
                            <span class="ml-auto bg-yellow-500 text-white text-xs rounded-full px-2 py-1">15</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Feed de postagens e atualizações" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="newspaper" class="w-5 h-5" />
                            <span class="lg:block hidden">Feed</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Loja virtual com produtos e serviços" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="shopping-bag" class="w-5 h-5" />
                            <span class="lg:block hidden">Loja</span>
                        </a>
                    </flux:tooltip>

                    <flux:tooltip content="Sua carteira digital - Saldo: R$ 150,00" position="right">
                        <a href="#" class="flex items-center gap-3 px-3 py-2 text-gray-300 hover:text-white hover:bg-zinc-800 rounded-lg transition-colors">
                            <x-flux::icon name="wallet" class="w-5 h-5" />
                            <span class="lg:block hidden">Carteira</span>
                            <span class="ml-auto text-green-400 text-sm font-medium">R$ 150,00</span>
                        </a>
                    </flux:tooltip>
                </nav>
            </div>
        </div>

        {{-- Simulação de Cards de Usuário com Tooltips --}}
        <div class="space-y-4">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Cards de Usuário com Tooltips
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {{-- Card 1 --}}
                <div class="bg-white dark:bg-zinc-800 rounded-lg p-4 shadow-lg">
                    <div class="flex items-center gap-3 mb-3">
                        <flux:tooltip content="Usuário online - Última atividade: agora" position="top">
                            <div class="relative">
                                <img src="https://via.placeholder.com/40" alt="Avatar" class="w-10 h-10 rounded-full">
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                            </div>
                        </flux:tooltip>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">Maria Silva</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">@maria_silva</p>
                        </div>
                    </div>
                    
                    <div class="flex gap-2 mb-3">
                        <flux:tooltip content="Enviar mensagem privada" position="top">
                            <flux:button variant="outline" size="sm" icon="chat-bubble-left">
                                Mensagem
                            </flux:button>
                        </flux:tooltip>
                        
                        <flux:tooltip content="Seguir este usuário para ver suas atualizações" position="top">
                            <flux:button variant="primary" size="sm" icon="plus">
                                Seguir
                            </flux:button>
                        </flux:tooltip>
                    </div>
                    
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <flux:tooltip content="Número de seguidores" position="bottom">
                            <span>1.2k seguidores</span>
                        </flux:tooltip>
                        
                        <flux:tooltip content="Pontuação total do usuário" position="bottom">
                            <span>850 pontos</span>
                        </flux:tooltip>
                    </div>
                </div>

                {{-- Card 2 --}}
                <div class="bg-white dark:bg-zinc-800 rounded-lg p-4 shadow-lg">
                    <div class="flex items-center gap-3 mb-3">
                        <flux:tooltip content="Usuário ausente - Última atividade: 2 horas atrás" position="top">
                            <div class="relative">
                                <img src="https://via.placeholder.com/40" alt="Avatar" class="w-10 h-10 rounded-full">
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-yellow-500 border-2 border-white rounded-full"></div>
                            </div>
                        </flux:tooltip>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">João Santos</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">@joao_santos</p>
                        </div>
                    </div>
                    
                    <div class="flex gap-2 mb-3">
                        <flux:tooltip content="Enviar mensagem privada" position="top">
                            <flux:button variant="outline" size="sm" icon="chat-bubble-left">
                                Mensagem
                            </flux:button>
                        </flux:tooltip>
                        
                        <flux:tooltip content="Você já segue este usuário" position="top">
                            <flux:button variant="ghost" size="sm" icon="check">
                                Seguindo
                            </flux:button>
                        </flux:tooltip>
                    </div>
                    
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <flux:tooltip content="Número de seguidores" position="bottom">
                            <span>856 seguidores</span>
                        </flux:tooltip>
                        
                        <flux:tooltip content="Pontuação total do usuário" position="bottom">
                            <span>1.2k pontos</span>
                        </flux:tooltip>
                    </div>
                </div>

                {{-- Card 3 --}}
                <div class="bg-white dark:bg-zinc-800 rounded-lg p-4 shadow-lg">
                    <div class="flex items-center gap-3 mb-3">
                        <flux:tooltip content="Usuário offline - Última atividade: 1 dia atrás" position="top">
                            <div class="relative">
                                <img src="https://via.placeholder.com/40" alt="Avatar" class="w-10 h-10 rounded-full">
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gray-500 border-2 border-white rounded-full"></div>
                            </div>
                        </flux:tooltip>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">Ana Costa</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">@ana_costa</p>
                        </div>
                    </div>
                    
                    <div class="flex gap-2 mb-3">
                        <flux:tooltip content="Enviar mensagem privada" position="top">
                            <flux:button variant="outline" size="sm" icon="chat-bubble-left">
                                Mensagem
                            </flux:button>
                        </flux:tooltip>
                        
                        <flux:tooltip content="Seguir este usuário para ver suas atualizações" position="top">
                            <flux:button variant="primary" size="sm" icon="plus">
                                Seguir
                            </flux:button>
                        </flux:tooltip>
                    </div>
                    
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <flux:tooltip content="Número de seguidores" position="bottom">
                            <span>2.1k seguidores</span>
                        </flux:tooltip>
                        
                        <flux:tooltip content="Pontuação total do usuário" position="bottom">
                            <span>1.8k pontos</span>
                        </flux:tooltip>
                    </div>
                </div>
            </div>
        </div>

        {{-- Simulação de Ações com Tooltips --}}
        <div class="space-y-4">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Ações com Tooltips Informativos
            </h2>
            
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Configurações de Perfil</h3>
                    
                    <div class="flex gap-2">
                        <flux:tooltip content="Salvar todas as alterações feitas" kbd="Ctrl+S" position="bottom">
                            <flux:button variant="primary" icon="check">
                                Salvar
                            </flux:button>
                        </flux:tooltip>
                        
                        <flux:tooltip content="Cancelar e descartar alterações" kbd="Esc" position="bottom">
                            <flux:button variant="outline" icon="x-mark">
                                Cancelar
                            </flux:button>
                        </flux:tooltip>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <flux:tooltip content="Seu nome completo como aparece no perfil" position="top">
                            <flux:field>
                                <flux:label>Nome Completo</flux:label>
                                <flux:input placeholder="Digite seu nome completo" />
                            </flux:field>
                        </flux:tooltip>
                    </div>
                    
                    <div>
                        <flux:tooltip content="Nome único para identificação no sistema (sem espaços)" position="top">
                            <flux:field>
                                <flux:label>Nome de Usuário</flux:label>
                                <flux:input placeholder="@seu_usuario" />
                            </flux:field>
                        </flux:tooltip>
                    </div>
                    
                    <div class="md:col-span-2">
                        <flux:tooltip content="Descrição que aparece no seu perfil público" position="top">
                            <flux:field>
                                <flux:label>Biografia</flux:label>
                                <flux:textarea placeholder="Conte um pouco sobre você..." rows="3" />
                            </flux:field>
                        </flux:tooltip>
                    </div>
                </div>
            </div>
        </div>

        {{-- Informações sobre Implementação --}}
        <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 class="text-md font-semibold text-blue-800 dark:text-blue-200 mb-2">
                Como Implementar:
            </h3>
            <div class="text-sm text-blue-700 dark:text-blue-300 space-y-2">
                <p><strong>1. Tooltip Simples:</strong> <code>&lt;flux:tooltip content="Texto do tooltip"&gt;...&lt;/flux:tooltip&gt;</code></p>
                <p><strong>2. Com Posição:</strong> <code>&lt;flux:tooltip content="Texto" position="top|bottom|left|right"&gt;...&lt;/flux:tooltip&gt;</code></p>
                <p><strong>3. Com Atalho:</strong> <code>&lt;flux:tooltip content="Texto" kbd="Ctrl+S"&gt;...&lt;/flux:tooltip&gt;</code></p>
                <p><strong>4. Clicável:</strong> <code>&lt;flux:tooltip content="Texto" toggleable&gt;...&lt;/flux:tooltip&gt;</code></p>
                <p><strong>5. Interativo:</strong> <code>&lt;flux:tooltip content="Texto" interactive&gt;...&lt;/flux:tooltip&gt;</code></p>
            </div>
        </div>
    </div>
</x-layouts.app>
