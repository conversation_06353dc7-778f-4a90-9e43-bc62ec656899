<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\JobCategory;
use Illuminate\Support\Str;

class JobCategoryManager extends Component
{
    use WithPagination;

    public $search = '';
    public $sortBy = 'name';
    public $sortDirection = 'asc';

    // Modal properties
    public $showModal = false;
    public $editingCategory = null;
    public $name = '';
    public $description = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'sortBy' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    protected $rules = [
        'name' => 'required|string|max:255|unique:job_categories,name',
        'description' => 'nullable|string|max:1000',
    ];

    protected $messages = [
        'name.required' => 'O nome da categoria é obrigatório.',
        'name.unique' => 'Já existe uma categoria com este nome.',
        'name.max' => 'O nome não pode ter mais de 255 caracteres.',
        'description.max' => 'A descrição não pode ter mais de 1000 caracteres.',
    ];

    public function mount()
    {
        //
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function create()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function edit($categoryId)
    {
        $this->editingCategory = JobCategory::findOrFail($categoryId);
        $this->name = $this->editingCategory->name;
        $this->description = $this->editingCategory->description;
        $this->showModal = true;
    }

    public function save()
    {
        $rules = $this->rules;

        // Se estiver editando, ignorar a categoria atual na validação de unicidade
        if ($this->editingCategory) {
            $rules['name'] = 'required|string|max:255|unique:job_categories,name,' . $this->editingCategory->id;
        }

        $this->validate($rules);

        $data = [
            'name' => $this->name,
            'description' => $this->description,
            'slug' => Str::slug($this->name),
        ];

        if ($this->editingCategory) {
            $this->editingCategory->update($data);
            session()->flash('message', 'Categoria atualizada com sucesso!');
        } else {
            JobCategory::create($data);
            session()->flash('message', 'Categoria criada com sucesso!');
        }

        $this->closeModal();
    }

    public function delete($categoryId)
    {
        $category = JobCategory::findOrFail($categoryId);

        // Verificar se a categoria tem vagas associadas
        if ($category->jobVacancies()->count() > 0) {
            session()->flash('error', 'Não é possível excluir uma categoria que possui vagas associadas.');
            return;
        }

        $category->delete();
        session()->flash('message', 'Categoria excluída com sucesso!');
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->editingCategory = null;
        $this->name = '';
        $this->description = '';
        $this->resetErrorBag();
    }

    public function getCategoriesProperty()
    {
        $query = JobCategory::withCount('jobVacancies')
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(15);
    }

    public function render()
    {
        return view('livewire.admin.job-category-manager', [
            'categories' => $this->categories,
        ]);
    }
}
