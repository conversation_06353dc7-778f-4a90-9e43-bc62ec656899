<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class SupportTicket extends Model
{
    protected $fillable = [
        'user_id',
        'assigned_to',
        'ticket_number',
        'category',
        'priority',
        'status',
        'title',
        'description',
        'attachments',
        'resolved_at',
        'closed_at',
        'rating',
        'rating_comment',
        'is_premium',
        'premium_amount',
    ];

    protected $casts = [
        'attachments' => 'array',
        'resolved_at' => 'datetime',
        'closed_at' => 'datetime',
        'is_premium' => 'boolean',
        'premium_amount' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($ticket) {
            if (!$ticket->ticket_number) {
                $ticket->ticket_number = 'TK-' . strtoupper(Str::random(8));
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(SupportTicketMessage::class, 'ticket_id');
    }

    public function publicMessages(): HasMany
    {
        return $this->hasMany(SupportTicketMessage::class, 'ticket_id')
                    ->where('is_internal', false);
    }

    public function internalMessages(): HasMany
    {
        return $this->hasMany(SupportTicketMessage::class, 'ticket_id')
                    ->where('is_internal', true);
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'aberto' => 'blue',
            'em_andamento' => 'yellow',
            'aguardando_resposta' => 'orange',
            'resolvido' => 'green',
            'fechado' => 'gray',
            default => 'gray',
        };
    }

    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'baixa' => 'green',
            'media' => 'yellow',
            'alta' => 'orange',
            'urgente' => 'red',
            default => 'gray',
        };
    }

    public function canBeEdited(): bool
    {
        return $this->isOpen() && $this->user_id === auth()->id();
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'aberto' => 'Aberto',
            'em_andamento' => 'Em Andamento',
            'aguardando_resposta' => 'Aguardando Resposta',
            'resolvido' => 'Resolvido',
            'fechado' => 'Fechado',
            default => 'Desconhecido',
        };
    }

    public function getPriorityLabelAttribute(): string
    {
        return match($this->priority) {
            'baixa' => 'Baixa',
            'media' => 'Média',
            'alta' => 'Alta',
            'urgente' => 'Urgente',
            default => 'Desconhecida',
        };
    }

    public function scopeOpen($query)
    {
        return $query->whereIn('status', ['aberto', 'em_andamento', 'aguardando_resposta']);
    }

    public function scopeClosed($query)
    {
        return $query->whereIn('status', ['resolvido', 'fechado']);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function isOpen(): bool
    {
        return in_array($this->status, ['aberto', 'em_andamento', 'aguardando_resposta']);
    }

    public function isClosed(): bool
    {
        return in_array($this->status, ['resolvido', 'fechado']);
    }

    public function canBeRated(): bool
    {
        return $this->status === 'resolvido' && !$this->rating;
    }
}
