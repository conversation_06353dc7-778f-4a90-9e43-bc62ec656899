<?php

namespace Database\Factories;

use App\Models\JobVacancy;
use App\Models\JobCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobVacancy>
 */
class JobVacancyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = JobVacancy::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->randomElement([
            'Desenvolvedor PHP',
            'Analista de Marketing',
            'Vendedor Externo',
            'Analista de RH',
            'Contador',
            'Operador de Produção',
            'Designer Gráfico',
            'Atendente de Call Center',
            'Motorista',
            'Advogado Júnior'
        ]);

        return [
            'title' => $title,
            'slug' => Str::slug($title) . '-' . $this->faker->randomNumber(4),
            'description' => $this->faker->paragraphs(3, true),
            'requirements' => $this->faker->paragraphs(2, true),
            'benefits' => $this->faker->paragraphs(2, true),
            'category_id' => JobCategory::factory(),
            'contract_type' => $this->faker->randomElement(['CLT', 'PJ', 'Freelance', 'Estágio', 'Temporário']),
            'work_mode' => $this->faker->randomElement(['Presencial', 'Remoto', 'Híbrido']),
            'location' => $this->faker->city() . ', ' . $this->faker->stateAbbr(),
            'salary_min' => $this->faker->randomFloat(2, 1500, 5000),
            'salary_max' => $this->faker->randomFloat(2, 5000, 15000),
            'salary_period' => 'Mensal',
            'salary_negotiable' => $this->faker->boolean(30),
            'vacancies' => $this->faker->numberBetween(1, 5),
            'application_deadline' => $this->faker->optional(0.7)->dateTimeBetween('now', '+3 months'),
            'experience_level' => $this->faker->randomElement(['Júnior', 'Pleno', 'Sênior', 'Especialista']),
            'requires_resume' => $this->faker->boolean(90),
            'requires_cover_letter' => $this->faker->boolean(60),
            'is_featured' => $this->faker->boolean(20),
            'is_active' => true,
            'views_count' => $this->faker->numberBetween(0, 1000),
            'applications_count' => $this->faker->numberBetween(0, 50),
        ];
    }

    /**
     * Indicate that the job is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the job is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the job requires resume.
     */
    public function requiresResume(): static
    {
        return $this->state(fn (array $attributes) => [
            'requires_resume' => true,
        ]);
    }

    /**
     * Indicate that the job requires cover letter.
     */
    public function requiresCoverLetter(): static
    {
        return $this->state(fn (array $attributes) => [
            'requires_cover_letter' => true,
        ]);
    }

    /**
     * Indicate that the job has expired deadline.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'application_deadline' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
        ]);
    }

    /**
     * Indicate that the job is remote.
     */
    public function remote(): static
    {
        return $this->state(fn (array $attributes) => [
            'work_mode' => 'Remoto',
            'location' => null,
        ]);
    }
}
