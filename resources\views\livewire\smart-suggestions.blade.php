<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <flux:icon.sparkles class="w-5 h-5 mr-2 text-purple-500" />
            Sugestões Inteligentes
        </h3>
        
        <div class="flex gap-2">
            <flux:button.group>
                <flux:button 
                    wire:click="setSuggestionType('all')" 
                    variant="{{ $suggestionType === 'all' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    Todas
                </flux:button>
                <flux:button 
                    wire:click="setSuggestionType('location')" 
                    variant="{{ $suggestionType === 'location' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    <flux:icon.map-pin class="w-4 h-4 mr-1" />
                    Local
                </flux:button>
                <flux:button 
                    wire:click="setSuggestionType('interests')" 
                    variant="{{ $suggestionType === 'interests' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    <flux:icon.heart class="w-4 h-4 mr-1" />
                    Interesses
                </flux:button>
                <flux:button 
                    wire:click="setSuggestionType('groups')" 
                    variant="{{ $suggestionType === 'groups' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    <flux:icon.user-group class="w-4 h-4 mr-1" />
                    Grupos
                </flux:button>
            </flux:button.group>
        </div>
    </div>

    @if($suggestions->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($suggestions as $suggestion)
                <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-zinc-600 transition-colors">
                    {{-- Header do Card --}}
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <a href="/{{ $suggestion->username }}" wire:navigate class="relative mr-3">
                                <img 
                                    src="{{ $this->getAvatar($suggestion) }}" 
                                    alt="{{ $suggestion->name }}"
                                    class="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-600"
                                >
                                <livewire:user-status-indicator :userId="$suggestion->id" :key="'status-'.$suggestion->id" />
                            </a>
                            <div class="min-w-0 flex-1">
                                <a href="/{{ $suggestion->username }}" wire:navigate class="font-medium text-gray-900 dark:text-white hover:text-purple-600 dark:hover:text-purple-400 transition-colors block truncate">
                                    {{ $suggestion->name }}
                                </a>
                                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                                    {{ '@' . $suggestion->username }}
                                </p>
                            </div>
                        </div>
                        
                        <flux:button 
                            wire:click="followUser({{ $suggestion->id }})" 
                            variant="primary" 
                            size="sm"
                            class="bg-purple-600 hover:bg-purple-700 text-white border-none flex-shrink-0"
                        >
                            <flux:icon.plus class="w-4 h-4" />
                        </flux:button>
                    </div>

                    {{-- Motivo da Sugestão --}}
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-3">
                        <flux:icon name="{{ $this->getSuggestionIcon($suggestion->suggestion_type) }}" 
                                   class="w-4 h-4 mr-2 {{ $this->getSuggestionColor($suggestion->suggestion_type) }}" />
                        <span>{{ $suggestion->suggestion_reason }}</span>
                    </div>

                    {{-- Informações Adicionais --}}
                    <div class="space-y-2">
                        @if($suggestion->city && $suggestion->suggestion_type === 'location')
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <flux:icon.map-pin class="w-3 h-3 mr-1" />
                                {{ $suggestion->city->name }}
                            </div>
                        @endif

                        @if($suggestion->suggestion_type === 'interests' && $suggestion->hobbies)
                            <div class="flex flex-wrap gap-1">
                                @foreach($suggestion->hobbies->take(3) as $hobby)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        {{ $hobby->nome }}
                                    </span>
                                @endforeach
                                @if($suggestion->hobbies->count() > 3)
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        +{{ $suggestion->hobbies->count() - 3 }} mais
                                    </span>
                                @endif
                            </div>
                        @endif

                        @if($suggestion->suggestion_type === 'groups' && $suggestion->groups)
                            <div class="flex flex-wrap gap-1">
                                @foreach($suggestion->groups->take(2) as $group)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        {{ $group->name }}
                                    </span>
                                @endforeach
                                @if($suggestion->groups->count() > 2)
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        +{{ $suggestion->groups->count() - 2 }} mais
                                    </span>
                                @endif
                            </div>
                        @endif

                        @if($suggestion->suggestion_type === 'activity')
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <flux:icon.clock class="w-3 h-3 mr-1" />
                                Ativo {{ $suggestion->last_seen ? $suggestion->last_seen->diffForHumans() : 'recentemente' }}
                            </div>
                        @endif
                    </div>

                    {{-- Ações Rápidas --}}
                    <div class="flex gap-2 mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                        <flux:button 
                            href="/{{ $suggestion->username }}" 
                            wire:navigate
                            variant="ghost" 
                            size="sm"
                            class="flex-1 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                            <flux:icon.eye class="w-4 h-4 mr-1" />
                            Ver Perfil
                        </flux:button>
                        
                        <flux:button 
                            href="{{ route('caixa_de_mensagens', ['user' => $suggestion->username]) }}"
                            wire:navigate
                            variant="ghost" 
                            size="sm"
                            class="flex-1 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                            <flux:icon.chat-bubble-left class="w-4 h-4 mr-1" />
                            Mensagem
                        </flux:button>
                    </div>
                </div>
            @endforeach
        </div>

        {{-- Botão para Recarregar Sugestões --}}
        <div class="mt-6 text-center">
            <flux:button 
                wire:click="loadSuggestions" 
                variant="ghost"
                class="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
            >
                <flux:icon.arrow-path class="w-4 h-4 mr-2" />
                Atualizar Sugestões
            </flux:button>
        </div>
    @else
        <div class="text-center py-8">
            <flux:icon.users class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Nenhuma sugestão encontrada
            </h4>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                @if($suggestionType === 'location')
                    Complete seu perfil com sua localização para ver sugestões baseadas em proximidade.
                @elseif($suggestionType === 'interests')
                    Adicione seus interesses no perfil para ver sugestões baseadas em hobbies.
                @elseif($suggestionType === 'groups')
                    Participe de grupos para ver sugestões baseadas em comunidades.
                @else
                    Complete seu perfil e interaja mais para receber sugestões personalizadas.
                @endif
            </p>
            <flux:button 
                href="{{ route('settings.profile') }}" 
                wire:navigate
                variant="primary"
                size="sm"
            >
                <flux:icon.pencil class="w-4 h-4 mr-2" />
                Completar Perfil
            </flux:button>
        </div>
    @endif
</div>
