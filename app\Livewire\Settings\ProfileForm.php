<?php

namespace App\Livewire\Settings;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Illuminate\Validation\Rule;

class ProfileForm extends Component
{
    public $name;
    public $email;
    public $username;
    public $sexo;
    public $aniversario;
    public $privado;
    public $bio;
    public $role;

    public function mount()
    {
        $user = Auth::user();
        $this->name = $user->name;
        $this->email = $user->email;
        $this->username = $user->username;
        $this->sexo = $user->sexo;
        $this->aniversario = $user->aniversario;
        $this->privado = $user->privado;
        $this->bio = $user->bio;
        $this->role = $user->role;
    }

    /**
     * Atualizar username em tempo real para converter espaços em underline
     */
    public function updatedUsername($value)
    {
        $this->username = str_replace(' ', '_', $value);
    }

    public function updateProfile()
    {
        $user = Auth::user();

        // Transformar espaços em underline no username
        $this->username = str_replace(' ', '_', $this->username);

        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'username' => [
                'required',
                'string',
                'max:255',
                Rule::unique('users')->ignore($user->id),
                'regex:/^[a-zA-Z0-9_]+$/' // Apenas letras, números e underline
            ],
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'sexo' => ['nullable', 'in:casal,homem,mulher'],
            'aniversario' => ['nullable', 'date'],
            'privado' => ['boolean'],
            'bio' => ['nullable', 'string', 'max:500'],
            'role' => ['nullable', 'string', 'in:admin,visitante,vip'],
        ], [
            'username.regex' => 'O nome de usuário deve conter apenas letras, números e underline (_). Acentos e espaços não são permitidos.',
        ]);

        // Converter string vazia de aniversario para null
        if (isset($validated['aniversario']) && $validated['aniversario'] === '') {
            $validated['aniversario'] = null;
        }

        // Converter string vazia de sexo para null
        if (isset($validated['sexo']) && $validated['sexo'] === '') {
            $validated['sexo'] = null;
        }

        $user->update($validated);

        $this->dispatch('profile-updated', name: $user->name);
    }

    public function render()
    {
        return view('livewire.settings.profile-form');
    }
}
