<?php

namespace App\Livewire\Support;

use App\Models\SupportTicket;
use App\Models\User;
use App\Notifications\NewSupportTicketCreated;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;

class CreateTicketModal extends Component
{
    use WithFileUploads;

    public $showModal = false;
    public $category = '';
    public $priority = 'media';
    public $title = '';
    public $description = '';
    public $attachments = [];
    public $isPremium = false;
    public $premiumPrice = 0;

    protected $listeners = [
        'openCreateTicketModal' => 'openModal',
        'closeCreateTicketModal' => 'closeModal',
    ];

    protected $rules = [
        'category' => 'required|string',
        'priority' => 'required|in:baixa,media,alta,urgente',
        'title' => 'required|string|min:5|max:255',
        'description' => 'required|string|min:10',
        'attachments.*' => 'nullable|file|max:10240|mimes:jpg,jpeg,png,pdf,doc,docx,txt',
    ];

    protected $messages = [
        'category.required' => 'Selecione uma categoria.',
        'title.required' => 'O título é obrigatório.',
        'title.min' => 'O título deve ter pelo menos 5 caracteres.',
        'description.required' => 'A descrição é obrigatória.',
        'description.min' => 'A descrição deve ter pelo menos 10 caracteres.',
        'attachments.*.max' => 'Cada arquivo deve ter no máximo 10MB.',
        'attachments.*.mimes' => 'Tipos de arquivo permitidos: JPG, PNG, PDF, DOC, DOCX, TXT.',
    ];

    public function mount()
    {
        $this->calculatePremiumPrice();
    }

    public function openModal()
    {
        $this->showModal = true;
        $this->calculatePremiumPrice();
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->reset(['category', 'priority', 'title', 'description', 'attachments', 'isPremium']);
        $this->resetValidation();
    }

    public function updatedPriority()
    {
        $this->calculatePremiumPrice();
    }

    public function updatedCategory()
    {
        $this->calculatePremiumPrice();
    }

    public function calculatePremiumPrice()
    {
        $basePrice = 0;
        
        // Preços baseados na prioridade
        $priorityPrices = [
            'baixa' => 0,
            'media' => 5,
            'alta' => 15,
            'urgente' => 30,
        ];

        // Preços baseados na categoria
        $categoryPrices = [
            'conta' => 0,
            'pagamento' => 10,
            'tecnico' => 20,
            'funcionalidade' => 5,
            'loja' => 10,
            'grupos' => 0,
            'mensagens' => 0,
            'perfil' => 0,
            'outros' => 5,
        ];

        $this->premiumPrice = ($priorityPrices[$this->priority] ?? 0) + ($categoryPrices[$this->category] ?? 0);
    }

    public function removeAttachment($index)
    {
        unset($this->attachments[$index]);
        $this->attachments = array_values($this->attachments);
    }

    public function createTicket()
    {
        $this->validate();

        try {
            // Verificar se o usuário tem saldo suficiente para ticket premium
            if ($this->isPremium && $this->premiumPrice > 0) {
                if (auth()->user()->wallet_balance < $this->premiumPrice) {
                    $this->dispatch('notify', [
                        'message' => 'Saldo insuficiente para criar ticket premium. Saldo necessário: R$ ' . number_format($this->premiumPrice, 2, ',', '.'),
                        'type' => 'error'
                    ]);
                    return;
                }
            }

            // Upload attachments
            $uploadedFiles = [];
            if (!empty($this->attachments)) {
                foreach ($this->attachments as $file) {
                    $path = $file->store('support-tickets', 'public');
                    $uploadedFiles[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getMimeType(),
                    ];
                }
            }

            // Create ticket
            $ticket = SupportTicket::create([
                'user_id' => auth()->id(),
                'category' => $this->category,
                'priority' => $this->priority,
                'title' => $this->title,
                'description' => $this->description,
                'attachments' => $uploadedFiles,
            ]);

            // Processar pagamento se for premium
            if ($this->isPremium && $this->premiumPrice > 0) {
                $this->processPayment($ticket);
            }

            // Notify user
            auth()->user()->notify(new NewSupportTicketCreated($ticket));

            // Notify admins
            $admins = User::where('role', 'admin')->get();
            foreach ($admins as $admin) {
                $admin->notify(new NewSupportTicketCreated($ticket));
            }

            $this->dispatch('notify', [
                'message' => "Ticket #{$ticket->ticket_number} criado com sucesso!" . 
                           ($this->isPremium ? " (Premium - R$ " . number_format($this->premiumPrice, 2, ',', '.') . " debitado)" : ""),
                'type' => 'success'
            ]);

            $this->closeModal();

            // Refresh parent component
            $this->dispatch('ticketCreated');

            // Redirect to ticket view
            return redirect()->route('support.tickets.show', $ticket);

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao criar ticket. Tente novamente.',
                'type' => 'error'
            ]);
        }
    }

    private function processPayment($ticket)
    {
        $user = auth()->user();
        
        // Debitar do saldo da carteira
        $user->decrement('wallet_balance', $this->premiumPrice);
        
        // Registrar transação (se houver sistema de transações)
        // WalletTransaction::create([...]);
        
        // Marcar ticket como premium
        $ticket->update([
            'is_premium' => true,
            'premium_amount' => $this->premiumPrice,
        ]);
    }

    public function getCategoriesProperty()
    {
        return [
            'conta' => 'Problemas com Conta',
            'pagamento' => 'Pagamentos e Cobrança',
            'tecnico' => 'Problemas Técnicos',
            'funcionalidade' => 'Dúvidas sobre Funcionalidades',
            'loja' => 'Loja Virtual',
            'grupos' => 'Grupos e Comunidades',
            'mensagens' => 'Sistema de Mensagens',
            'perfil' => 'Perfil e Configurações',
            'outros' => 'Outros Assuntos',
        ];
    }

    public function getPrioritiesProperty()
    {
        return [
            'baixa' => 'Baixa - Dúvida geral',
            'media' => 'Média - Problema que não impede o uso',
            'alta' => 'Alta - Problema que impede funcionalidades',
            'urgente' => 'Urgente - Sistema inacessível',
        ];
    }

    public function render()
    {
        return view('livewire.support.create-ticket-modal');
    }
}
