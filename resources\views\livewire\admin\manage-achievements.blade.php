<div>
    <div class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Formulário de Criação/Edição -->
            <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
                 <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
                    <h3 class="text-lg font-semibold leading-6 text-gray-900 dark:text-white">{{ $is_editing ? 'Editar Conquista' : 'Criar Nova Conquista' }}</h3>
                </div>
                <div class="p-6">

                    <form wire:submit.prevent="{{ $is_editing ? 'updateAchievement' : 'createAchievement' }}">
                        <flux:input type="text" wire:model="name" label="Nome" placeholder="Nome da Conquista" />
                        <flux:textarea wire:model="description" label="Descrição" placeholder="Descrição da Conquista" class="mt-4" />
                        <flux:input type="text" wire:model="icon" label="Ícone" placeholder="Nome ou caminho do ícone" class="mt-4" />
                        <flux:input type="number" wire:model="points" label="Pontos" placeholder="Pontos concedidos" class="mt-4" />
                        <flux:input type="text" wire:model="type" label="Tipo" placeholder="Tipo da Conquista (opcional)" class="mt-4" />

                        <flux:button type="submit" class="mt-6">{{ $is_editing ? 'Atualizar Conquista' : 'Criar Conquista' }}</flux:button>
                        @if ($is_editing)
                            <flux:button type="button" wire:click="resetForm" class="mt-6 ml-2" color="secondary">Cancelar Edição</flux:button>
                        @endif
                    </form>

                    @if (session()->has('message'))
                         <div class="mt-4 p-4 text-sm rounded-md {{ session('message_type', 'success') === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                            {{ session('message') }}
                        </div>
                    @endif

                     @error('name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                     @error('points') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                     @error('type') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>
            </div>

            <!-- Tabela de Conquistas Existentes -->
            <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
                 <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
                    <h3 class="text-lg font-semibold leading-6 text-gray-900 dark:text-white">Lista de Conquistas</h3>
                </div>
                 <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
                            <thead class="bg-gray-50 dark:bg-zinc-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Nome</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Pontos</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Tipo</th>
                                    <th scope="col" class="relative px-6 py-3"><span class="sr-only">Ações</span></th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
                                @forelse ($achievements as $achievement)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $achievement->name }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $achievement->points }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $achievement->type ?? '-' }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <flux:button size="sm" color="secondary" wire:click="editAchievement({{ $achievement->id }})" class="mr-2">Editar</flux:button>
                                            <flux:button size="sm" color="danger" wire:click="deleteAchievement({{ $achievement->id }})" wire:confirm="Tem certeza que deseja deletar esta conquista?">Deletar</flux:button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 text-center">Nenhuma conquista encontrada.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $achievements->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
