<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Notifications\UserUpgradedToVip;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Str;

class UserManager extends Component
{
    use WithPagination;

    public $search = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;
    public $roleFilter = '';
    public $statusFilter = 'active'; // Mostrar apenas usuários ativos por padrão

    // Campos do formulário
    public $userId;
    public $name;
    public $email;
    public $role = 'user';
    public $active = true;

    // Controle de modal
    public $showModal = false;
    public $confirmingDelete = false;
    public $deleteId;
    public $isEditing = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'role' => 'required|in:visitante,vip,admin',
        'active' => 'boolean',
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingRoleFilter()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function createUser()
    {
        $this->resetForm();
        $this->isEditing = false;
        $this->showModal = true;
    }

    public function edit($id)
    {
        $this->resetForm();
        $this->isEditing = true;
        $this->userId = $id;

        $user = User::findOrFail($id);

        $this->name = $user->name;
        $this->email = $user->email;
        $this->role = $user->role;
        $this->active = $user->active;

        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        try {
            if ($this->isEditing) {
                // Atualizar usuário existente
                $user = User::findOrFail($this->userId);

                // Armazenar o papel anterior para detectar mudanças
                $oldRole = $user->role;

                $user->name = $this->name;
                $user->email = $this->email;
                $user->role = $this->role;
                $user->active = $this->active;

                $user->save();

                // Verificar se houve mudança de visitante para VIP
                if ($oldRole === 'visitante' && $this->role === 'vip') {
                    $this->sendVipUpgradeNotification($user);
                }

                $message = 'Usuário atualizado com sucesso!';
            } else {
                // Criar novo usuário
                $existingUser = User::where('email', $this->email)->first();
                if ($existingUser) {
                    $this->addError('email', 'Este email já está em uso.');
                    return false;
                }

                $user = new User();
                $user->name = $this->name;
                $user->email = $this->email;
                $user->role = $this->role;
                $user->active = $this->active;
                $user->password = bcrypt(Str::random(10)); // Senha temporária
                $user->username = Str::slug($this->name) . rand(100, 999);

                $user->save();

                $message = 'Usuário criado com sucesso! Uma senha temporária foi gerada.';
            }

            $this->showModal = false;
            $this->resetForm();

            $this->dispatch('notify', [
                'message' => $message,
                'type' => 'success'
            ]);

            $this->dispatch('saved');

            return true;
        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao salvar usuário: ' . $e->getMessage(),
                'type' => 'error'
            ]);

            return false;
        }
    }

    public function confirmDelete($id)
    {
        $this->confirmingDelete = true;
        $this->deleteId = $id;
    }

    public function delete()
    {
        try {
            $user = User::findOrFail($this->deleteId);

            // Não permitir desativar o próprio usuário
            if ($user->id === auth()->id()) {
                throw new \Exception('Você não pode desativar seu próprio usuário.');
            }

            // Desativar o usuário em vez de excluir
            $user->update([
                'active' => false,
                'email' => $user->email . '_deactivated_' . time(), // Evita conflitos de email
            ]);

            // Opcional: Deslogar o usuário de todas as sessões
            // DB::table('sessions')->where('user_id', $user->id)->delete();

            $this->confirmingDelete = false;
            $this->deleteId = null;

            $this->dispatch('notify', [
                'message' => 'Usuário desativado com sucesso! Todos os dados foram preservados.',
                'type' => 'success'
            ]);

            return true;
        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao desativar usuário: ' . $e->getMessage(),
                'type' => 'error'
            ]);

            return false;
        }
    }

    public function reactivateUser($userId)
    {
        try {
            $user = User::findOrFail($userId);

            // Restaurar email original (remover sufixo de desativação)
            $originalEmail = preg_replace('/_deactivated_\d+$/', '', $user->email);

            // Verificar se o email original já está em uso
            if (User::where('email', $originalEmail)->where('id', '!=', $userId)->exists()) {
                throw new \Exception('Não é possível reativar: email já está em uso por outro usuário.');
            }

            $user->update([
                'active' => true,
                'email' => $originalEmail,
            ]);

            $this->dispatch('notify', [
                'message' => 'Usuário reativado com sucesso!',
                'type' => 'success'
            ]);

            return true;
        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao reativar usuário: ' . $e->getMessage(),
                'type' => 'error'
            ]);

            return false;
        }
    }

    /**
     * Enviar notificação de upgrade para VIP
     */
    private function sendVipUpgradeNotification(User $user)
    {
        try {
            // Enviar email para o usuário
            $user->notify(new UserUpgradedToVip($user));

            // Enviar cópia para administradores
            $adminUsers = User::where('role', 'administrador')->get();
            foreach ($adminUsers as $admin) {
                try {
                    $admin->notify(new UserUpgradedToVip($user, true)); // true indica que é cópia para admin
                } catch (\Exception $e) {
                    // Log do erro mas não interrompe o processo
                    \Log::error('Erro ao enviar cópia de upgrade VIP para admin: ' . $e->getMessage());
                }
            }

            // Enviar cópia para email de contato
            try {
                $contactEmail = '<EMAIL>';
                \Illuminate\Support\Facades\Notification::route('mail', $contactEmail)
                    ->notify(new UserUpgradedToVip($user, true));
            } catch (\Exception $e) {
                \Log::error('Erro ao enviar cópia de upgrade VIP para contato: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            // Log do erro mas não interrompe o processo principal
            \Log::error('Erro ao enviar notificação de upgrade VIP: ' . $e->getMessage());
        }
    }

    public function resetForm()
    {
        $this->userId = null;
        $this->name = '';
        $this->email = '';
        $this->role = 'visitante';
        $this->active = true;

        $this->resetErrorBag();
    }

    public function render()
    {
        $query = User::query();

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhere('username', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->roleFilter) {
            $query->where('role', $this->roleFilter);
        }

        // Filtro de status
        if ($this->statusFilter === 'active') {
            $query->where('active', true);
        } elseif ($this->statusFilter === 'inactive') {
            $query->where('active', false);
        }
        // Se for 'all', não aplica filtro de status

        $query->orderBy($this->sortBy, $this->sortDirection);

        $users = $query->paginate($this->perPage);

        return view('livewire.admin.user-manager', [
            'users' => $users,
        ]);
    }
}
