<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\Group;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SocialConnections extends Component
{
    public User $user;
    public $mutualFollowers = [];
    public $mutualFollowing = [];
    public $commonGroups = [];
    public $suggestedConnections = [];
    public $networkStats = [];
    public $showMutualFollowers = false;
    public $showMutualFollowing = false;
    public $showCommonGroups = false;
    public $showSuggestedConnections = false;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadSocialData();
    }

    public function loadSocialData()
    {
        if (!Auth::check()) {
            return;
        }

        $currentUser = Auth::user();
        
        // Não mostrar dados para o próprio perfil
        if ($currentUser->id === $this->user->id) {
            return;
        }

        $this->loadMutualConnections($currentUser);
        $this->loadCommonGroups($currentUser);
        $this->loadSuggestedConnections($currentUser);
        $this->loadNetworkStats();
    }

    protected function loadMutualConnections($currentUser)
    {
        // Seguidores mútuos (pessoas que seguem ambos os usuários)
        $this->mutualFollowers = $currentUser->followers()
            ->whereIn('follower_id', $this->user->followers()->pluck('follower_id'))
            ->with('userPhotos')
            ->limit(6)
            ->get();

        // Seguindo mútuos (pessoas que ambos seguem)
        $this->mutualFollowing = $currentUser->following()
            ->whereIn('following_id', $this->user->following()->pluck('following_id'))
            ->with('userPhotos')
            ->limit(6)
            ->get();
    }

    protected function loadCommonGroups($currentUser)
    {
        // Grupos em comum
        $currentUserGroups = $currentUser->groups()->where('is_approved', true)->pluck('group_id');
        $this->commonGroups = $this->user->groups()
            ->where('is_approved', true)
            ->whereIn('group_id', $currentUserGroups)
            ->limit(6)
            ->get();
    }

    protected function loadSuggestedConnections($currentUser)
    {
        // Sugestões baseadas em conexões mútuas e grupos em comum
        $mutualFollowerIds = $this->mutualFollowers->pluck('id');
        $commonGroupMemberIds = collect();

        foreach ($this->commonGroups as $group) {
            $groupMembers = $group->members()
                ->where('is_approved', true)
                ->where('user_id', '!=', $currentUser->id)
                ->where('user_id', '!=', $this->user->id)
                ->pluck('user_id');
            $commonGroupMemberIds = $commonGroupMemberIds->merge($groupMembers);
        }

        $suggestedIds = $mutualFollowerIds->merge($commonGroupMemberIds)->unique();

        $this->suggestedConnections = User::whereIn('id', $suggestedIds)
            ->whereNotIn('id', $currentUser->following()->pluck('following_id'))
            ->with('userPhotos')
            ->limit(6)
            ->get();
    }

    protected function loadNetworkStats()
    {
        $this->networkStats = [
            'followers_count' => $this->user->followers()->count(),
            'following_count' => $this->user->following()->count(),
            'groups_count' => $this->user->groups()->where('is_approved', true)->count(),
            'mutual_followers_count' => $this->mutualFollowers->count(),
            'mutual_following_count' => $this->mutualFollowing->count(),
            'common_groups_count' => $this->commonGroups->count(),
        ];
    }

    public function toggleMutualFollowers()
    {
        $this->showMutualFollowers = !$this->showMutualFollowers;
    }

    public function toggleMutualFollowing()
    {
        $this->showMutualFollowing = !$this->showMutualFollowing;
    }

    public function toggleCommonGroups()
    {
        $this->showCommonGroups = !$this->showCommonGroups;
    }

    public function toggleSuggestedConnections()
    {
        $this->showSuggestedConnections = !$this->showSuggestedConnections;
    }

    public function followUser($userId)
    {
        $currentUser = Auth::user();
        $userToFollow = User::find($userId);
        
        if ($userToFollow && !$currentUser->isFollowing($userToFollow)) {
            $currentUser->following()->attach($userId);
            
            // Recarregar sugestões
            $this->loadSuggestedConnections($currentUser);
            
            // Dispatch evento para atualizar outros componentes
            $this->dispatch('user-followed', userId: $userId);
        }
    }

    public function getAvatar($user)
    {
        if ($user->userPhotos && $user->userPhotos->first()) {
            return Storage::url($user->userPhotos->first()->photo_path);
        }
        return asset('images/users/avatar.svg');
    }

    public function render()
    {
        return view('livewire.social-connections');
    }
}
