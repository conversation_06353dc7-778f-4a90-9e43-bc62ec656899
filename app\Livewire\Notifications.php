<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Notification;

class Notifications extends Component
{
    public $unreadCount = 0;
    public $notifications = [];

    public function mount()
    {
        $this->loadNotifications();
    }

    public function loadNotifications()
    {
        // Busca todas as notificações não lidas (sem limite)
        $this->notifications = auth()->user()->notifications()
            ->where('read', false)
            ->whereIn('type', ['like', 'message', 'points', 'mention']) // Incluir notificações de menção
            ->with(['sender.userPhotos' => function($query) {
                $query->latest()->take(1);
            }, 'post'])
            ->latest()
            ->get();

        // Conta o total real de notificações não lidas
        $this->unreadCount = auth()->user()->notifications()
            ->where('read', false)
            ->whereIn('type', ['like', 'message', 'points', 'mention']) // Incluir notificações de menção
            ->count();
    }

    public function markAsRead($notificationId)
    {
        $notification = auth()->user()->notifications()->where('id', $notificationId)->first();
        if ($notification && !$notification->read) {
            $notification->update(['read' => true]);
            $this->loadNotifications();

            // If it's a message notification, redirect to messages page
            if ($notification->type === 'message') {
                return redirect()->route('caixa_de_mensagens');
            }
        }
    }

    public function clearAllNotifications()
    {
        // Marca todas as notificações não lidas como lidas
        auth()->user()->notifications()
            ->where('read', false)
            ->whereIn('type', ['like', 'message', 'points'])
            ->update(['read' => true]);

        // Recarrega as notificações
        $this->loadNotifications();

        // Dispatch evento para mostrar toast de sucesso
        $this->dispatch('notify', [
            'message' => 'Todas as notificações foram marcadas como lidas',
            'type' => 'success'
        ]);
    }

    public function render()
    {
        return view('livewire.notifications');
    }
}
