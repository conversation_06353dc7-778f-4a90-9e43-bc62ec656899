<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Charge;
use Stripe\Exception\CardException;

class PaymentTest extends Component
{
    public $testResults = [];
    public $isRunning = false;

    public function runTests()
    {
        $this->isRunning = true;
        $this->testResults = [];

        // Teste 1: Verificar configurações do Stripe
        $this->testStripeConfig();

        // Teste 2: Verificar notificações
        $this->testNotifications();

        // Teste 3: Verificar logs
        $this->testLogging();

        // Teste 4: Verificar sistema VIP
        $this->testVipSystem();

        $this->isRunning = false;
    }

    private function testStripeConfig()
    {
        try {
            $stripeKey = config('cashier.key');
            $stripeSecret = config('cashier.secret');
            $currency = config('cashier.currency');

            // Verificar tipo de chaves (test vs live)
            $keyType = str_starts_with($stripeKey, 'pk_test_') ? 'test' :
                      (str_starts_with($stripeKey, 'pk_live_') ? 'live' : 'unknown');
            $secretType = str_starts_with($stripeSecret, 'sk_test_') ? 'test' :
                         (str_starts_with($stripeSecret, 'sk_live_') ? 'live' : 'unknown');

            $this->testResults[] = [
                'test' => 'Configuração do Stripe',
                'status' => 'success',
                'message' => "Chaves configuradas. Moeda: {$currency}",
                'details' => [
                    'key_present' => !empty($stripeKey),
                    'secret_present' => !empty($stripeSecret),
                    'currency' => $currency,
                    'key_type' => $keyType,
                    'secret_type' => $secretType,
                    'keys_match' => $keyType === $secretType
                ]
            ];

            // Testar conexão com Stripe
            Stripe::setApiKey($stripeSecret);

            // Tentar fazer uma chamada simples para a API do Stripe
            try {
                $balance = \Stripe\Balance::retrieve();

                $this->testResults[] = [
                    'test' => 'Conexão com Stripe',
                    'status' => 'success',
                    'message' => 'Conexão estabelecida e API respondendo',
                    'details' => [
                        'available_balance' => $balance->available[0]->amount ?? 'N/A',
                        'currency' => $balance->available[0]->currency ?? 'N/A'
                    ]
                ];
            } catch (\Exception $stripeError) {
                $this->testResults[] = [
                    'test' => 'Conexão com Stripe',
                    'status' => 'error',
                    'message' => 'Erro na API: ' . $stripeError->getMessage(),
                    'details' => [
                        'error_type' => get_class($stripeError),
                        'error_code' => method_exists($stripeError, 'getCode') ? $stripeError->getCode() : 'N/A'
                    ]
                ];
            }

        } catch (\Exception $e) {
            $this->testResults[] = [
                'test' => 'Configuração do Stripe',
                'status' => 'error',
                'message' => 'Erro: ' . $e->getMessage(),
                'details' => []
            ];
        }
    }

    private function testNotifications()
    {
        try {
            // Testar notificação de sucesso
            $this->dispatch('notify', [
                'message' => 'Teste de notificação de sucesso',
                'type' => 'success'
            ]);

            // Testar notificação de erro
            $this->dispatch('notify', [
                'message' => 'Teste de notificação de erro',
                'type' => 'error'
            ]);

            $this->testResults[] = [
                'test' => 'Sistema de Notificações',
                'status' => 'success',
                'message' => 'Notificações enviadas com sucesso',
                'details' => []
            ];

        } catch (\Exception $e) {
            $this->testResults[] = [
                'test' => 'Sistema de Notificações',
                'status' => 'error',
                'message' => 'Erro: ' . $e->getMessage(),
                'details' => []
            ];
        }
    }

    private function testLogging()
    {
        try {
            Log::info('Teste de log do sistema de pagamentos', [
                'test' => true,
                'timestamp' => now()
            ]);

            $this->testResults[] = [
                'test' => 'Sistema de Logs',
                'status' => 'success',
                'message' => 'Log registrado com sucesso',
                'details' => []
            ];

        } catch (\Exception $e) {
            $this->testResults[] = [
                'test' => 'Sistema de Logs',
                'status' => 'error',
                'message' => 'Erro: ' . $e->getMessage(),
                'details' => []
            ];
        }
    }

    public function testCardError()
    {
        try {
            // Simular um erro de cartão
            $this->dispatch('notify', [
                'message' => 'Cartão recusado por fundos insuficientes. Verifique o saldo ou use outro cartão.',
                'type' => 'error'
            ]);

            $this->testResults[] = [
                'test' => 'Simulação de Erro de Cartão',
                'status' => 'success',
                'message' => 'Erro simulado com sucesso',
                'details' => []
            ];

        } catch (\Exception $e) {
            $this->testResults[] = [
                'test' => 'Simulação de Erro de Cartão',
                'status' => 'error',
                'message' => 'Erro: ' . $e->getMessage(),
                'details' => []
            ];
        }
    }

    private function testVipSystem()
    {
        try {
            // Verificar se a tabela VIP existe
            $vipSubscriptionsExist = \Schema::hasTable('vip_subscriptions');

            if (!$vipSubscriptionsExist) {
                $this->testResults[] = [
                    'test' => 'Sistema VIP - Tabela',
                    'status' => 'error',
                    'message' => 'Tabela vip_subscriptions não encontrada',
                    'details' => []
                ];
                return;
            }

            // Verificar se o VipSubscriptionService existe
            $serviceExists = class_exists(\App\Services\VipSubscriptionService::class);

            // Verificar se o controller existe
            $controllerExists = class_exists(\App\Http\Controllers\VipSubscriptionController::class);

            // Verificar rotas VIP
            $routesExist = \Route::has('vip.checkout') && \Route::has('vip.payment.success') && \Route::has('vip.payment.cancel');

            $this->testResults[] = [
                'test' => 'Sistema VIP - Componentes',
                'status' => ($serviceExists && $controllerExists && $routesExist) ? 'success' : 'error',
                'message' => 'Verificação dos componentes do sistema VIP',
                'details' => [
                    'vip_table_exists' => $vipSubscriptionsExist,
                    'service_exists' => $serviceExists,
                    'controller_exists' => $controllerExists,
                    'routes_exist' => $routesExist
                ]
            ];

            // Testar criação de sessão de checkout (simulação)
            $this->testResults[] = [
                'test' => 'Sistema VIP - Configuração',
                'status' => 'success',
                'message' => 'Sistema VIP configurado corretamente',
                'details' => [
                    'stripe_key_configured' => !empty(config('cashier.key')),
                    'stripe_secret_configured' => !empty(config('cashier.secret')),
                    'currency' => config('cashier.currency')
                ]
            ];

        } catch (\Exception $e) {
            $this->testResults[] = [
                'test' => 'Sistema VIP',
                'status' => 'error',
                'message' => 'Erro: ' . $e->getMessage(),
                'details' => []
            ];
        }
    }

    public function testVipCheckout()
    {
        try {
            // Simular dados de checkout VIP
            $testData = [
                'plan' => '30',
                'price' => '48'
            ];

            // Log do teste
            Log::info('Teste de checkout VIP iniciado', $testData);

            $this->testResults[] = [
                'test' => 'Simulação de Checkout VIP',
                'status' => 'success',
                'message' => 'Dados de teste preparados para checkout VIP',
                'details' => $testData
            ];

        } catch (\Exception $e) {
            $this->testResults[] = [
                'test' => 'Simulação de Checkout VIP',
                'status' => 'error',
                'message' => 'Erro: ' . $e->getMessage(),
                'details' => []
            ];
        }
    }

    public function clearResults()
    {
        $this->testResults = [];
    }

    public function render()
    {
        return view('livewire.payment-test');
    }
}
