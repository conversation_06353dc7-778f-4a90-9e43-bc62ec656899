<?php

use App\Models\UserCoverPhoto;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Volt\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

new class extends Component {
    public static $name = 'settings.profile-with-cover';
    use WithFileUploads;

    /**
     * The cover photo file upload.
     *
     * @var \Livewire\TemporaryUploadedFile|null
     */
    public $cover = null;

    /**
     * Whether to show the preview interface.
     *
     * @var bool
     */
    public $showPreview = false;

    /**
     * The crop position for the cover.
     *
     * @var string
     */
    public $cropPosition = 'center';

    /**
     * The temporary URL for the uploaded image.
     *
     * @var string|null
     */
    public $tempImageUrl = null;

    /**
     * Processing state for UI feedback.
     *
     * @var bool
     */
    public $processing = false;

    /**
     * Validation rules for the cover photo.
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'cover' => ['required', 'image', 'mimes:jpg,jpeg,png,webp', 'max:10240'], // 10MB max
            'cropPosition' => ['required', 'string', 'in:top,center,bottom']
        ];
    }

    /**
     * Update the user's cover photo.
     *
     * @return void
     */
    public function updateCover(): void
    {
        try {
            $this->processing = true;

            $user = Auth::user();

            if (!$user) {
                throw new \Exception('Authenticated user not found.');
            }

            $this->validate();

            if ($this->cover) {
                Log::info('Iniciando processo de atualização da capa', [
                    'user_id' => $user->id,
                    'crop_position' => $this->cropPosition
                ]);

                // Store the original file
                $originalPath = $this->cover->store('covers/originals', 'public');

                if (!$originalPath) {
                    throw new \Exception('Failed to store the cover photo.');
                }

                Log::info('Arquivo original salvo', [
                    'originalPath' => $originalPath
                ]);

                // Process and create the cover image
                $coverPath = $this->createCoverImage($originalPath);

                if (!$coverPath) {
                    throw new \Exception('Failed to process the cover image.');
                }

                // Verificar se já existe uma capa para este usuário
                $existingCover = UserCoverPhoto::where('user_id', $user->id)->latest()->first();

                if ($existingCover) {
                    // Deletar imagem anterior se existir
                    if ($existingCover->photo_path && Storage::disk('public')->exists($existingCover->photo_path)) {
                        Storage::disk('public')->delete($existingCover->photo_path);
                    }
                    if ($existingCover->cropped_photo_path && Storage::disk('public')->exists($existingCover->cropped_photo_path)) {
                        Storage::disk('public')->delete($existingCover->cropped_photo_path);
                    }

                    // Atualizar a capa existente
                    $existingCover->update([
                        'photo_path' => $originalPath,
                        'cropped_photo_path' => $coverPath,
                        'crop_position' => $this->cropPosition
                    ]);
                } else {
                    // Create new cover photo record
                    UserCoverPhoto::create([
                        'user_id' => $user->id,
                        'photo_path' => $originalPath,
                        'cropped_photo_path' => $coverPath,
                        'crop_position' => $this->cropPosition
                    ]);
                }

                Log::info('Capa atualizada com sucesso', [
                    'user_id' => $user->id,
                    'originalPath' => $originalPath,
                    'coverPath' => $coverPath
                ]);

                $this->reset(['cover', 'showPreview', 'tempImageUrl']);
                $this->dispatch('cover-updated');
                session()->flash('success', 'Foto de capa atualizada com sucesso!');
            }
        } catch (\Throwable $e) {
            Log::error('Error updating cover photo: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            session()->flash('error', 'Ocorreu um erro ao atualizar a capa. Tente novamente.');
        } finally {
            $this->processing = false;
        }
    }

    /**
     * Create a cover image with automatic cropping.
     *
     * @param string $originalPath The path to the original image
     * @return string|null The path to the cover image or null on failure
     */
    protected function createCoverImage(string $originalPath): ?string
    {
        try {
            // Get file paths
            $originalImage = Storage::disk('public')->path($originalPath);
            $coverPath = 'covers/cover_' . time() . '_' . basename($originalPath);
            $coverFullPath = Storage::disk('public')->path($coverPath);

            // Create directory if it doesn't exist
            $directory = dirname($coverFullPath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            Log::info('Criando imagem de capa', [
                'originalPath' => $originalPath,
                'coverPath' => $coverPath,
                'cropPosition' => $this->cropPosition
            ]);

            // Create image manager instance with GD driver
            $manager = new ImageManager(new Driver());

            // Load image
            $image = $manager->read($originalImage);

            $originalWidth = $image->width();
            $originalHeight = $image->height();

            // Define target dimensions for cover (16:9 ratio)
            $targetWidth = 1200;
            $targetHeight = 675; // 1200 / 16 * 9

            // Calculate crop dimensions maintaining aspect ratio
            $sourceRatio = $originalWidth / $originalHeight;
            $targetRatio = $targetWidth / $targetHeight;

            if ($sourceRatio > $targetRatio) {
                // Image is wider than target ratio - crop width
                $cropHeight = $originalHeight;
                $cropWidth = $originalHeight * $targetRatio;
                $cropY = 0;

                // Position based on crop position
                switch ($this->cropPosition) {
                    case 'top':
                        $cropX = 0;
                        break;
                    case 'bottom':
                        $cropX = $originalWidth - $cropWidth;
                        break;
                    case 'center':
                    default:
                        $cropX = ($originalWidth - $cropWidth) / 2;
                        break;
                }
            } else {
                // Image is taller than target ratio - crop height
                $cropWidth = $originalWidth;
                $cropHeight = $originalWidth / $targetRatio;
                $cropX = 0;

                // Position based on crop position
                switch ($this->cropPosition) {
                    case 'top':
                        $cropY = 0;
                        break;
                    case 'bottom':
                        $cropY = $originalHeight - $cropHeight;
                        break;
                    case 'center':
                    default:
                        $cropY = ($originalHeight - $cropHeight) / 2;
                        break;
                }
            }

            Log::info('Dimensões de recorte calculadas', [
                'original' => [$originalWidth, $originalHeight],
                'crop' => [$cropX, $cropY, $cropWidth, $cropHeight],
                'target' => [$targetWidth, $targetHeight],
                'position' => $this->cropPosition
            ]);

            // Crop the image
            $image->crop(
                (int)$cropWidth,
                (int)$cropHeight,
                (int)$cropX,
                (int)$cropY
            );

            // Resize to target dimensions
            $image->resize($targetWidth, $targetHeight);

            // Optimize quality
            $image->save($coverFullPath, quality: 85);

            Log::info('Imagem de capa criada com sucesso', [
                'path' => $coverPath,
                'dimensions' => [$targetWidth, $targetHeight]
            ]);

            return $coverPath;
        } catch (\Throwable $e) {
            Log::error('Error creating cover image: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

    /**
     * Handle file upload and prepare for preview.
     *
     * @return void
     */
    public function updatedCover(): void
    {
        if ($this->cover) {
            try {
                // Validar o arquivo antes de processar
                $this->validate([
                    'cover' => ['required', 'image', 'mimes:jpg,jpeg,png,webp', 'max:10240']
                ]);

                $this->tempImageUrl = $this->cover->temporaryUrl();
                $this->showPreview = true;

                Log::info('Imagem de capa carregada para preview', [
                    'tempUrl' => $this->tempImageUrl
                ]);
            } catch (\Exception $e) {
                Log::error('Erro ao processar upload da capa: ' . $e->getMessage());
                $this->reset(['cover', 'showPreview', 'tempImageUrl']);
                session()->flash('error', 'Erro ao carregar a imagem. Verifique o formato e tamanho do arquivo.');
            }
        }
    }

    /**
     * Cancel upload.
     *
     * @return void
     */
    public function cancelUpload(): void
    {
        $this->reset(['cover', 'showPreview', 'tempImageUrl']);
    }
}; ?>

<section class="w-full profile-with-cover">
    <style>
        /* Container de preview da imagem */
        .preview-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            height: 450px;
            background-color: #f3f4f6;
            border-radius: 0.5rem;
            overflow: hidden;
            margin: 0 auto;
            border: 2px dashed #d1d5db;
        }

        .preview-container.has-image {
            border: 2px solid #3b82f6;
        }

        .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .preview-image.position-top {
            object-position: top;
        }

        .preview-image.position-bottom {
            object-position: bottom;
        }

        .preview-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .preview-container:hover .preview-overlay {
            opacity: 1;
        }

        .dark .preview-container {
            background-color: #1f2937;
            border-color: #374151;
        }

        .dark .preview-container.has-image {
            border-color: #60a5fa;
        }

        /* Responsivo */
        @media (max-width: 768px) {
            .preview-container {
                height: 300px;
            }
        }

        @media (max-width: 480px) {
            .preview-container {
                height: 250px;
            }
        }
    </style>

    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Cover')" :subheading="__('Update your cover photo')">
        <form wire:submit.prevent="updateCover" class="my-6 w-full space-y-6">
            <div>
                <!-- File upload component -->
                <x-file-upload
                    wire:model="cover"
                    label="Foto de Capa"
                    accept="image/png, image/jpeg, image/webp"
                    icon="photo"
                    :iconVariant="$cover ? 'solid' : 'outline'"
                    help="Selecione uma imagem para usar como capa do seu perfil. Tamanho máximo: 10MB. A imagem será automaticamente redimensionada para 1200x675px (16:9)."
                />

                @error('cover')
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror

                <!-- Preview interface -->
                @if($showPreview && $tempImageUrl)
                    <div class="mt-4">
                        <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            Preview da sua capa (1200x675px - proporção 16:9)
                        </div>

                        <!-- Preview container -->
                        <div class="preview-container has-image mb-4">
                            <img
                                src="{{ $tempImageUrl }}"
                                alt="Preview da capa"
                                class="preview-image position-{{ $cropPosition }}"
                            />
                            <div class="preview-overlay">
                                <div class="text-white text-center">
                                    <x-flux::icon name="eye" class="w-8 h-8 mx-auto mb-2" />
                                    <p class="text-sm">Preview da capa</p>
                                </div>
                            </div>
                        </div>

                        <!-- Crop position controls -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Posição do recorte:
                            </label>
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input
                                        type="radio"
                                        wire:model.live="cropPosition"
                                        value="top"
                                        class="mr-2 text-blue-600 focus:ring-blue-500"
                                    />
                                    <span class="text-sm">Topo</span>
                                </label>
                                <label class="flex items-center">
                                    <input
                                        type="radio"
                                        wire:model.live="cropPosition"
                                        value="center"
                                        class="mr-2 text-blue-600 focus:ring-blue-500"
                                    />
                                    <span class="text-sm">Centro</span>
                                </label>
                                <label class="flex items-center">
                                    <input
                                        type="radio"
                                        wire:model.live="cropPosition"
                                        value="bottom"
                                        class="mr-2 text-blue-600 focus:ring-blue-500"
                                    />
                                    <span class="text-sm">Base</span>
                                </label>
                            </div>
                        </div>

                        <!-- Instructions -->
                        <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                <strong>Como funciona:</strong> Sua imagem será automaticamente redimensionada para 1200x675px (proporção 16:9).
                                Use as opções acima para escolher qual parte da imagem será mantida no recorte.
                            </p>
                        </div>

                        <!-- Action buttons -->
                        <div class="flex items-center gap-2">
                            <x-flux::button
                                type="button"
                                variant="ghost"
                                wire:click="cancelUpload"
                                class="flex-1"
                                :disabled="$processing"
                            >
                                Cancelar
                            </x-flux::button>

                            <x-flux::button
                                type="submit"
                                variant="primary"
                                class="flex-1"
                                :disabled="$processing"
                                wire:loading.attr="disabled"
                                wire:target="updateCover"
                            >
                                <span wire:loading.remove wire:target="updateCover">
                                    Salvar Capa
                                </span>
                                <span wire:loading wire:target="updateCover">
                                    Salvando...
                                </span>
                            </x-flux::button>
                        </div>
                    </div>
                @elseif (auth()->user() && auth()->user()->userCoverPhotos()->latest()->first())
                    @php
                        $coverPhoto = auth()->user()->userCoverPhotos()->latest()->first();
                        $photoPath = $coverPhoto->cropped_photo_path ?? $coverPhoto->photo_path;
                    @endphp

                    <div class="mt-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            Foto de capa atual
                        </p>
                        <div class="preview-container has-image">
                            <img
                                src="{{ Storage::url($photoPath) }}"
                                alt="Capa atual"
                                class="preview-image"
                            />
                        </div>
                    </div>
                @endif
            </div>

            <!-- Action buttons and messages (only show when not in preview mode) -->
            @if(!$showPreview)
            <div class="flex items-center gap-4">
                <div class="flex items-center justify-end w-full">
                    <x-flux::button
                        variant="primary"
                        type="submit"
                        class="w-full"
                        :disabled="!$cover || $processing"
                        wire:loading.attr="disabled"
                        wire:target="updateCover"
                    >
                        <span wire:loading.remove wire:target="updateCover">
                            Salvar Capa
                        </span>
                        <span wire:loading wire:target="updateCover">
                            Salvando...
                        </span>
                    </x-flux::button>
                </div>
            </div>
            @endif

            <!-- Messages -->
            <div class="mt-4">
                <x-action-message class="me-3" on="cover-updated">
                    Foto de capa atualizada com sucesso.
                </x-action-message>

                @if (session('success'))
                    <p class="mt-2 font-medium text-green-600 dark:text-green-400">
                        {{ session('success') }}
                    </p>
                @endif

                @if (session('error'))
                    <p class="mt-2 font-medium text-red-600 dark:text-red-400">
                        {{ session('error') }}
                    </p>
                @endif
            </div>
        </form>
    </x-settings.layout>
</section>

