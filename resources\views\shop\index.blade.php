<x-layouts.app :title="__('Loja')">
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-title mb-6">Loja</h1>

    @if (session('success'))
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p>{{ session('success') }}</p>
        </div>
    @endif

    @if($products->isEmpty())
        <div class="text-center py-16">
            <p class="text-body-light">Nenhum produto encontrado no momento.</p>
        </div>
    @else
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            @foreach($products as $product)
                <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <a href="{{ route('shop.product.show', $product->slug) }}">
                        <img class="h-48 w-full object-cover" src="{{ $product->getImageUrl() ?? 'https://placehold.co/600x400/e2e8f0/1e293b?text=Sem+Imagem' }}" alt="{{ $product->name }}">
                    </a>
                    <div class="p-4">
                        <h2 class="text-lg font-semibold text-title truncate">{{ $product->name }}</h2>
                        <p class="text-body-light mt-1">{{ Str::limit($product->description, 50) }}</p>
                        <div class="mt-4 flex items-center justify-between">
                            <span class="text-xl font-bold text-title">R$ {{ number_format($product->price, 2, ',', '.') }}</span>
                            <a href="{{ route('shop.product.show', $product->slug) }}" class="text-white bg-blue-600 hover:bg-blue-700 font-semibold py-2 px-4 rounded-md text-sm">
                                Ver Detalhes
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="mt-8">
            {{ $products->links() }}
        </div>
    @endif
</div>
</x-layouts.app>