<?php

namespace App\Livewire\Support;

use App\Models\HelpArticle;
use Livewire\Component;

class ArticleFeedback extends Component
{
    public HelpArticle $article;
    public $userFeedback = null; // 'helpful', 'not_helpful', or null

    public function mount(HelpArticle $article)
    {
        $this->article = $article;
        
        // Check if user already gave feedback (you might want to store this in a separate table)
        // For now, we'll use session to prevent multiple votes per session
        $this->userFeedback = session("article_feedback_{$article->id}");
    }

    public function markAsHelpful()
    {
        if ($this->userFeedback) {
            $this->dispatch('notify', [
                'message' => 'Você já avaliou este artigo.',
                'type' => 'info'
            ]);
            return;
        }

        $this->article->markAsHelpful();
        $this->userFeedback = 'helpful';
        session(["article_feedback_{$this->article->id}" => 'helpful']);

        $this->dispatch('notify', [
            'message' => 'Obrigado pelo feedback! Isso nos ajuda a melhorar.',
            'type' => 'success'
        ]);

        $this->article->refresh();
    }

    public function markAsNotHelpful()
    {
        if ($this->userFeedback) {
            $this->dispatch('notify', [
                'message' => 'Você já avaliou este artigo.',
                'type' => 'info'
            ]);
            return;
        }

        $this->article->markAsNotHelpful();
        $this->userFeedback = 'not_helpful';
        session(["article_feedback_{$this->article->id}" => 'not_helpful']);

        $this->dispatch('notify', [
            'message' => 'Obrigado pelo feedback! Vamos trabalhar para melhorar este conteúdo.',
            'type' => 'info'
        ]);

        $this->article->refresh();
    }

    public function render()
    {
        return view('livewire.support.article-feedback');
    }
}
