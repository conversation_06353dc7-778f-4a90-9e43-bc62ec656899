# Correção do Erro de Exclusão de Produtos

## Problema
Erro ao tentar excluir produtos que já foram vendidos:
```
SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`desiree`.`order_items`, CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`))
```

## Causa
O erro ocorre porque existe uma restrição de chave estrangeira na tabela `order_items` que impede a exclusão de produtos que já foram vendidos. Isso é uma medida de segurança para preservar o histórico de pedidos.

## Solução Implementada

### 1. Verificações de Integridade
O método `delete()` no `ProductManager` agora verifica:
- ✅ Se o produto já foi vendido (tem registros em `order_items`)
- ✅ Se o produto está em carrinhos de compras
- ✅ Se o produto está em listas de desejos

### 2. Mensagens de Erro Melhoradas
- Mensagens específicas para cada tipo de restrição
- Sugestão de desativar o produto em vez de excluí-lo

### 3. Alternativa: Desativar Produto
- Novo botão "Desativar/Ativar" no modal de exclusão
- Permite manter o produto no sistema mas torná-lo indisponível
- Preserva o histórico de vendas

### 4. Modal Melhorado
- Interface mais informativa
- Avisos sobre as restrições
- Opções claras para o usuário

## Arquivos Modificados

### `app/Livewire/Admin/ProductManager.php`
- Método `delete()` com verificações de integridade
- Novo método `toggleProductStatus()` para ativar/desativar produtos
- Tratamento de erros melhorado

### `resources/views/livewire/admin/product-manager.blade.php`
- Modal de exclusão redesenhado
- Botão dinâmico para ativar/desativar
- Avisos informativos

## Para Produção (KingHost)

### Opção 1: Usar o Sistema (Recomendado)
1. Acesse o gerenciador de produtos
2. Em vez de excluir, use o botão "Desativar"
3. Produtos desativados não aparecerão na loja mas preservarão o histórico

### Opção 2: SQL Manual (Avançado)
Execute o arquivo `database/sql/fix_product_deletion_constraints.sql` no phpMyAdmin para:
- Verificar quais produtos têm restrições
- Desativar produtos em lote
- Limpar produtos órfãos (sem vendas)

## Comandos SQL Úteis

### Verificar produtos que não podem ser excluídos:
```sql
SELECT p.id, p.name, COUNT(oi.id) as vendas
FROM products p
INNER JOIN order_items oi ON p.id = oi.product_id
GROUP BY p.id, p.name;
```

### Desativar produtos vendidos:
```sql
UPDATE products 
SET status = 'inactive' 
WHERE id IN (
    SELECT DISTINCT product_id 
    FROM order_items
);
```

### Encontrar produtos que podem ser excluídos:
```sql
SELECT p.id, p.name
FROM products p
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN cart_items ci ON p.id = ci.product_id
LEFT JOIN wishlists w ON p.id = w.product_id
WHERE oi.id IS NULL 
AND ci.id IS NULL 
AND w.id IS NULL;
```

## Boas Práticas

### ✅ Recomendado
- Desativar produtos em vez de excluí-los
- Manter histórico de vendas intacto
- Usar filtros para ocultar produtos inativos na loja

### ❌ Evitar
- Excluir produtos que já foram vendidos
- Modificar restrições de chave estrangeira sem backup
- Perder histórico de pedidos

## Benefícios da Solução

1. **Preservação de Dados**: Histórico de vendas mantido
2. **Flexibilidade**: Produtos podem ser reativados
3. **Segurança**: Evita perda acidental de dados
4. **Usabilidade**: Interface clara e intuitiva
5. **Conformidade**: Mantém integridade referencial

## Monitoramento

Para monitorar produtos inativos:
```sql
SELECT COUNT(*) as produtos_inativos 
FROM products 
WHERE status = 'inactive';
```

Para reativar produtos específicos:
```sql
UPDATE products 
SET status = 'active' 
WHERE id IN (1, 2, 3); -- IDs dos produtos
```
