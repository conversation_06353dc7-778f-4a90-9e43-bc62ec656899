<?php

namespace Tests\Feature;

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EventRegistrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'email_verified_at' => now(),
        ]);
        
        // Create a test event
        $this->event = Event::factory()->create([
            'name' => 'Test Event',
            'date' => now()->addDays(7),
            'start_time' => now()->addDays(7)->setTime(20, 0),
            'price' => 0, // Free event
            'is_active' => true,
            'capacity' => 100,
        ]);
    }

    public function test_user_can_register_for_free_event()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        $response->assertRedirect(route('events.show', $this->event->slug));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('event_attendees', [
            'event_id' => $this->event->id,
            'user_id' => $this->user->id,
            'status' => 'confirmed',
            'payment_status' => 'completed',
        ]);
    }

    public function test_user_cannot_register_twice_for_same_event()
    {
        $this->actingAs($this->user);

        // First registration
        EventAttendee::create([
            'event_id' => $this->event->id,
            'user_id' => $this->user->id,
            'status' => 'confirmed',
            'payment_status' => 'completed',
            'ticket_code' => 'TEST123',
        ]);

        // Try to register again
        $response = $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        $response->assertRedirect(route('events.show', $this->event->slug));
        $response->assertSessionHas('info');

        // Should still have only one registration
        $this->assertEquals(1, EventAttendee::where('event_id', $this->event->id)
            ->where('user_id', $this->user->id)
            ->where('status', '!=', 'cancelled')
            ->count());
    }

    public function test_user_cannot_register_for_inactive_event()
    {
        $this->actingAs($this->user);
        
        $this->event->update(['is_active' => false]);

        $response = $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        $response->assertRedirect(route('events.show', $this->event->slug));
        $response->assertSessionHas('error');

        $this->assertDatabaseMissing('event_attendees', [
            'event_id' => $this->event->id,
            'user_id' => $this->user->id,
        ]);
    }

    public function test_user_cannot_register_for_past_event()
    {
        $this->actingAs($this->user);
        
        $this->event->update([
            'date' => now()->subDays(1),
            'start_time' => now()->subDays(1)->setTime(20, 0),
        ]);

        $response = $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        $response->assertRedirect(route('events.show', $this->event->slug));
        $response->assertSessionHas('error');

        $this->assertDatabaseMissing('event_attendees', [
            'event_id' => $this->event->id,
            'user_id' => $this->user->id,
        ]);
    }

    public function test_user_cannot_register_for_sold_out_event()
    {
        $this->actingAs($this->user);
        
        $this->event->update(['capacity' => 1]);
        
        // Fill the capacity
        EventAttendee::create([
            'event_id' => $this->event->id,
            'user_id' => User::factory()->create()->id,
            'status' => 'confirmed',
            'payment_status' => 'completed',
            'ticket_code' => 'TEST123',
        ]);

        $response = $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        $response->assertRedirect(route('events.show', $this->event->slug));
        $response->assertSessionHas('error');

        $this->assertDatabaseMissing('event_attendees', [
            'event_id' => $this->event->id,
            'user_id' => $this->user->id,
        ]);
    }

    public function test_paid_event_creates_pending_registration()
    {
        $this->actingAs($this->user);
        
        $this->event->update(['price' => 50.00]);

        // Mock Stripe to avoid actual API calls
        $this->mock(\Stripe\Checkout\Session::class, function ($mock) {
            $mock->shouldReceive('create')->andReturn((object)[
                'id' => 'cs_test_123',
                'url' => 'https://checkout.stripe.com/test'
            ]);
        });

        $response = $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        // Should redirect to Stripe checkout
        $response->assertRedirect('https://checkout.stripe.com/test');

        $this->assertDatabaseHas('event_attendees', [
            'event_id' => $this->event->id,
            'user_id' => $this->user->id,
            'status' => 'registered',
            'payment_status' => 'pending',
        ]);
    }

    public function test_guest_cannot_register_for_event()
    {
        $response = $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        $response->assertRedirect(route('login'));
    }

    public function test_ticket_code_is_generated_for_free_event()
    {
        $this->actingAs($this->user);

        $this->post(route('events.register', $this->event), [
            'confirm' => true
        ]);

        $attendee = EventAttendee::where('event_id', $this->event->id)
            ->where('user_id', $this->user->id)
            ->first();

        $this->assertNotNull($attendee->ticket_code);
        $this->assertMatchesRegularExpression('/^[A-Z0-9]{8}$/', $attendee->ticket_code);
    }
}
