<?php

namespace App\Http\Controllers;

use App\Models\Hashtag;
use Illuminate\Http\Request;
use App\Services\ContentProcessor; // For processing content for display

class HashtagPageController extends Controller
{
    /**
     * Display the specified hashtag page.
     *
     * @param  \App\Models\Hashtag  $hashtag
     * @return \Illuminate\View\View
     */
    public function show(Hashtag $hashtag)
    {
        // Eager load posts and their necessary relations for display
        // Assuming only public posts or posts accessible to the current user should be shown.
        // Add any privacy checks if necessary.
        $posts = $hashtag->posts() // This uses the morphedByMany relationship
            ->with([
                'user:id,name,username', // Select specific columns for user
                'user.userPhotos', // For avatar
                'comments.user:id,name,username', // For comment authors
                'comments.user.userPhotos',
                'likedByUsers:id' // For like status
            ])
            ->where(function ($query) {
                // Example: Filter out posts from private groups if the user is not a member
                // This part needs to be adapted based on your group privacy rules
                // $query->whereNull('group_id') // Posts not in any group
                //    ->orWhereHas('group', function ($groupQuery) {
                //        $groupQuery->where('privacy', 'public')
                //            ->orWhere(function($q) {
                //                // Add logic for private groups if user is member
                //            });
                //    });
                // For now, let's assume all posts linked to a hashtag are considered "public" for this page
                // or that the Post model's global scopes handle privacy.
            })
            ->latest()
            ->paginate(15); // Or your preferred pagination number

        // Process content for display
        $posts->getCollection()->transform(function ($post) {
            $post->processed_content = ContentProcessor::processContent($post->content ?? '');
            // Optionally process comments here too if you display them directly on this page
            // and not through a Livewire component that handles its own processing.
            $post->comments->transform(function ($comment) {
                $comment->processed_body = ContentProcessor::processContent($comment->body ?? '');
                return $comment;
            });
            return $post;
        });

        return view('hashtag.show', [
            'hashtag' => $hashtag,
            'posts' => $posts,
        ]);
    }
}