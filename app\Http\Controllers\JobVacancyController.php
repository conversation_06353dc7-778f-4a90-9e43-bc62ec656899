<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\JobVacancy;
use App\Models\JobCategory;

class JobVacancyController extends Controller
{
    /**
     * Exibe a lista de vagas.
     */
    public function index(Request $request)
    {
        $query = JobVacancy::with('category')
            ->active()
            ->validDeadline()
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc');

        // Filtros
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        if ($request->filled('contract_type')) {
            $query->byContractType($request->contract_type);
        }

        if ($request->filled('work_mode')) {
            $query->byWorkMode($request->work_mode);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%")
                    ->orWhereHas('category', function ($categoryQuery) use ($search) {
                        $categoryQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        $job_vacancies = $query->paginate(12);
        $categories = JobCategory::active()->ordered()->get();

        return view('job_vacancies.index', compact('job_vacancies', 'categories'));
    }

    /**
     * Exibe detalhes de uma vaga específica.
     */
    public function show($slug)
    {
        $job = JobVacancy::with(['category', 'applications.user'])
            ->where('slug', $slug)
            ->active()
            ->firstOrFail();

        // Incrementar contador de visualizações
        $job->incrementViews();

        // Verificar se o usuário já se candidatou
        $userApplication = null;
        $canApply = true;
        if (auth()->check()) {
            $userApplication = $job->getUserApplication(auth()->id());
            $canApply = !$userApplication && $job->isAcceptingApplications();
        }

        // Vagas relacionadas
        $relatedJobs = JobVacancy::with('category')
            ->where('category_id', $job->category_id)
            ->where('id', '!=', $job->id)
            ->active()
            ->validDeadline()
            ->limit(3)
            ->get();

        return view('job_vacancies.show', compact('job', 'userApplication', 'canApply', 'relatedJobs'));
    }

    /**
     * Cria uma nova vaga.
     */
    public function store(Request $request)
    {
        $data = $request->all();

        // Corrige campos numéricos vazios
        $data['salary_min'] = ($data['salary_min'] ?? '') === '' ? null : $data['salary_min'];
        $data['salary_max'] = ($data['salary_max'] ?? '') === '' ? null : $data['salary_max'];

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'nullable|string',
            'benefits' => 'nullable|string',
            'category_id' => 'required|exists:job_categories,id',
            'contract_type' => 'required|string',
            'work_mode' => 'required|string',
            'location' => 'nullable|string',
            'salary_min' => 'nullable|numeric',
            'salary_max' => 'nullable|numeric',
            'salary_period' => 'required|string',
            'salary_negotiable' => 'boolean',
            'vacancies' => 'required|integer|min:1',
            'application_deadline' => 'required|date',
            'experience_level' => 'required|string',
            'requires_resume' => 'boolean',
            'requires_cover_letter' => 'boolean',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ], [
            'title.required' => 'O título da vaga é obrigatório.',
            'description.required' => 'A descrição da vaga é obrigatória.',
            'category_id.required' => 'Selecione uma categoria.',
            'category_id.exists' => 'Categoria inválida.',
            'contract_type.required' => 'Selecione o tipo de contrato.',
            'work_mode.required' => 'Selecione a modalidade.',
            'salary_min.numeric' => 'O salário mínimo deve ser um número.',
            'salary_max.numeric' => 'O salário máximo deve ser um número.',
            'vacancies.required' => 'Informe o número de vagas.',
            'vacancies.integer' => 'O número de vagas deve ser um número inteiro.',
            'vacancies.min' => 'Deve haver pelo menos uma vaga.',
            'application_deadline.required' => 'Informe a data limite para candidatura.',
            'application_deadline.date' => 'Data limite inválida.',
            'experience_level.required' => 'Informe o nível de experiência.',
        ]);

        // ...criação da vaga...
    }

    /**
     * Atualiza uma vaga existente.
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();

        // Corrige campos numéricos vazios
        $data['salary_min'] = ($data['salary_min'] ?? '') === '' ? null : $data['salary_min'];
        $data['salary_max'] = ($data['salary_max'] ?? '') === '' ? null : $data['salary_max'];

        // ...validação e atualização...
        // $validated = $request->validate([...]);
        // $job = JobVacancy::findOrFail($id);
        // $job->update($validated);

        // ...existing code...
    }
}
