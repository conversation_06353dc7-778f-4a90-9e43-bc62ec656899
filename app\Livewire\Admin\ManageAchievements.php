<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use App\Models\Achievement;
use Livewire\WithPagination;

class ManageAchievements extends Component
{
    use WithPagination;

    public $name, $description, $icon, $points, $type;
    public $achievement_id = null;
    public $is_editing = false;

    protected $rules = [
        'name' => 'required|unique:achievements,name',
        'description' => 'nullable',
        'icon' => 'nullable',
        'points' => 'required|integer|min:0',
        'type' => 'nullable|string',
    ];

    public function render()
    {
        $achievements = Achievement::paginate(10);
        return view('livewire.admin.manage-achievements', [
            'achievements' => $achievements,
        ]);
    }

    public function createAchievement()
    {
        $this->validate();

        Achievement::create([
            'name' => $this->name,
            'description' => $this->description,
            'icon' => $this->icon,
            'points' => $this->points,
            'type' => $this->type,
        ]);

        session()->flash('message', 'Conquista criada com sucesso!');

        $this->resetForm();
    }

    public function editAchievement($id)
    {
        $achievement = Achievement::findOrFail($id);

        $this->achievement_id = $achievement->id;
        $this->name = $achievement->name;
        $this->description = $achievement->description;
        $this->icon = $achievement->icon;
        $this->points = $achievement->points;
        $this->type = $achievement->type;
        $this->is_editing = true;

        $this->rules['name'] = 'required|unique:achievements,name,' . $this->achievement_id;
    }

    public function updateAchievement()
    {
        $this->validate();

        $achievement = Achievement::findOrFail($this->achievement_id);

        $achievement->update([
            'name' => $this->name,
            'description' => $this->description,
            'icon' => $this->icon,
            'points' => $this->points,
            'type' => $this->type,
        ]);

        session()->flash('message', 'Conquista atualizada com sucesso!');

        $this->resetForm();
    }

    public function deleteAchievement($id)
    {
        Achievement::destroy($id);
        session()->flash('message', 'Conquista deletada com sucesso!');
    }

    public function resetForm()
    {
        $this->name = '';
        $this->description = '';
        $this->icon = '';
        $this->points = 0;
        $this->type = '';
        $this->achievement_id = null;
        $this->is_editing = false;

        $this->rules['name'] = 'required|unique:achievements,name';
    }
}
