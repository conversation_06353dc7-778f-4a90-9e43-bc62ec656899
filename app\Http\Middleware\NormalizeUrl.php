<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class NormalizeUrl
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verificar se a requisição tem www e redirecionar para versão sem www
        $host = $request->getHost();
        
        if (str_starts_with($host, 'www.')) {
            $newHost = str_replace('www.', '', $host);
            $newUrl = $request->getScheme() . '://' . $newHost . $request->getRequestUri();
            
            return redirect($newUrl, 301);
        }
        
        return $next($request);
    }
} 