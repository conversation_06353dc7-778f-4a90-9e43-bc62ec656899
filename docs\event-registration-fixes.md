# Correções para Inscrições de Eventos

Este documento descreve as correções implementadas para resolver problemas com inscrições de eventos no sistema.

## Problemas Identificados e Corrigidos

### 1. Inconsistência entre Controller e Livewire
**Problema**: Havia duas implementações diferentes para inscrições (Controller e Livewire) com lógicas ligeiramente diferentes.

**Solução**: 
- Padronizou a lógica de validação em ambos
- Melhorou o tratamento de erros
- Adicionou logs para debugging

### 2. Query SQL Incorreta
**Problema**: Query usava `event_attendees.status` em vez de apenas `status`.

**Solução**: Corrigido para usar `status` diretamente.

### 3. Falta de Validação de Session ID do Stripe
**Problema**: Não havia verificação se o session_id retornado pelo Stripe era válido.

**Solução**: 
- Adicionada verificação do session_id
- Validação de metadata do Stripe
- Prevenção de pagamentos duplicados

### 4. Tratamento de Erros Inadequado
**Problema**: Erros não eram logados adequadamente e mensagens genéricas eram exibidas.

**Solução**:
- Adicionados logs detalhados
- Mensagens de erro mais específicas
- Cleanup automático de registros órfãos

## Arquivos Modificados

### Controllers
- `app/Http/Controllers/EventAttendeeController.php`
  - Melhorado tratamento de erros
  - Adicionada validação de session_id
  - Logs detalhados

### Livewire Components
- `app/Livewire/Events/EventRegistration.php`
  - Corrigida query SQL
  - Melhorado tratamento de erros
  - Consistência com Controller

### Rotas
- `routes/web.php`
  - Corrigidas rotas de sucesso/cancelamento
  - Adicionado middleware de verificação de tempo

### Middleware
- `app/Http/Middleware/CheckEventRegistrationTime.php` (novo)
  - Verifica se usuário visitante tem tempo suficiente para completar inscrição

## Comandos Artisan

### Comando de Correção
```bash
php artisan events:fix-registrations
```

Executa verificações e correções automáticas:
- Corrige status de pagamento inconsistentes
- Gera códigos de ingresso faltantes
- Remove registros duplicados
- Limpa registros órfãos
- Desativa eventos passados

### Modo Dry Run
```bash
php artisan events:fix-registrations --dry-run
```

Mostra o que seria corrigido sem fazer alterações.

## Scripts SQL

### Para Produção (phpMyAdmin)
Execute o arquivo `database/sql/fix_event_registrations.sql` no phpMyAdmin para:
- Corrigir estrutura da tabela
- Adicionar índices
- Limpar dados inconsistentes
- Verificar integridade

## Testes

### Executar Testes
```bash
php artisan test tests/Feature/EventRegistrationTest.php
```

Os testes cobrem:
- Inscrição em eventos gratuitos
- Prevenção de inscrições duplicadas
- Validação de eventos inativos/passados/esgotados
- Geração de códigos de ingresso
- Redirecionamento para pagamento

## Monitoramento

### Logs
Os logs são salvos em `storage/logs/laravel.log` com prefixos:
- `Erro ao criar inscrição gratuita:`
- `Erro ao criar sessão Stripe:`
- `Erro ao processar sucesso do pagamento:`

### Verificações Manuais

#### Verificar Eventos Ativos
```sql
SELECT id, name, date, is_active, price, capacity 
FROM events 
WHERE is_active = 1 
ORDER BY date ASC;
```

#### Verificar Inscrições por Evento
```sql
SELECT 
    e.name as evento,
    COUNT(ea.id) as total_inscricoes,
    COUNT(CASE WHEN ea.status = 'confirmed' THEN 1 END) as confirmadas,
    COUNT(CASE WHEN ea.payment_status = 'completed' THEN 1 END) as pagas
FROM events e
LEFT JOIN event_attendees ea ON e.id = ea.event_id
WHERE e.is_active = 1
GROUP BY e.id, e.name
ORDER BY e.date ASC;
```

#### Verificar Registros com Problemas
```sql
SELECT 
    ea.id,
    ea.status,
    ea.payment_status,
    ea.ticket_code,
    e.name as evento_nome
FROM event_attendees ea
JOIN events e ON ea.event_id = e.id
WHERE 
    (ea.payment_status = 'completed' AND ea.status != 'confirmed') OR
    (ea.ticket_code IS NULL OR ea.ticket_code = '')
ORDER BY ea.created_at DESC;
```

## Prevenção de Problemas Futuros

### 1. Middleware de Tempo
- Usuários visitantes não podem iniciar inscrições com menos de 2 minutos restantes
- Evita inscrições incompletas por timeout

### 2. Validações Robustas
- Verificação de eventos ativos/válidos
- Prevenção de inscrições duplicadas
- Validação de capacidade

### 3. Logs Detalhados
- Todos os erros são logados com contexto
- Facilita debugging em produção

### 4. Testes Automatizados
- Cobertura completa dos cenários de inscrição
- Execução automática em CI/CD

## Troubleshooting

### Problema: Usuário não consegue se inscrever
1. Verificar se o evento está ativo: `is_active = 1`
2. Verificar se não passou da data: `date >= CURDATE()`
3. Verificar capacidade: `capacity IS NULL OR available_spots > 0`
4. Verificar se não há inscrição existente

### Problema: Pagamento não é processado
1. Verificar configuração do Stripe
2. Verificar logs de erro
3. Verificar se session_id é válido
4. Verificar metadata do Stripe

### Problema: Código de ingresso não gerado
1. Executar comando de correção
2. Verificar se status é 'confirmed'
3. Verificar se payment_status é 'completed'

## Configuração do Stripe

Certifique-se de que as seguintes variáveis estão configuradas:
```env
STRIPE_KEY=pk_test_...
STRIPE_SECRET=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

## Suporte

Para problemas não cobertos por este documento:
1. Verificar logs em `storage/logs/laravel.log`
2. Executar comando de diagnóstico: `php artisan events:fix-registrations --dry-run`
3. Verificar configuração do Stripe
4. Contatar suporte técnico com logs específicos
