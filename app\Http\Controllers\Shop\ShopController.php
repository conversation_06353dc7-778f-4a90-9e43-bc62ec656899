<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductDownload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShopController extends Controller
{
    /**
     * Display the main shop page.
     */
    public function index()
    {
        // Lógica para buscar todos os produtos.
        $products = Product::where('is_active', true)->paginate(12);
        return view('shop.index', compact('products'));
    }

    /**
     * Display a single product page.
     */
    public function show(Product $product)
    {
        if (!$product->is_active) {
            abort(404);
        }
        return view('shop.product-show', compact('product'));
    }

    /**
     * Display the user's downloadable products.
     */
    public function downloads()
    {
        $downloads = ProductDownload::where('user_id', Auth::id())
            ->with('product', 'orderItem.order')
            ->whereHas('orderItem.order', function ($query) {
                $query->where('status', '!=', 'pending')
                      ->where('status', '!=', 'failed')
                      ->where('status', '!=', 'cancelled');
            })
            ->latest()
            ->get();
            
        return view('shop.downloads', compact('downloads'));
    }

    /**
     * Handle a file download request.
     */
    public function downloadFile(ProductDownload $productDownload)
    {
        // Verifique se o usuário logado é o dono do download
        if ($productDownload->user_id !== Auth::id()) {
            abort(403, 'Acesso não autorizado.');
        }

        // Verifique se o link de download expirou
        if ($productDownload->hasExpired()) {
            return back()->with('error', 'Este link de download expirou.');
        }

        // Verifique se o limite de downloads foi atingido
        if ($productDownload->hasReachedDownloadLimit()) {
            return back()->with('error', 'Você atingiu o limite de downloads para este produto.');
        }
        
        // Incremente a contagem de downloads
        $productDownload->incrementDownloadCount();

        // Retorne o arquivo para download
        return response()->download(storage_path('app/' . $productDownload->product->digital_file_path));
    }
}
