<?php

namespace App\Notifications;

use App\Models\SupportTicket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewSupportTicketCreated extends Notification
{
    use Queueable;

    protected $ticket;

    public function __construct(SupportTicket $ticket)
    {
        $this->ticket = $ticket;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("Novo Ticket de Suporte #{$this->ticket->ticket_number}")
            ->view('emails.support.new-ticket-created', [
                'ticket' => $this->ticket,
                'user' => $notifiable,
            ]);
    }
}
