<?php

namespace App\Livewire\Support;

use App\Models\SupportTicket;
use App\Models\User;
use App\Notifications\NewSupportTicketCreated;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;

class CreateTicket extends Component
{
    use WithFileUploads;

    public $category = '';
    public $priority = 'media';
    public $title = '';
    public $description = '';
    public $attachments = [];
    public $showModal = false;

    protected $rules = [
        'category' => 'required|string',
        'priority' => 'required|in:baixa,media,alta,urgente',
        'title' => 'required|string|min:5|max:255',
        'description' => 'required|string|min:10',
        'attachments.*' => 'nullable|file|max:10240|mimes:jpg,jpeg,png,pdf,doc,docx,txt',
    ];

    protected $messages = [
        'category.required' => 'Selecione uma categoria.',
        'title.required' => 'O título é obrigatório.',
        'title.min' => 'O título deve ter pelo menos 5 caracteres.',
        'description.required' => 'A descrição é obrigatória.',
        'description.min' => 'A descrição deve ter pelo menos 10 caracteres.',
        'attachments.*.max' => 'Cada arquivo deve ter no máximo 10MB.',
        'attachments.*.mimes' => 'Tipos de arquivo permitidos: JPG, PNG, PDF, DOC, DOCX, TXT.',
    ];

    public function mount($showModal = false)
    {
        $this->showModal = $showModal;
    }

    public function openModal()
    {
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->reset(['category', 'priority', 'title', 'description', 'attachments']);
        $this->resetValidation();
    }

    public function removeAttachment($index)
    {
        unset($this->attachments[$index]);
        $this->attachments = array_values($this->attachments);
    }

    public function createTicket()
    {
        $this->validate();

        try {
            // Upload attachments
            $uploadedFiles = [];
            if (!empty($this->attachments)) {
                foreach ($this->attachments as $file) {
                    $path = $file->store('support-tickets', 'public');
                    $uploadedFiles[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getMimeType(),
                    ];
                }
            }

            // Create ticket
            $ticket = SupportTicket::create([
                'user_id' => auth()->id(),
                'category' => $this->category,
                'priority' => $this->priority,
                'title' => $this->title,
                'description' => $this->description,
                'attachments' => $uploadedFiles,
            ]);

            // Notify user
            auth()->user()->notify(new NewSupportTicketCreated($ticket));

            // Notify admins
            $admins = User::where('role', 'admin')->get();
            foreach ($admins as $admin) {
                $admin->notify(new NewSupportTicketCreated($ticket));
            }

            $this->dispatch('notify', [
                'message' => "Ticket #{$ticket->ticket_number} criado com sucesso!",
                'type' => 'success'
            ]);

            $this->closeModal();

            // Redirect to ticket view
            return redirect()->route('support.tickets.show', $ticket);

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao criar ticket. Tente novamente.',
                'type' => 'error'
            ]);
        }
    }

    public function getCategoriesProperty()
    {
        return [
            'conta' => 'Problemas com Conta',
            'pagamento' => 'Pagamentos e Cobrança',
            'tecnico' => 'Problemas Técnicos',
            'funcionalidade' => 'Dúvidas sobre Funcionalidades',
            'loja' => 'Loja Virtual',
            'grupos' => 'Grupos e Comunidades',
            'mensagens' => 'Sistema de Mensagens',
            'perfil' => 'Perfil e Configurações',
            'outros' => 'Outros Assuntos',
        ];
    }

    public function getPrioritiesProperty()
    {
        return [
            'baixa' => 'Baixa - Dúvida geral',
            'media' => 'Média - Problema que não impede o uso',
            'alta' => 'Alta - Problema que impede funcionalidades',
            'urgente' => 'Urgente - Sistema inacessível',
        ];
    }

    public function render()
    {
        return view('livewire.support.create-ticket');
    }
}
