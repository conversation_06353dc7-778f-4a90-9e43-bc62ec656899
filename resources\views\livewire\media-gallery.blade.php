<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md overflow-hidden">
    {{-- Head<PERSON> da Galeria --}}
    <div class="p-6 border-b border-gray-200 dark:border-zinc-700">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                <flux:icon.photo class="w-6 h-6 mr-2 text-purple-500" />
                Galeria de Mídia
            </h3>
            
            @if(Auth::check() && Auth::id() === $user->id)
                <div class="flex gap-2">
                    <flux:button wire:click="openCreateAlbumModal" variant="ghost" size="sm">
                        <flux:icon.folder-plus class="w-4 h-4 mr-1" />
                        Novo Álbum
                    </flux:button>
                    <flux:button wire:click="openUploadModal" variant="primary" size="sm">
                        <flux:icon.plus class="w-4 h-4 mr-1" />
                        Upload
                    </flux:button>
                </div>
            @endif
        </div>

        {{-- Filtros e Visualização --}}
        <div class="flex flex-wrap items-center justify-between gap-4">
            {{-- Filtros de Tipo --}}
            <div class="flex gap-2">
                <flux:button.group>
                    <flux:button 
                        wire:click="setFilter('all')" 
                        variant="{{ $filterType === 'all' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        Todos
                    </flux:button>
                    <flux:button 
                        wire:click="setFilter('photos')" 
                        variant="{{ $filterType === 'photos' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        <flux:icon.photo class="w-4 h-4 mr-1" />
                        Fotos
                    </flux:button>
                    <flux:button 
                        wire:click="setFilter('videos')" 
                        variant="{{ $filterType === 'videos' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        <flux:icon.video-camera class="w-4 h-4 mr-1" />
                        Vídeos
                    </flux:button>
                </flux:button.group>
            </div>

            {{-- Modos de Visualização --}}
            <div class="flex gap-2">
                <flux:button.group>
                    <flux:button 
                        wire:click="setView('grid')" 
                        variant="{{ $currentView === 'grid' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        <flux:icon.layout-grid class="w-4 h-4" />
                    </flux:button>
                    <flux:button 
                        wire:click="setView('album')" 
                        variant="{{ $currentView === 'album' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        <flux:icon.folder class="w-4 h-4" />
                    </flux:button>
                </flux:button.group>
            </div>
        </div>
    </div>

    {{-- Conteúdo da Galeria --}}
    <div class="p-6">
        @if($currentView === 'album')
            {{-- Visualização por Álbuns --}}
            @if($albums->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($albums as $album)
                        <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                             wire:click="selectAlbum({{ $album->id }})">
                            {{-- Capa do Álbum --}}
                            <div class="aspect-video bg-gray-200 dark:bg-zinc-600 relative overflow-hidden">
                                @if($album->medias->count() > 0)
                                    <div class="grid grid-cols-2 gap-1 h-full">
                                        @foreach($album->medias->take(4) as $index => $media)
                                            <div class="{{ $index === 0 && $album->medias->count() === 1 ? 'col-span-2' : '' }} relative">
                                                @if($media->type === 'photo')
                                                    <img src="{{ $media->thumbnail_url }}" 
                                                         alt="{{ $media->title }}"
                                                         class="w-full h-full object-cover">
                                                @else
                                                    <div class="w-full h-full bg-gray-800 flex items-center justify-center">
                                                        <flux:icon.play class="w-8 h-8 text-white" />
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="w-full h-full flex items-center justify-center">
                                        <flux:icon.photo class="w-12 h-12 text-gray-400" />
                                    </div>
                                @endif
                                
                                {{-- Contador de Mídias --}}
                                <div class="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                    {{ $album->media_count }}
                                </div>
                            </div>
                            
                            {{-- Info do Álbum --}}
                            <div class="p-4">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-1">
                                    {{ $album->name }}
                                </h4>
                                @if($album->description)
                                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                        {{ $album->description }}
                                    </p>
                                @endif
                                <div class="flex items-center justify-between mt-2">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $album->photos_count }} fotos, {{ $album->videos_count }} vídeos
                                    </span>
                                    <flux:badge variant="{{ $album->privacy === 'public' ? 'success' : 'warning' }}" size="sm">
                                        {{ $album->privacy === 'public' ? 'Público' : 'Privado' }}
                                    </flux:badge>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <flux:icon.folder class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Nenhum álbum encontrado
                    </h4>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        {{ Auth::check() && Auth::id() === $user->id ? 'Crie seu primeiro álbum para organizar suas fotos e vídeos.' : 'Este usuário ainda não possui álbuns públicos.' }}
                    </p>
                    @if(Auth::check() && Auth::id() === $user->id)
                        <flux:button wire:click="openCreateAlbumModal" variant="primary">
                            <flux:icon.folder-plus class="w-4 h-4 mr-2" />
                            Criar Primeiro Álbum
                        </flux:button>
                    @endif
                </div>
            @endif
        @else
            {{-- Visualização em Grid --}}
            @if($selectedAlbum)
                <div class="mb-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <flux:button wire:click="selectAlbum(null)" variant="ghost" size="sm" class="mr-3">
                            <flux:icon.arrow-left class="w-4 h-4 mr-1" />
                            Voltar
                        </flux:button>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                            {{ $albums->firstWhere('id', $selectedAlbum)?->name }}
                        </h4>
                    </div>
                </div>
            @endif

            @if($medias->count() > 0)
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    @foreach($medias as $media)
                        <div class="aspect-square bg-gray-200 dark:bg-zinc-600 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group relative"
                             wire:click="showMedia({{ $media->id }})">
                            @if($media->type === 'photo')
                                <img src="{{ $media->thumbnail_url }}" 
                                     alt="{{ $media->title }}"
                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            @else
                                <div class="w-full h-full bg-gray-800 flex items-center justify-center relative">
                                    <img src="{{ $media->thumbnail_url }}" 
                                         alt="{{ $media->title }}"
                                         class="w-full h-full object-cover opacity-70">
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <flux:icon.play class="w-8 h-8 text-white drop-shadow-lg" />
                                    </div>
                                    @if($media->formatted_duration)
                                        <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                            {{ $media->formatted_duration }}
                                        </div>
                                    @endif
                                </div>
                            @endif
                            
                            {{-- Overlay com ações --}}
                            @if(Auth::check() && Auth::id() === $user->id)
                                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                    <flux:button 
                                        wire:click.stop="deleteMedia({{ $media->id }})"
                                        variant="danger" 
                                        size="sm"
                                        onclick="return confirm('Tem certeza que deseja deletar esta mídia?')"
                                    >
                                        <flux:icon.trash class="w-4 h-4" />
                                    </flux:button>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <flux:icon.photo class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Nenhuma mídia encontrada
                    </h4>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        {{ Auth::check() && Auth::id() === $user->id ? 'Faça upload de suas primeiras fotos e vídeos.' : 'Este usuário ainda não possui mídias públicas.' }}
                    </p>
                    @if(Auth::check() && Auth::id() === $user->id)
                        <flux:button wire:click="openUploadModal" variant="primary">
                            <flux:icon.plus class="w-4 h-4 mr-2" />
                            Fazer Upload
                        </flux:button>
                    @endif
                </div>
            @endif
        @endif
    </div>

    {{-- Modal de Visualização de Mídia --}}
    @if($showModal && $selectedMedia)
        <div class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" wire:click="closeModal">
            <div class="max-w-4xl max-h-full p-4" wire:click.stop>
                <div class="bg-white dark:bg-zinc-800 rounded-lg overflow-hidden">
                    {{-- Header do Modal --}}
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-zinc-700">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white">
                                {{ $selectedMedia->title ?: 'Mídia' }}
                            </h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {{ $selectedMedia->album->name }} • {{ $selectedMedia->uploaded_at->format('d/m/Y H:i') }}
                            </p>
                        </div>
                        <flux:button wire:click="closeModal" variant="ghost" size="sm">
                            <flux:icon.x-mark class="w-5 h-5" />
                        </flux:button>
                    </div>
                    
                    {{-- Conteúdo da Mídia --}}
                    <div class="p-4">
                        @if($selectedMedia->type === 'photo')
                            <img src="{{ $selectedMedia->url }}" 
                                 alt="{{ $selectedMedia->title }}"
                                 class="max-w-full max-h-96 mx-auto rounded-lg">
                        @else
                            <video src="{{ $selectedMedia->url }}" 
                                   controls 
                                   class="max-w-full max-h-96 mx-auto rounded-lg"
                                   preload="metadata">
                            </video>
                        @endif
                        
                        @if($selectedMedia->description)
                            <div class="mt-4 p-3 bg-gray-50 dark:bg-zinc-700 rounded-lg">
                                <p class="text-gray-700 dark:text-gray-300">{{ $selectedMedia->description }}</p>
                            </div>
                        @endif
                        
                        {{-- Informações da Mídia --}}
                        <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Tipo:</span>
                                <span class="ml-1 text-gray-900 dark:text-white">
                                    {{ $selectedMedia->type === 'photo' ? 'Foto' : 'Vídeo' }}
                                </span>
                            </div>
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Tamanho:</span>
                                <span class="ml-1 text-gray-900 dark:text-white">{{ $selectedMedia->formatted_size }}</span>
                            </div>
                            @if($selectedMedia->width && $selectedMedia->height)
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Dimensões:</span>
                                    <span class="ml-1 text-gray-900 dark:text-white">{{ $selectedMedia->width }}x{{ $selectedMedia->height }}</span>
                                </div>
                            @endif
                            @if($selectedMedia->formatted_duration)
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">Duração:</span>
                                    <span class="ml-1 text-gray-900 dark:text-white">{{ $selectedMedia->formatted_duration }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Modal de Upload --}}
    @if($showUploadModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" wire:click="closeUploadModal">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 max-w-md w-full mx-4" wire:click.stop>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        Upload de Mídia
                    </h3>
                    <flux:button wire:click="closeUploadModal" variant="ghost" size="sm">
                        <flux:icon.x-mark class="w-5 h-5" />
                    </flux:button>
                </div>

                <form wire:submit.prevent="uploadMedia">
                    <div class="space-y-4">
                        {{-- Seleção de Álbum --}}
                        <div>
                            <flux:field>
                                <flux:label>Álbum</flux:label>
                                <flux:select wire:model="uploadAlbumId">
                                    @foreach($albums as $album)
                                        <option value="{{ $album->id }}">{{ $album->name }}</option>
                                    @endforeach
                                </flux:select>
                            </flux:field>
                        </div>

                        {{-- Upload de Arquivos --}}
                        <div>
                            <flux:field>
                                <flux:label>Arquivos</flux:label>
                                <input type="file"
                                       wire:model="uploadFiles"
                                       multiple
                                       accept="image/*,video/*"
                                       class="w-full p-2 border border-gray-300 dark:border-zinc-600 rounded-lg bg-white dark:bg-zinc-700 text-gray-900 dark:text-white">
                                <flux:description>
                                    Selecione fotos (JPEG, PNG, GIF) ou vídeos (MP4, MOV, AVI). Máximo 20MB por arquivo.
                                </flux:description>
                            </flux:field>
                        </div>

                        {{-- Título --}}
                        <div>
                            <flux:field>
                                <flux:label>Título (opcional)</flux:label>
                                <flux:input wire:model="uploadTitle" placeholder="Digite um título..." />
                            </flux:field>
                        </div>

                        {{-- Descrição --}}
                        <div>
                            <flux:field>
                                <flux:label>Descrição (opcional)</flux:label>
                                <flux:textarea wire:model="uploadDescription" placeholder="Digite uma descrição..." rows="3" />
                            </flux:field>
                        </div>
                    </div>

                    <div class="flex gap-2 mt-6">
                        <flux:button type="submit" variant="primary" class="flex-1">
                            <flux:icon.arrow-up-tray class="w-4 h-4 mr-2" />
                            Fazer Upload
                        </flux:button>
                        <flux:button wire:click="closeUploadModal" variant="ghost">
                            Cancelar
                        </flux:button>
                    </div>
                </form>
            </div>
        </div>
    @endif

    {{-- Modal de Criar Álbum --}}
    @if($showCreateAlbumModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" wire:click="closeCreateAlbumModal">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 max-w-md w-full mx-4" wire:click.stop>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        Criar Novo Álbum
                    </h3>
                    <flux:button wire:click="closeCreateAlbumModal" variant="ghost" size="sm">
                        <flux:icon.x-mark class="w-5 h-5" />
                    </flux:button>
                </div>

                <form wire:submit.prevent="createAlbum">
                    <div class="space-y-4">
                        {{-- Nome do Álbum --}}
                        <div>
                            <flux:field>
                                <flux:label>Nome do Álbum</flux:label>
                                <flux:input wire:model="newAlbumName" placeholder="Ex: Viagem para Paris" required />
                            </flux:field>
                        </div>

                        {{-- Descrição --}}
                        <div>
                            <flux:field>
                                <flux:label>Descrição (opcional)</flux:label>
                                <flux:textarea wire:model="newAlbumDescription" placeholder="Descreva o álbum..." rows="3" />
                            </flux:field>
                        </div>

                        {{-- Privacidade --}}
                        <div>
                            <flux:field>
                                <flux:label>Privacidade</flux:label>
                                <flux:select wire:model="newAlbumPrivacy">
                                    <option value="public">Público - Todos podem ver</option>
                                    <option value="friends">Amigos - Apenas seguidores</option>
                                    <option value="private">Privado - Apenas eu</option>
                                </flux:select>
                            </flux:field>
                        </div>
                    </div>

                    <div class="flex gap-2 mt-6">
                        <flux:button type="submit" variant="primary" class="flex-1">
                            <flux:icon.folder-plus class="w-4 h-4 mr-2" />
                            Criar Álbum
                        </flux:button>
                        <flux:button wire:click="closeCreateAlbumModal" variant="ghost">
                            Cancelar
                        </flux:button>
                    </div>
                </form>
            </div>
        </div>
    @endif
</div>
