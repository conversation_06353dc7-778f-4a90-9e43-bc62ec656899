<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Hashtag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'posts_count'
    ];

    /**
     * Get all of the posts that are assigned this hashtag.
     */
    public function posts()
    {
        return $this->morphedByMany(Post::class, 'taggable', 'taggables')->withTimestamps();
    }

    /**
     * Get all of the comments that are assigned this hashtag.
     */
    public function comments()
    {
        return $this->morphedByMany(Comment::class, 'taggable', 'taggables')->withTimestamps();
    }

    /**
     * Get all of the group posts that are assigned this hashtag.
     * Assuming you have a GroupPost model. If not, this should be adjusted.
     */
    public function groupPosts()
    {
        // Assuming a GroupPost model exists. If your model for posts within groups
        // is just the Post model with a group_id, then this specific relationship
        // might not be needed here, or posts() would cover it if filtered by group_id.
        // For now, let's assume a distinct GroupPost model or a similar concept.
        // If GroupPost model is named differently, please adjust App\Models\GroupPost::class
        return $this->morphedByMany(\App\Models\GroupPost::class, 'taggable', 'taggables')->withTimestamps();
    }

    /**
     * Criar slug automaticamente
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($hashtag) {
            if (empty($hashtag->slug)) {
                $hashtag->slug = Str::slug($hashtag->name);
            }
        });
    }

    /**
     * Buscar ou criar hashtag
     */
    public static function findOrCreateByName($name)
    {
        $cleanName = strtolower(trim($name, '#'));
        
        return static::firstOrCreate(
            ['name' => $cleanName],
            ['slug' => Str::slug($cleanName)]
        );
    }

    /**
     * Incrementar contador de posts
     */
    public function incrementPostsCount()
    {
        $this->increment('posts_count');
    }

    /**
     * Decrementar contador de posts
     */
    public function decrementPostsCount()
    {
        $this->decrement('posts_count');
    }

    /**
     * Hashtags em trending (mais usadas)
     */
    public static function trending($limit = 10)
    {
        return static::where('posts_count', '>', 0)
            ->orderBy('posts_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Buscar hashtags por nome para autocomplete
     */
    public static function searchByName($query, $limit = 10)
    {
        return static::where('name', 'like', $query . '%')
            ->orderBy('posts_count', 'desc')
            ->limit($limit)
            ->get();
    }
}
