<x-layouts.app>
<div class="container mx-auto py-8">
    <h1 class="text-3xl font-bold mb-6">Criar Nova Vaga</h1>

    @if ($errors->any())
        <div class="mb-4 p-4 bg-red-100 text-red-700 rounded">
            <strong>Por favor, corrija os erros abaixo:</strong>
            <ul class="mt-2 list-disc list-inside">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('job_vacancies.store') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="mb-4">
            <label for="title" class="block text-sm font-medium text-gray-700">T<PERSON><PERSON><PERSON> da Vaga</label>
            <input type="text" name="title" id="title" value="{{ old('title') }}" required
                class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:ring-opacity-50 @error('title') border-red-500 @enderror">
        </div>

        <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700">Descrição da Vaga</label>
            <textarea name="description" id="description" rows="4" required
                class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:ring-opacity-50 @error('description') border-red-500 @enderror">{{ old('description') }}</textarea>
        </div>

        <div class="mb-4">
            <label for="category_id" class="block text-sm font-medium text-gray-700">Categoria</label>
            <select name="category_id" id="category_id" required
                class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:ring-opacity-50 @error('category_id') border-red-500 @enderror">
                <option value="">Selecione uma categoria</option>
                @foreach ($categories as $category)
                    <option value="{{ $category->id }}" @if (old('category_id') == $category->id) selected @endif>
                        {{ $category->name }}
                    </option>
                @endforeach
            </select>
        </div>

        <div class="mb-4">
            <label for="company_name" class="block text-sm font-medium text-gray-700">Nome da Empresa</label>
            <input type="text" name="company_name" id="company_name" value="{{ old('company_name') }}" required
                class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:ring-opacity-50 @error('company_name') border-red-500 @enderror">
        </div>

        <div class="mb-4">
            <label for="company_website" class="block text-sm font-medium text-gray-700">Website da Empresa</label>
            <input type="url" name="company_website" id="company_website" value="{{ old('company_website') }}"
                class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:ring-opacity-50 @error('company_website') border-red-500 @enderror">
        </div>

        <div class="mb-4">
            <label for="salary" class="block text-sm font-medium text-gray-700">Salário</label>
            <input type="text" name="salary" id="salary" value="{{ old('salary') }}"
                class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:ring-opacity-50 @error('salary') border-red-500 @enderror">
        </div>

        <div class="mb-4">
            <label for="expiration_date" class="block text-sm font-medium text-gray-700">Data de Expiração</label>
            <input type="date" name="expiration_date" id="expiration_date" value="{{ old('expiration_date') }}"
                class="mt-1 block w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring focus:ring-opacity-50 @error('expiration_date') border-red-500 @enderror">
        </div>

        <div class="mb-4">
            <label for="is_active" class="flex items-center">
                <input type="checkbox" name="is_active" id="is_active" value="1" checked
                    class="form-checkbox h-5 w-5 text-indigo-600 transition duration-150 ease-in-out">
                <span class="ml-2 text-sm text-gray-700">Ativar vaga imediatamente</span>
            </label>
        </div>

        <div class="mb-4">
            <button type="submit"
                class="w-full inline-flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Criar Vaga
            </button>
        </div>
    </form>
</div>
</x-layouts.app>