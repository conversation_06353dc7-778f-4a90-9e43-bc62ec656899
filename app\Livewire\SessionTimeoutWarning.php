<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;

class SessionTimeoutWarning extends Component
{
    public $showWarning = false;
    public $timeRemaining = 0;
    public $timeoutMinutes = 30;
    public $warningThreshold = 5; // Mostra aviso 5 minutos antes

    public function mount()
    {
        $this->timeoutMinutes = config('session.inactivity_timeout', 30);
        $this->checkSessionTimeout();
    }

    // Polling a cada 60 segundos para verificar timeout
    public function getListeners()
    {
        return [
            'echo:user.' . auth()->id() . ',session-timeout' => 'checkSessionTimeout',
        ];
    }

    public function checkSessionTimeout()
    {
        if (!auth()->check()) {
            return;
        }

        $lastActivity = Session::get('last_activity');
        
        if ($lastActivity) {
            $lastActivityTime = Carbon::createFromTimestamp($lastActivity);
            $now = Carbon::now();
            $inactiveMinutes = $now->diffInMinutes($lastActivityTime);
            
            // Calcula tempo restante
            $remainingMinutes = $this->timeoutMinutes - $inactiveMinutes;
            
            // Mostra aviso se está próximo do timeout
            if ($remainingMinutes <= $this->warningThreshold && $remainingMinutes > 0) {
                $this->showWarning = true;
                $this->timeRemaining = $remainingMinutes;
            } else {
                $this->showWarning = false;
            }
        }
    }

    public function extendSession()
    {
        // Atualiza o timestamp da última atividade
        Session::put('last_activity', time());
        $this->showWarning = false;
        
        // Mostra mensagem de sucesso
        $this->dispatch('toast', [
            'message' => 'Sessão estendida com sucesso!',
            'type' => 'success',
            'timeout' => 3000
        ]);
    }

    public function logout()
    {
        auth()->logout();
        Session::flush();
        
        return redirect()->route('login')
            ->with('info', 'Você foi desconectado por escolha própria.');
    }

    public function render()
    {
        return view('livewire.session-timeout-warning');
    }
} 