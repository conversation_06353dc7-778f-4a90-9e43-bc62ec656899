<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class JobApplicationRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return $next($request);
        }

        $userId = Auth::id();
        $userRole = Auth::user()->role;
        
        // Definir limites baseados no tipo de usuário
        $limits = $this->getRateLimits($userRole);
        
        // Verificar limite por hora
        $hourlyKey = "job_applications_hourly_{$userId}";
        $hourlyCount = Cache::get($hourlyKey, 0);
        
        if ($hourlyCount >= $limits['hourly']) {
            return response()->json([
                'error' => 'Limite de candidaturas por hora excedido. Tente novamente mais tarde.'
            ], 429);
        }
        
        // Verificar limite diário
        $dailyKey = "job_applications_daily_{$userId}";
        $dailyCount = Cache::get($dailyKey, 0);
        
        if ($dailyCount >= $limits['daily']) {
            return response()->json([
                'error' => 'Limite de candidaturas diárias excedido. Tente novamente amanhã.'
            ], 429);
        }
        
        // Incrementar contadores após o processamento bem-sucedido
        $response = $next($request);
        
        if ($response->getStatusCode() === 200) {
            Cache::put($hourlyKey, $hourlyCount + 1, now()->addHour());
            Cache::put($dailyKey, $dailyCount + 1, now()->addDay());
        }
        
        return $response;
    }
    
    /**
     * Obter limites de rate limiting baseados no tipo de usuário
     */
    private function getRateLimits($userRole): array
    {
        return match ($userRole) {
            'vip' => [
                'hourly' => 10,
                'daily' => 50,
            ],
            'administrador' => [
                'hourly' => 100,
                'daily' => 1000,
            ],
            default => [
                'hourly' => 5,
                'daily' => 20,
            ],
        };
    }
}
