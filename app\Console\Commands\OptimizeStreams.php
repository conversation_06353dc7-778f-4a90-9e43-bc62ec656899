<?php

namespace App\Console\Commands;

use App\Services\StreamOptimizationService;
use App\Models\LiveStream;
use Illuminate\Console\Command;

class OptimizeStreams extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'streams:optimize {--cache : Apenas limpar cache} {--videos : Apenas otimizar vídeos} {--database : Apenas otimizar banco}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Otimizar sistema de streaming - cache, vídeos e banco de dados';

    protected $optimizationService;

    public function __construct(StreamOptimizationService $optimizationService)
    {
        parent::__construct();
        $this->optimizationService = $optimizationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Iniciando otimização do sistema de streaming...');

        // Mostrar métricas iniciais
        $this->showPerformanceMetrics('Métricas Iniciais');

        $cacheOnly = $this->option('cache');
        $videosOnly = $this->option('videos');
        $databaseOnly = $this->option('database');

        // Se nenhuma opção específica, executar tudo
        $runAll = !$cacheOnly && !$videosOnly && !$databaseOnly;

        if ($cacheOnly || $runAll) {
            $this->optimizeCache();
        }

        if ($videosOnly || $runAll) {
            $this->optimizeVideos();
        }

        if ($databaseOnly || $runAll) {
            $this->optimizeDatabase();
        }

        // Mostrar métricas finais
        $this->showPerformanceMetrics('Métricas Finais');

        $this->info('✅ Otimização concluída com sucesso!');
    }

    /**
     * Otimizar cache
     */
    private function optimizeCache()
    {
        $this->info('🗄️  Otimizando cache...');

        $this->optimizationService->clearStreamCache();
        $this->line('   ✓ Cache limpo');

        $activeStreams = $this->optimizationService->cacheActiveStreams();
        $this->line("   ✓ {$activeStreams->count()} streams ativos cacheados");

        $popularReplays = $this->optimizationService->cachePopularReplays();
        $this->line("   ✓ {$popularReplays->count()} replays populares cacheados");
    }

    /**
     * Otimizar vídeos
     */
    private function optimizeVideos()
    {
        $this->info('🎬 Otimizando vídeos...');

        $unoptimizedStreams = LiveStream::where('has_replay', true)
            ->whereNotNull('video_path')
            ->whereNull('optimized_paths')
            ->limit(10) // Processar apenas 10 por vez para evitar sobrecarga
            ->get();

        if ($unoptimizedStreams->isEmpty()) {
            $this->line('   ✓ Todos os vídeos já estão otimizados');
            return;
        }

        $progressBar = $this->output->createProgressBar($unoptimizedStreams->count());
        $progressBar->start();

        foreach ($unoptimizedStreams as $stream) {
            $result = $this->optimizationService->optimizeVideo($stream->video_path, $stream->id);

            if ($result) {
                $this->line("\n   ✓ Vídeo otimizado: {$stream->title}");
            } else {
                $this->line("\n   ✗ Falha ao otimizar: {$stream->title}");
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->line("\n   ✓ Otimização de vídeos concluída");
    }

    /**
     * Otimizar banco de dados
     */
    private function optimizeDatabase()
    {
        $this->info('🗃️  Otimizando banco de dados...');

        $result = $this->optimizationService->optimizeDatabase();

        if ($result) {
            $this->line('   ✓ Banco de dados otimizado');
        } else {
            $this->line('   ✗ Erro ao otimizar banco de dados');
        }
    }

    /**
     * Mostrar métricas de performance
     */
    private function showPerformanceMetrics($title)
    {
        $metrics = $this->optimizationService->getPerformanceMetrics();

        $this->info("\n📊 {$title}:");
        $this->table(
            ['Métrica', 'Valor'],
            [
                ['Streams Ativos', $metrics['active_streams_count']],
                ['Total de Replays', number_format($metrics['total_replays'])],
                ['Uso de Memória', $this->formatBytes($metrics['memory_usage'])],
                ['Pico de Memória', $this->formatBytes($metrics['peak_memory'])],
                ['Espaço em Disco', $this->formatBytes($metrics['disk_space'])],
                ['Tamanho do BD', $this->formatBytes($metrics['database_size'])],
            ]
        );
    }

    /**
     * Formatar bytes em formato legível
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
