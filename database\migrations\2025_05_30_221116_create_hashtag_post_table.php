<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the old table if it exists to avoid conflicts with the new structure
        Schema::dropIfExists('hashtag_post');

        if (!Schema::hasTable('taggables')) {
            Schema::create('taggables', function (Blueprint $table) {
                $table->unsignedBigInteger('hashtag_id');
                $table->morphs('taggable'); // This will create taggable_id and taggable_type

                $table->foreign('hashtag_id')->references('id')->on('hashtags')->onDelete('cascade');

                // It's common to have a primary key for the pivot table itself,
                // or a unique constraint on hashtag_id, taggable_id, and taggable_type
                // to prevent duplicate entries.
                $table->primary(['hashtag_id', 'taggable_id', 'taggable_type'], 'taggables_primary_key');
                // Add an index for faster lookups on the polymorphic columns
                $table->index(['taggable_id', 'taggable_type'], 'taggables_taggable_index');
                
                // Timestamps can be useful if you want to know when a tag was applied
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('taggables');
        // If you want to recreate the old hashtag_post table on rollback (not typical for this kind of change):
        /*
        if (!Schema::hasTable('hashtag_post')) {
            Schema::create('hashtag_post', function (Blueprint $table) {
                $table->unsignedBigInteger('hashtag_id');
                $table->unsignedBigInteger('post_id');
                $table->timestamps();
                $table->foreign('hashtag_id')->references('id')->on('hashtags')->onDelete('cascade');
                $table->foreign('post_id')->references('id')->on('posts')->onDelete('cascade');
                $table->primary(['hashtag_id', 'post_id']);
            });
        }
        */
    }
};
