<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\UserPhoto;
use Illuminate\Support\Facades\Storage;

class Leaderboard extends Component
{
    public $topUsers;

    public function mount()
    {
        // Buscar os 10 usuários com mais pontos de ranking (apenas com pontuação > 0)
        $this->topUsers = User::where('ranking_points', '>', 0)
            ->orderBy('ranking_points', 'desc')
            ->take(10)
            ->get();

        // Adicionar avatares aos usuários do ranking
        $this->topUsers->each(function ($user) {
            $user->avatar = $this->getAvatar($user->id);
        });
    }

    public function getAvatar($userId)
    {
        // Buscar a foto atual (is_current = true) primeiro, depois a mais recente
        $path = UserPhoto::where('user_id', $userId)
            ->where('is_current', true)
            ->value('photo_path');

        if (!$path) {
            $path = UserPhoto::where('user_id', $userId)
                ->latest()
                ->value('photo_path');
        }

        return $path ? Storage::url($path) : asset('images/users/avatar.svg');
    }

    public function render()
    {
        return view('livewire.leaderboard');
    }
}
