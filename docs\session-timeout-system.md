# Sistema de Timeout de Sessão

## Visão Geral

O sistema de timeout de sessão foi implementado para garantir que usuários sejam automaticamente deslogados após um período de inatividade, melhorando a segurança da aplicação.

## Componentes Implementados

### 1. Middleware SessionTimeout
**Arquivo:** `app/Http/Middleware/SessionTimeout.php`

- Monitora a atividade do usuário
- Faz logout automático após período de inatividade
- Registra logs de logout por inatividade
- Redireciona para página de login com mensagem

### 2. Componente SessionTimeoutWarning
**Arquivo:** `app/Livewire/SessionTimeoutWarning.php`

- Mostra aviso visual antes do timeout
- Permite estender a sessão
- Permite logout manual
- Polling automático a cada 60 segundos

### 3. Comando de Limpeza
**Arquivo:** `app/Console/Commands/CleanExpiredSessions.php`

- Remove sessões expiradas do banco
- Comando: `php artisan sessions:clean --days=7`

## Configuração

### Variáveis de Ambiente (.env)

```env
# Tempo limite de inatividade em minutos (padrão: 30)
SESSION_INACTIVITY_TIMEOUT=30

# Tempo de vida da sessão em minutos (padrão: 120)
SESSION_LIFETIME=120

# Sessão expira ao fechar navegador (padrão: false)
SESSION_EXPIRE_ON_CLOSE=false
```

### Configuração no config/session.php

```php
'inactivity_timeout' => (int) env('SESSION_INACTIVITY_TIMEOUT', 30),
```

## Como Funciona

### 1. Detecção de Atividade
- O middleware `SessionTimeout` é executado em todas as requisições
- Atualiza o timestamp `last_activity` na sessão
- Verifica se o tempo de inatividade excedeu o limite

### 2. Aviso de Timeout
- 5 minutos antes do timeout, o componente `SessionTimeoutWarning` mostra um aviso
- Usuário pode escolher:
  - **Manter conectado**: Estende a sessão
  - **Sair agora**: Faz logout imediato
  - **Fechar**: Ignora o aviso

### 3. Logout Automático
- Se o usuário não interagir, é automaticamente deslogado
- Sessão é limpa
- Redirecionamento para página de login com mensagem

## Personalização

### Alterar Tempo de Timeout

1. **Via .env:**
```env
SESSION_INACTIVITY_TIMEOUT=60  # 60 minutos
```

2. **Via código:**
```php
// No middleware SessionTimeout.php
$timeoutMinutes = config('session.inactivity_timeout', 30);
```

### Alterar Tempo de Aviso

```php
// No componente SessionTimeoutWarning.php
public $warningThreshold = 10; // 10 minutos antes
```

### Alterar Frequência de Verificação

```php
// No componente SessionTimeoutWarning.php
<div wire:poll.30s="checkSessionTimeout"> // 30 segundos
```

## Manutenção

### Limpeza Automática de Sessões

Adicione ao cron do servidor:

```bash
# Limpar sessões expiradas diariamente às 2h da manhã
0 2 * * * cd /path/to/project && php artisan sessions:clean --days=7
```

### Monitoramento

Verifique os logs para logout por inatividade:

```bash
tail -f storage/logs/laravel.log | grep "Usuário deslogado por inatividade"
```

## Segurança

### Benefícios
- Previne acesso não autorizado a contas esquecidas
- Reduz risco de ataques em sessões abandonadas
- Melhora conformidade com políticas de segurança

### Considerações
- Usuários podem perder trabalho não salvo
- Pode ser inconveniente para usuários legítimos
- Recomenda-se configurar tempo adequado para o tipo de aplicação

## Troubleshooting

### Problema: Usuário sendo deslogado muito rapidamente
**Solução:** Verificar se o middleware está sendo executado corretamente e se o timestamp está sendo atualizado.

### Problema: Aviso não aparece
**Solução:** Verificar se o componente `SessionTimeoutWarning` está incluído no layout e se o polling está funcionando.

### Problema: Sessões não são limpas
**Solução:** Verificar se o comando `sessions:clean` está sendo executado e se há permissões adequadas no banco de dados.

## Logs

O sistema registra os seguintes eventos:

```php
// Logout por inatividade
\Log::info('Usuário deslogado por inatividade', [
    'user_id' => $user->id,
    'email' => $user->email,
    'inactive_minutes' => $inactiveMinutes,
    'timeout_minutes' => $timeoutMinutes
]);
```

## Testes

Para testar o sistema:

1. Faça login na aplicação
2. Deixe a página aberta sem interação
3. Aguarde o aviso aparecer (5 minutos antes do timeout)
4. Teste as opções de "Manter conectado" e "Sair agora"
5. Verifique se o logout automático funciona após o timeout 