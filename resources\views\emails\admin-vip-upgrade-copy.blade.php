<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[ADMIN COPY] Usuário Promovido a VIP - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #7c3aed, #1f2937);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .admin-badge {
            display: inline-block;
            background-color: #dc2626;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px 20px;
        }
        .user-info {
            background-color: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .user-info h3 {
            margin-top: 0;
            color: #374151;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #6b7280;
        }
        .info-value {
            color: #374151;
        }
        .vip-badge {
            display: inline-block;
            background: linear-gradient(135deg, #FFE600, #E60073);
            color: #000;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 12px;
        }
        .action-button {
            display: inline-block;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            margin: 10px 5px;
            text-align: center;
        }
        .footer {
            background-color: #1f2937;
            color: #9ca3af;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        }
        .footer a {
            color: #60a5fa;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 4px;
            }
            .header, .content {
                padding: 20px 15px;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                margin-bottom: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="admin-badge">ADMIN COPY</div>
            <h1>Usuário Promovido a VIP</h1>
            <p style="margin: 10px 0 0 0;">Notificação Administrativa</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Olá <strong>{{ $adminRecipient->name ?? 'Administrador' }}</strong>,</p>
            
            <p>Um usuário foi promovido de <strong>Visitante</strong> para <strong>VIP</strong> na plataforma.</p>

            <!-- Informações do Usuário -->
            <div class="user-info">
                <h3>📋 Informações do Usuário Promovido</h3>
                
                <div class="info-row">
                    <span class="info-label">Nome:</span>
                    <span class="info-value">{{ $upgradedUser->name }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ $upgradedUser->email }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Username:</span>
                    <span class="info-value">{{ $upgradedUser->username }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">ID do Usuário:</span>
                    <span class="info-value">#{{ $upgradedUser->id }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Novo Status:</span>
                    <span class="info-value"><span class="vip-badge">VIP</span></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Data da Promoção:</span>
                    <span class="info-value">{{ now()->format('d/m/Y H:i:s') }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Cadastrado em:</span>
                    <span class="info-value">{{ $upgradedUser->created_at->format('d/m/Y H:i:s') }}</span>
                </div>
            </div>

            <p><strong>Ações realizadas automaticamente:</strong></p>
            <ul>
                <li>✅ Email de boas-vindas VIP enviado para o usuário</li>
                <li>✅ Status do usuário atualizado no sistema</li>
                <li>✅ Benefícios VIP ativados</li>
                <li>✅ Notificação enviada para administradores</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ url('/' . $upgradedUser->username) }}" class="action-button">
                    Ver Perfil do Usuário
                </a>
                <a href="{{ url('/admin/users') }}" class="action-button">
                    Gerenciar Usuários
                </a>
            </div>

            <p style="margin-top: 30px; font-size: 14px; color: #6b7280;">
                Esta é uma notificação automática do sistema de gerenciamento de usuários.
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ config('app.name') }} - Painel Administrativo</strong></p>
            <p>
                <a href="{{ url('/admin') }}">Painel Admin</a> | 
                <a href="mailto:<EMAIL>">Suporte Técnico</a>
            </p>
            <p style="margin-top: 15px; font-size: 12px;">
                Este email foi enviado automaticamente. Não responda diretamente a esta mensagem.
            </p>
        </div>
    </div>
</body>
</html>
