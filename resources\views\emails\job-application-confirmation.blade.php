<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Candidatura Confirmada - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.875rem;
            font-weight: 700;
        }
        .vip-badge {
            background: linear-gradient(135deg, #FFE600 0%, #FFA500 100%);
            color: #000;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            display: inline-block;
            margin-left: 0.5rem;
        }
        .content {
            padding: 2rem;
        }
        .success-icon {
            text-align: center;
            font-size: 4rem;
            margin: 1rem 0;
        }
        .job-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .info-box {
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            padding: 1rem;
            margin: 1.5rem 0;
            border-radius: 0 6px 6px 0;
        }
        .vip-info-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #FFE600;
            padding: 1rem;
            margin: 1.5rem 0;
            border-radius: 0 6px 6px 0;
        }
        .next-steps {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        .step:last-child {
            margin-bottom: 0;
        }
        .step-number {
            background: linear-gradient(135deg, #E60073 0%, #00FFF7 100%);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.875rem;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #E60073 0%, #00FFF7 100%);
            color: white;
            text-decoration: none;
            padding: 0.875rem 2rem;
            border-radius: 6px;
            font-weight: 600;
            font-size: 1rem;
            margin: 1rem 0;
            transition: transform 0.2s;
        }
        .action-button:hover {
            transform: translateY(-1px);
        }
        .footer {
            background-color: #f9fafb;
            padding: 1.5rem;
            text-align: center;
            font-size: 0.875rem;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
        }
        .footer a {
            color: #E60073;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            .content {
                padding: 1.5rem;
            }
            .header {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">
                Candidatura Confirmada
                @if($application->is_vip_priority)
                    <span class="vip-badge">VIP</span>
                @endif
            </p>
        </div>
        
        <div class="content">
            <div class="success-icon">✅</div>
            
            <p style="font-size: 1.125rem; margin-bottom: 1.5rem; color: #1f2937; text-align: center;">
                Olá <strong>{{ $user->name }}</strong>,
            </p>
            
            <p style="text-align: center; font-size: 1.1rem;">
                Sua candidatura foi <strong style="color: #10b981;">enviada com sucesso</strong>!
            </p>

            <div class="job-summary">
                <h3 style="margin: 0 0 1rem 0; font-size: 1.25rem;">📋 Resumo da Candidatura</h3>
                <p style="margin: 0.5rem 0; color: rgba(255,255,255,0.9);">
                    <strong>Vaga:</strong> {{ $job->title }}
                </p>
                <p style="margin: 0.5rem 0; color: rgba(255,255,255,0.9);">
                    <strong>Categoria:</strong> {{ $job->category->name }}
                </p>
                <p style="margin: 0.5rem 0; color: rgba(255,255,255,0.9);">
                    <strong>Modalidade:</strong> {{ $job->work_mode }}
                </p>
                <p style="margin: 0.5rem 0; color: rgba(255,255,255,0.9);">
                    <strong>Data da Candidatura:</strong> {{ $application->created_at->format('d/m/Y H:i') }}
                </p>
                <p style="margin: 0.5rem 0; color: rgba(255,255,255,0.9);">
                    <strong>Status:</strong> {{ $application->status }}
                </p>
            </div>

            @if($application->is_vip_priority)
            <div class="vip-info-box">
                <p style="margin: 0; font-weight: 600; color: #92400e;">⭐ Candidatura VIP</p>
                <p style="margin: 0.5rem 0 0 0; color: #92400e;">
                    Sua candidatura VIP terá prioridade na análise dos recrutadores! Você será um dos primeiros candidatos a serem avaliados.
                </p>
            </div>
            @endif

            <div class="info-box">
                <p style="margin: 0; font-weight: 600; color: #0369a1;">📧 Confirmação Enviada</p>
                <p style="margin: 0.5rem 0 0 0; color: #0369a1;">
                    Uma cópia desta confirmação foi enviada para seu email. Nossa equipe de recrutamento foi notificada sobre sua candidatura.
                </p>
            </div>

            <div class="next-steps">
                <h4 style="margin: 0 0 1rem 0; color: #1f2937;">🚀 Próximos Passos</h4>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Análise Inicial</strong><br>
                        Nossa equipe analisará seu currículo e carta de apresentação nos próximos dias úteis.
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Contato</strong><br>
                        Se seu perfil for compatível com a vaga, entraremos em contato via email ou telefone.
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Processo Seletivo</strong><br>
                        Caso selecionado, você participará das próximas etapas do processo seletivo.
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin: 2rem 0;">
                <a href="{{ route('job_vacancies.show', $job->slug) }}" class="action-button">
                    Ver Detalhes da Vaga
                </a>
            </div>

            <p style="font-size: 0.875rem; color: #6b7280; text-align: center;">
                <strong>Dica:</strong> Mantenha seu perfil atualizado e fique atento ao seu email para não perder nenhuma comunicação importante.
            </p>
        </div>
        
        <div class="footer">
            <p>
                Obrigado por escolher o {{ config('app.name') }} para sua busca profissional!
            </p>
            <p>
                © {{ date('Y') }} {{ config('app.name') }}. Todos os direitos reservados.<br>
                <a href="{{ config('app.url') }}">{{ config('app.url') }}</a>
            </p>
        </div>
    </div>
</body>
</html>
