<div class="min-h-screen bg-zinc-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-300 mb-2">Replays de Lives</h1>
            <p class="text-gray-400">Assista novamente às lives que você perdeu</p>
            <p class="text-sm text-gray-500">Total de replays: {{ $replays->total() }}</p>
        </div>

        <!-- Debug Info -->
        <div class="mb-4 p-4 bg-zinc-800 rounded">
            <p class="text-sm">Debug: {{ $replays->count() }} replays carregados</p>
            <p class="text-sm">Busca: "{{ $search }}"</p>
            <p class="text-sm">Página atual: {{ $replays->currentPage() }}</p>
        </div>

        <!-- Busca -->
        <div class="mb-8">
            <div class="max-w-md">
                <input
                    wire:model.live="search"
                    placeholder="Buscar por título ou streamer..."
                    class="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded text-white"
                />
            </div>
        </div>

        <!-- Grid de Replays -->
        @if($replays->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                @foreach($replays as $replay)
                    <div class="bg-zinc-800 rounded-lg p-4">
                        <h3 class="font-bold text-gray-300 mb-2">{{ $replay->title }}</h3>
                        <p class="text-sm text-gray-400 mb-2">Por: {{ $replay->user->name }}</p>
                        <p class="text-xs text-gray-500">
                            {{ $replay->viewers_count }} visualizações •
                            {{ $replay->ended_at?->diffForHumans() }}
                        </p>
                        @if($replay->duration)
                            <p class="text-xs text-gray-500">Duração: {{ $replay->getFormattedDuration() }}</p>
                        @endif
                        <a href="{{ route('live-streams.view', $replay->id) }}"
                           class="inline-block mt-2 px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700">
                            Assistir Replay
                        </a>
                    </div>
                @endforeach
            </div>

            <!-- Paginação -->
            <div class="flex justify-center">
                {{ $replays->links() }}
            </div>
        @else
            <!-- Estado Vazio -->
            <div class="text-center py-16">
                <x-flux::icon name="video-camera-slash" class="w-16 h-16 mx-auto mb-4 text-gray-500" />
                <h3 class="text-xl font-bold text-gray-400 mb-2">Nenhum replay encontrado</h3>
                <p class="text-gray-500 mb-6">
                    @if($search)
                        Não encontramos replays com o termo "{{ $search }}"
                    @else
                        Ainda não há replays de lives disponíveis
                    @endif
                </p>
                @if($search)
                    <flux:button wire:click="$set('search', '')" variant="outline">
                        Limpar busca
                    </flux:button>
                @endif
            </div>
        @endif
    </div>
</div>
