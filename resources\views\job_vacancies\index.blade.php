<x-layouts.app :title="'Trabalhe Conosco'">
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <flux:icon icon="briefcase" class="w-8 h-8 text-primary" />
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Trabalhe Conosco</h1>
            </div>
            <p class="text-gray-600 dark:text-gray-400 text-lg">
                Encontre oportunidades incríveis e faça parte da nossa equipe
            </p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <flux:icon icon="briefcase" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $job_vacancies->total() }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Vagas Disponíveis</div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <flux:icon icon="star" class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $job_vacancies->where('is_featured', true)->count() }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Em Destaque</div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                        <flux:icon icon="tag" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $categories->count() }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Categorias</div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <flux:icon icon="clock" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $job_vacancies->where('created_at', '>=', now()->subDays(7))->count() }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Novas esta Semana</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-8">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filtrar Vagas</h3>
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <flux:field>
                            <flux:label>Categoria</flux:label>
                            <flux:select name="category" onchange="this.form.submit()">
                                <option value="">Todas as áreas</option>
                                @foreach($categories as $cat)
                                    <option value="{{$cat->id}}" @selected(request('category')==$cat->id)>{{$cat->name}}</option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Tipo de Contrato</flux:label>
                            <flux:select name="contract_type" onchange="this.form.submit()">
                                <option value="">Todos os tipos</option>
                                <option value="CLT" @selected(request('contract_type')=='CLT')>CLT</option>
                                <option value="PJ" @selected(request('contract_type')=='PJ')>PJ</option>
                                <option value="Freelance" @selected(request('contract_type')=='Freelance')>Freelance</option>
                                <option value="Estágio" @selected(request('contract_type')=='Estágio')>Estágio</option>
                                <option value="Temporário" @selected(request('contract_type')=='Temporário')>Temporário</option>
                            </flux:select>
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Modalidade</flux:label>
                            <flux:select name="work_mode" onchange="this.form.submit()">
                                <option value="">Todas as modalidades</option>
                                <option value="Presencial" @selected(request('work_mode')=='Presencial')>Presencial</option>
                                <option value="Remoto" @selected(request('work_mode')=='Remoto')>Remoto</option>
                                <option value="Híbrido" @selected(request('work_mode')=='Híbrido')>Híbrido</option>
                            </flux:select>
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Nível de Experiência</flux:label>
                            <flux:select name="experience_level" onchange="this.form.submit()">
                                <option value="">Todos os níveis</option>
                                <option value="Júnior" @selected(request('experience_level')=='Júnior')>Júnior</option>
                                <option value="Pleno" @selected(request('experience_level')=='Pleno')>Pleno</option>
                                <option value="Sênior" @selected(request('experience_level')=='Sênior')>Sênior</option>
                                <option value="Especialista" @selected(request('experience_level')=='Especialista')>Especialista</option>
                            </flux:select>
                        </flux:field>
                    </div>
                </form>

                @if(request()->hasAny(['category', 'contract_type', 'work_mode', 'experience_level']))
                    <div class="mt-4 flex items-center gap-2 flex-wrap">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Filtros ativos:</span>
                        @if(request('category'))
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                {{ $categories->find(request('category'))->name ?? 'Categoria' }}
                                <a href="{{ request()->fullUrlWithQuery(['category' => null]) }}" class="ml-1 text-blue-600 hover:text-blue-800">
                                    <flux:icon icon="x-mark" class="w-3 h-3" />
                                </a>
                            </span>
                        @endif
                        @if(request('contract_type'))
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                {{ request('contract_type') }}
                                <a href="{{ request()->fullUrlWithQuery(['contract_type' => null]) }}" class="ml-1 text-green-600 hover:text-green-800">
                                    <flux:icon icon="x-mark" class="w-3 h-3" />
                                </a>
                            </span>
                        @endif
                        @if(request('work_mode'))
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                {{ request('work_mode') }}
                                <a href="{{ request()->fullUrlWithQuery(['work_mode' => null]) }}" class="ml-1 text-purple-600 hover:text-purple-800">
                                    <flux:icon icon="x-mark" class="w-3 h-3" />
                                </a>
                            </span>
                        @endif
                        @if(request('experience_level'))
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                {{ request('experience_level') }}
                                <a href="{{ request()->fullUrlWithQuery(['experience_level' => null]) }}" class="ml-1 text-yellow-600 hover:text-yellow-800">
                                    <flux:icon icon="x-mark" class="w-3 h-3" />
                                </a>
                            </span>
                        @endif
                        <a href="{{ route('job_vacancies.index') }}" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Limpar todos
                        </a>
                    </div>
                @endif
            </div>
        </div>
        <!-- Job Listings -->
        @if($job_vacancies->count())
            <div class="space-y-6">
                @include('job_vacancies.partials.job-cards')
            </div>

            <!-- Pagination -->
            @if($job_vacancies->hasPages())
                <div class="mt-8 flex justify-center">
                    {{ $job_vacancies->withQueryString()->links() }}
                </div>
            @endif
        @else
            <!-- Empty State -->
            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-12 text-center">
                <div class="flex flex-col items-center">
                    <flux:icon icon="briefcase" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Nenhuma vaga encontrada</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
                        Não encontramos vagas que correspondam aos seus critérios de busca. Tente ajustar os filtros ou volte mais tarde para ver novas oportunidades.
                    </p>
                    <flux:button href="{{ route('job_vacancies.index') }}" variant="primary">
                        <flux:icon icon="arrow-path" class="w-4 h-4 mr-2" />
                        Ver Todas as Vagas
                    </flux:button>
                </div>
            </div>
        @endif

        <!-- VIP Info Banner -->
        <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-700 p-6">
            <div class="flex items-start gap-4">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <flux:icon icon="star" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Usuários VIP têm prioridade nas candidaturas!</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Qualquer pessoa pode se candidatar às nossas vagas, mas membros VIP têm análise prioritária e mais chances de destaque no processo seletivo.
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                            <flux:icon icon="check" class="w-4 h-4 mr-1" />
                            Análise prioritária
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                            <flux:icon icon="check" class="w-4 h-4 mr-1" />
                            Destaque no processo
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <flux:icon icon="check" class="w-4 h-4 mr-1" />
                            Feedback mais rápido
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>